import React from 'react';
import { Typography } from '@/shared/components/common';
import CrawGroupMembersForm from './CrawGroupMembersForm';

/**
 * Demo component để test CrawGroupMembersForm
 */
const CrawGroupMembersFormDemo: React.FC = () => {
  const handleSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    alert(`Tạo chiến dịch thành công!\nTên: ${data.campaignName}\nTài khoản: ${data.accountIds.length} tài khoản\nNhóm: ${data.groupIds.length} nhóm`);
  };

  const handleCancel = () => {
    console.log('Form cancelled');
    alert('Đã hủy tạo chiến dịch');
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <div className="mb-6">
        <Typography variant="h4" className="font-bold mb-2">
          Demo: Craw Group Members Form
        </Typography>
        <Typography variant="body1" className="text-muted">
          Form tạo chiến dịch craw nhóm Zalo
        </Typography>
      </div>

      <div className="max-w-2xl">
        <CrawGroupMembersForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </div>
    </div>
  );
};

export default CrawGroupMembersFormDemo;
