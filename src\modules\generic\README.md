# Generic Page Module

Trang `/generic` là một trang hiển thị widgets ở chế độ siêu tối giản với WebSocket real-time sync, cho phép backend điều khiển việc thêm/xóa widgets.

## 🎯 Tính năng

### ✅ **Đã hoàn thành**
- ✅ Hiển thị widgets ở chế độ ultra-minimal (không có header, border, controls)
- ✅ WebSocket real-time communication với backend
- ✅ Session-based widget management
- ✅ Auto-reconnect WebSocket
- ✅ Responsive layout với react-grid-layout
- ✅ Debug mode với keyboard shortcuts
- ✅ Empty state khi không có widgets
- ✅ Connection status indicator

### 🔄 **Đang phát triển**
- 🔄 Backend API implementation
- 🔄 Widget persistence
- 🔄 Advanced layout management
- 🔄 Widget animation transitions

## 🚀 Cách sử dụng

### **Frontend (Đã sẵn sàng)**

1. **T<PERSON>y cập trang:**
   ```
   http://localhost:5174/generic
   ```

2. **URL với session ID:**
   ```
   http://localhost:5174/generic?sessionId=your-session-id
   ```

3. **Keyboard shortcuts (Development mode):**
   - `Ctrl+R`: Request state sync
   - `Ctrl+I`: Show page info

### **Backend Integration (Cần implement)**

#### **WebSocket Endpoint**
```
ws://localhost:8080/ws/generic?sessionId=<session-id>
```

#### **API Endpoints**
```typescript
// Lấy state hiện tại
GET /api/generic/state?sessionId=<session-id>

// Tạo session mới
POST /api/generic/sessions

// Tạo widget
POST /api/generic/widgets
{
  "sessionId": "string",
  "widgetType": "string",
  "title": "string",
  "position": { "x": 0, "y": 0 },
  "size": { "w": 4, "h": 3 },
  "props": {}
}

// Xóa widget
DELETE /api/generic/widgets/:widgetId
{
  "sessionId": "string"
}

// Cập nhật layout
PUT /api/generic/layout
{
  "sessionId": "string",
  "layout": [...]
}

// Sync state
POST /api/generic/sync
{
  "sessionId": "string",
  "widgets": [...],
  "layout": [...]
}
```

#### **WebSocket Events**

**Frontend → Backend:**
```typescript
// Thêm widget
{
  "type": "add_widget",
  "payload": {
    "widget": { ... },
    "position": { "x": 0, "y": 0 }
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}

// Xóa widget
{
  "type": "remove_widget",
  "payload": {
    "widgetId": "string"
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}

// Cập nhật layout
{
  "type": "update_layout",
  "payload": {
    "layout": [...]
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}

// Request sync
{
  "type": "sync_state",
  "payload": {},
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Backend → Frontend:**
```typescript
// Widget được thêm
{
  "type": "add_widget",
  "payload": {
    "widget": { ... }
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}

// Widget được xóa
{
  "type": "remove_widget",
  "payload": {
    "widgetId": "string"
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}

// Layout được cập nhật
{
  "type": "update_layout",
  "payload": {
    "layout": [...]
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}

// State sync
{
  "type": "sync_state",
  "payload": {
    "widgets": [...],
    "layout": [...]
  },
  "sessionId": "string",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🏗️ Kiến trúc

### **Components**
- `GenericPage`: Main page component
- `GenericWorkspace`: Widget layout container
- `useGenericWebSocket`: WebSocket management hook

### **Services**
- `genericWebSocketService`: WebSocket communication
- `genericApiService`: REST API calls

### **Types**
- `GenericWidget`: Widget data structure
- `GenericLayout`: Layout configuration
- `GenericWebSocketEvent`: WebSocket event types

## 🎨 Styling

Trang sử dụng chế độ **ultra-minimal**:
- Không có header, border, hoặc controls
- Background trong suốt
- Widgets chiếm toàn bộ container
- Minimal margin và padding

## 🔧 Development

### **Testing WebSocket locally**
```javascript
// Trong browser console
const ws = new WebSocket('ws://localhost:8080/ws/generic?sessionId=test123');

ws.onopen = () => console.log('Connected');
ws.onmessage = (e) => console.log('Received:', JSON.parse(e.data));

// Gửi test event
ws.send(JSON.stringify({
  type: 'add_widget',
  payload: {
    widget: {
      id: 'test-widget',
      title: 'Test Widget',
      type: 'data-count',
      x: 0, y: 0, w: 4, h: 3
    }
  },
  sessionId: 'test123',
  timestamp: new Date().toISOString()
}));
```

### **Environment Variables**
```env
REACT_APP_WS_HOST=localhost:8080  # WebSocket host
```

## 📝 TODO

### **Backend Implementation**
- [ ] WebSocket server setup
- [ ] Session management
- [ ] Widget persistence
- [ ] Real-time event broadcasting

### **Frontend Enhancements**
- [ ] Widget animation transitions
- [ ] Error handling improvements
- [ ] Offline mode support
- [ ] Performance optimizations

### **Testing**
- [ ] Unit tests
- [ ] Integration tests
- [ ] E2E tests với WebSocket

## 🐛 Known Issues

1. **WebSocket fallback**: Hiện tại chưa có fallback khi WebSocket không khả dụng
2. **Error boundaries**: Cần thêm error boundaries cho WebSocket errors
3. **Memory leaks**: Cần kiểm tra cleanup khi component unmount

## 📞 Support

Để hỗ trợ hoặc báo lỗi, vui lòng tạo issue trong repository.
