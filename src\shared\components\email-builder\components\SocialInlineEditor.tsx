import React, { useRef, useState, useEffect } from 'react';
import { Share2, Plus, Minus, AlignLeft, AlignCenter, AlignRight, Type } from 'lucide-react';

interface SocialLink {
  platform: string;
  url: string;
  text: string;
}

interface SocialInlineEditorProps {
  socialLinks: SocialLink[];
  fontSize?: number;
  spacing?: number;
  textAlign?: 'left' | 'center' | 'right';
  showText?: boolean;
  onUpdate: (updates: {
    socialLinks?: SocialLink[];
    fontSize?: number;
    spacing?: number;
    textAlign?: 'left' | 'center' | 'right';
    showText?: boolean;
  }) => void;
  onClickOutside?: () => void;
  className?: string;
}

const SocialInlineEditor: React.FC<SocialInlineEditorProps> = ({
  socialLinks,
  fontSize = 16,
  spacing = 10,
  textAlign = 'center',
  showText = true,
  onUpdate,
  onClickOutside,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [localSocialLinks, setLocalSocialLinks] = useState<SocialLink[]>(socialLinks || [
    { platform: 'Facebook', url: 'https://facebook.com', text: 'Facebook' },
    { platform: 'Twitter', url: 'https://twitter.com', text: 'Twitter' },
    { platform: 'Instagram', url: 'https://instagram.com', text: 'Instagram' }
  ]);
  const [localFontSize, setLocalFontSize] = useState(fontSize);
  const [localSpacing, setLocalSpacing] = useState(spacing);
  const [localTextAlign, setLocalTextAlign] = useState(textAlign);
  const [localShowText, setLocalShowText] = useState(showText);

  console.log('SocialInlineEditor rendered with:', {
    socialLinks,
    fontSize,
    spacing,
    textAlign,
    showText,
    hasOnClickOutside: !!onClickOutside,
  });

  useEffect(() => {
    if (!onClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.social-inline-editor') ||
            element.closest('button') ||
            element.closest('input') ||
            element.closest('select') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'INPUT');

        if (!isToolbarClick) {
          onClickOutside();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClickOutside]);

  const handleFontSizeChange = (newSize: number) => {
    setLocalFontSize(newSize);
    onUpdate({ fontSize: newSize });
  };

  const handleSpacingChange = (newSpacing: number) => {
    setLocalSpacing(newSpacing);
    onUpdate({ spacing: newSpacing });
  };

  const handleTextAlignChange = (newAlign: 'left' | 'center' | 'right') => {
    setLocalTextAlign(newAlign);
    onUpdate({ textAlign: newAlign });
  };

  const handleShowTextChange = (show: boolean) => {
    setLocalShowText(show);
    onUpdate({ showText: show });
  };

  const handleLinkChange = (index: number, field: keyof SocialLink, value: string) => {
    const newLinks = [...localSocialLinks];
    newLinks[index] = { ...newLinks[index], [field]: value };
    setLocalSocialLinks(newLinks);
    onUpdate({ socialLinks: newLinks });
  };

  const addSocialLink = () => {
    const newLinks = [...localSocialLinks, { platform: 'New Platform', url: 'https://', text: 'New Platform' }];
    setLocalSocialLinks(newLinks);
    onUpdate({ socialLinks: newLinks });
  };

  const removeSocialLink = (index: number) => {
    if (localSocialLinks.length > 1) {
      const newLinks = localSocialLinks.filter((_, i) => i !== index);
      setLocalSocialLinks(newLinks);
      onUpdate({ socialLinks: newLinks });
    }
  };

  const platformColors: { [key: string]: string } = {
    'Facebook': '#1877f2',
    'Twitter': '#1da1f2',
    'Instagram': '#e4405f',
    'LinkedIn': '#0077b5',
    'YouTube': '#ff0000',
    'TikTok': '#000000',
    'Pinterest': '#bd081c',
    'Snapchat': '#fffc00',
  };

  return (
    <div
      ref={editorRef}
      className={`social-inline-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* Font Size */}
        <div className="flex items-center gap-1">
          <Type size={12} className="text-gray-600 dark:text-gray-300" />
          <label className="text-xs text-gray-600 dark:text-gray-300">Size:</label>
          <input
            type="number"
            value={localFontSize}
            onChange={(e) => handleFontSizeChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="10"
            max="32"
          />
        </div>

        {/* Spacing */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Gap:</label>
          <input
            type="number"
            value={localSpacing}
            onChange={(e) => handleSpacingChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="0"
            max="50"
          />
        </div>

        {/* Show Text Toggle */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Text:</label>
          <button
            className={`px-2 py-0.5 text-xs rounded transition-colors ${
              localShowText
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleShowTextChange(!localShowText)}
          >
            {localShowText ? 'ON' : 'OFF'}
          </button>
        </div>

        {/* Divider */}
        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500"></div>

        {/* Alignment Buttons */}
        <div className="flex items-center gap-1">
          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'left'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('left')}
            title="Align Left"
          >
            <AlignLeft size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'center'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('center')}
            title="Align Center"
          >
            <AlignCenter size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'right'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('right')}
            title="Align Right"
          >
            <AlignRight size={12} />
          </button>
        </div>

        {/* Add Link Button */}
        <button
          className="flex items-center gap-1 px-2 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-xs"
          onClick={addSocialLink}
          title="Add Social Link"
        >
          <Plus size={12} />
          Add
        </button>
      </div>

      {/* Social Links Editor */}
      <div className="p-3 bg-white dark:bg-gray-800">
        <div className="space-y-2">
          {localSocialLinks.map((link, index) => (
            <div key={index} className="flex items-center gap-2 p-2 border border-gray-200 dark:border-gray-600 rounded">
              <div className="flex items-center gap-2 flex-1">
                <input
                  type="text"
                  value={link.platform}
                  onChange={(e) => handleLinkChange(index, 'platform', e.target.value)}
                  className="w-20 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  placeholder="Platform"
                />
                <input
                  type="text"
                  value={link.text}
                  onChange={(e) => handleLinkChange(index, 'text', e.target.value)}
                  className="w-20 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  placeholder="Text"
                />
                <input
                  type="url"
                  value={link.url}
                  onChange={(e) => handleLinkChange(index, 'url', e.target.value)}
                  className="flex-1 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  placeholder="https://..."
                />
              </div>
              {localSocialLinks.length > 1 && (
                <button
                  className="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                  onClick={() => removeSocialLink(index)}
                  title="Remove Link"
                >
                  <Minus size={12} />
                </button>
              )}
            </div>
          ))}
        </div>

        {/* Preview */}
        <div 
          className="mt-4 p-3 border border-gray-200 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-700 flex"
          style={{ 
            justifyContent: localTextAlign === 'center' ? 'center' : localTextAlign === 'right' ? 'flex-end' : 'flex-start'
          }}
        >
          <div className="flex items-center" style={{ gap: `${localSpacing}px` }}>
            {localSocialLinks.map((link, index) => (
              <a
                key={index}
                href={link.url || '#'}
                style={{
                  color: platformColors[link.platform] || '#007bff',
                  fontSize: `${localFontSize}px`,
                  textDecoration: 'none',
                }}
                className="hover:opacity-80 transition-opacity"
                onClick={(e) => e.preventDefault()}
              >
                {localShowText ? link.text : '●'}
              </a>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialInlineEditor;
