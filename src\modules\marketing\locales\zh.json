{"marketing": {"title": "营销", "description": "管理营销活动和目标受众", "menu": {"audience": "受众", "segment": "细分", "campaign": "活动", "tags": "标签", "customFields": "自定义字段", "reports": "报告", "templateEmail": "电子邮件模板", "articles": "文章", "videos": "视频"}, "systemEmailTemplates": {"table": {"template": "模板", "type": "类型", "tags": "标签", "createdAt": "创建时间", "actions": "操作"}}, "dashboard": {"title": "营销仪表板", "description": "营销活动概览"}, "segment": {"title": "细分管理", "description": "根据条件细分客户", "manage": "管理细分", "create": "创建新细分", "edit": "编辑细分", "addNew": "创建新细分", "detail": "细分详情", "name": "细分名称", "audienceCount": "受众数量", "audience": "受众", "status": "状态", "totalContacts": "联系人总数", "conditions": "条件", "confirmDelete": "您确定要删除此细分吗？", "totalSegments": "细分总数", "statuses": {"active": "活跃", "inactive": "不活跃", "draft": "草稿"}, "operators": {"equals": "等于", "not_equals": "不等于", "contains": "包含", "not_contains": "不包含", "greater_than": "大于", "less_than": "小于", "between": "介于"}, "form": {"title": "细分信息", "name": "细分名称", "namePlaceholder": "输入细分名称...", "description": "描述", "descriptionPlaceholder": "输入细分描述...", "conditions": "条件", "conditionsDescription": "设置条件来细分客户", "addGroup": "添加条件组", "addCondition": "添加条件", "removeGroup": "删除组", "removeCondition": "删除条件", "field": "字段", "operator": "运算符", "value": "值", "valuePlaceholder": "输入值...", "and": "且", "or": "或", "operators": {"equals": "等于", "not_equals": "不等于", "contains": "包含", "not_contains": "不包含", "starts_with": "开始于", "ends_with": "结束于", "greater_than": "大于", "less_than": "小于", "greater_than_or_equal": "大于等于", "less_than_or_equal": "小于等于", "is_empty": "为空", "is_not_empty": "不为空"}, "validation": {"nameRequired": "细分名称是必填的", "nameMinLength": "细分名称至少需要2个字符", "nameMaxLength": "细分名称不能超过100个字符", "descriptionMaxLength": "描述不能超过500个字符", "conditionsRequired": "至少需要一个条件", "fieldRequired": "字段是必填的", "operatorRequired": "运算符是必填的", "valueRequired": "值是必填的"}, "buttons": {"save": "保存", "cancel": "关闭", "saveTooltip": "保存细分", "cancelTooltip": "取消并关闭表单"}, "systemField": "系统字段"}, "selectCustomField": "选择字段..."}, "tags": {"title": "标签管理", "description": "管理受众标签", "addNew": "添加新标签", "edit": "编辑标签", "name": "标签名称", "status": "状态", "confirmDelete": "您确定要删除此标签吗？", "statuses": {"active": "活跃", "inactive": "不活跃"}, "form": {"name": "标签名称", "namePlaceholder": "输入标签名称", "color": "颜色代码", "colorPlaceholder": "选择标签颜色", "randomColor": "随机颜色"}, "validation": {"nameRequired": "标签名称是必填的", "colorInvalid": "颜色代码必须是HEX格式（例如：#FF0000）"}, "deleteSuccess": "标签删除成功", "deleteError": "删除标签失败", "selectToDelete": "请选择要删除的标签", "bulkDeleteSuccess": "成功删除{{count}}个标签", "bulkDeleteError": "批量删除标签失败", "confirmBulkDeleteMessage": "您确定要删除{{count}}个选中的标签吗？", "totalTags": "标签总数", "manage": "管理标签"}, "customFields": {"title": "自定义字段", "description": "管理自定义字段"}, "reports": {"title": "报告", "description": "查看营销报告和分析"}, "templateEmail": {"title": "邮件模板管理", "description": "创建和管理邮件营销模板"}, "articles": {"title": "文章管理", "description": "创建和管理营销文章"}, "videos": {"title": "视频管理", "description": "创建和管理营销视频"}, "email": {"title": "邮件营销", "description": "管理邮件营销活动"}, "mediaResources": {"title": "媒体资源", "description": "管理Zalo OA的媒体文件", "table": {"preview": "预览", "filename": "文件名", "type": "类型", "size": "大小", "mimeType": "MIME类型", "description": "描述", "uploadedAt": "上传日期"}, "type": {"image": "图片", "gif": "GIF", "file": "文件"}, "filter": {"type": "按类型筛选"}, "form": {"title": "添加媒体资源", "officialAccount": {"label": "官方账号", "placeholder": "选择官方账号"}, "file": {"label": "文件上传", "placeholder": "拖拽或点击上传文件", "supportedTypes": "支持的文件类型：", "imageTypes": "图片：JPG、PNG（最大1MB）", "documentTypes": "文档：PDF、DOC、DOCX、CSV（最大10MB）", "gifTypes": "GIF：GIF（最大5MB）"}, "description": {"label": "描述", "placeholder": "输入文件描述（可选）"}, "submit": "上传文件"}, "validation": {"oaRequired": "请选择官方账号", "fileRequired": "请选择要上传的文件", "imageSizeExceeded": "图片不能超过1MB", "documentSizeExceeded": "文档不能超过10MB", "gifSizeExceeded": "GIF不能超过5MB", "unsupportedFileType": "不支持的文件类型"}, "success": {"uploadSuccess": "文件上传成功！"}, "error": {"uploadFailed": "文件上传失败。请重试。"}}, "sms": {"title": "短信营销", "description": "管理短信营销活动"}, "googleAds": {"title": "Google广告", "description": "管理Google广告", "detail": {"title": "Google广告详情"}, "accounts": {"title": "Google广告账户", "description": "管理Google广告账户"}, "campaigns": {"title": "Google广告活动", "description": "管理Google广告活动"}, "keywords": {"title": "Google广告关键词", "description": "管理Google广告关键词"}, "ads": {"title": "Google广告", "description": "管理Google广告"}, "reports": {"title": "Google广告报告", "description": "Google广告报告"}, "settings": {"title": "Google广告设置", "description": "Google广告设置"}}, "facebookAds": {"title": "Facebook Ads", "description": "集成和管理Facebook Ads活动", "totalAccounts": "账户", "manage": "管理Facebook Ads", "comingSoon": "功能正在开发中。请稍后再试！", "overview": {"title": "Facebook Ads概览", "description": "管理和监控Facebook广告效果", "stats": "概览统计", "quickActions": "快速操作", "performance": "效果", "recentActivity": "最近活动", "connectedAs": "已连接为", "notConnected": "未连接", "connectAccount": "连接账户", "totalAccounts": "总账户数", "activeAccounts": "活跃账户", "totalCampaigns": "总活动数", "activeCampaigns": "活跃活动"}, "stats": {"totalCampaigns": "总活动数", "totalSpend": "总花费", "impressions": "展示次数", "clicks": "点击次数", "ctr": "点击率(CTR)", "cpc": "每次点击费用(CPC)", "reach": "覆盖人数", "frequency": "频次", "conversions": "转化次数", "costPerConversion": "每次转化费用", "roas": "广告支出回报率", "adSpend": "广告支出"}, "actions": {"createCampaign": "创建活动", "createCampaignDesc": "创建新的广告活动", "manageAccounts": "管理账户", "manageAccountsDesc": "连接和管理Facebook账户", "viewReports": "查看报告", "viewReportsDesc": "分析活动效果", "manageCampaigns": "管理活动", "manageCampaignsDesc": "查看和管理现有活动", "optimizeCampaigns": "优化活动", "optimizeCampaignsDesc": "改善广告效果", "audienceInsights": "受众洞察", "audienceInsightsDesc": "分析客户受众"}, "accounts": {"title": "Facebook账户管理", "description": "连接和管理Facebook广告账户", "addManual": "手动添加", "overview": {"stats": "账户统计", "quickActions": "快速操作"}, "stats": {"totalAccounts": "总账户数", "activeAccounts": "活跃账户", "totalBalance": "总余额", "monthlySpend": "本月支出"}, "actions": {"connectAccount": "连接账户", "connectAccountDesc": "连接新的Facebook Ads账户", "syncAccounts": "同步账户", "syncAccountsDesc": "从Facebook更新信息", "managePermissions": "管理权限", "managePermissionsDesc": "配置访问权限", "viewReports": "查看报告", "viewReportsDesc": "详细账户报告"}, "modal": {"title": "连接Facebook账户", "accessToken": "访问令牌", "accessTokenPlaceholder": "输入Facebook访问令牌", "accountId": "账户ID", "accountIdPlaceholder": "输入Facebook账户ID (act_...)", "connect": "连接"}}, "status": {"active": "活跃", "paused": "暂停", "completed": "已完成", "draft": "草稿", "pending": "待审核", "rejected": "已拒绝"}, "metrics": {"impressions": "展示次数", "clicks": "点击次数", "ctr": "点击率", "cpc": "每次点击费用", "spend": "花费", "reach": "覆盖人数", "frequency": "频次", "conversions": "转化次数", "costPerConversion": "每次转化费用", "roas": "广告支出回报率", "quality": "质量", "relevance": "相关性"}}, "zaloAds": {"title": "Zalo广告", "description": "管理Zalo广告", "overview": {"title": "Zalo广告概览"}, "accounts": {"title": "Zalo广告账户管理", "description": "管理Zalo广告账户"}, "campaigns": {"title": "Zalo广告活动管理", "description": "管理Zalo广告活动"}, "reports": {"title": "Zalo广告报告", "description": "Zalo广告报告"}}, "tiktokAds": {"title": "TikTok广告", "description": "管理TikTok广告", "overview": {"title": "TikTok广告概览"}, "accounts": {"title": "TikTok广告账户", "description": "管理TikTok广告账户"}, "campaigns": {"title": "TikTok广告活动", "description": "管理TikTok广告活动"}, "creatives": {"title": "TikTok广告创意", "description": "管理TikTok广告创意"}, "audiences": {"title": "TikTok广告受众", "description": "管理TikTok广告受众"}, "reports": {"title": "TikTok广告报告", "description": "TikTok广告报告"}, "settings": {"title": "TikTok广告设置", "description": "TikTok广告设置"}}, "resources": {"title": "资源", "description": "模板库和支持工具"}, "zns": {"campaign": {"list": {"title": "ZNS活动列表"}, "create": {"title": "创建新的ZNS活动", "success": "活动创建成功", "error": "活动创建失败"}, "form": {"name": "活动名称", "namePlaceholder": "输入活动名称", "description": "描述", "descriptionPlaceholder": "输入活动描述（可选）", "integration": "官方账号", "integrationPlaceholder": "选择官方账号...", "template": "ZNS模板", "templatePlaceholder": "选择ZNS模板...", "customerListType": "设置方式", "customerListTypePlaceholder": "选择方式...", "audience": "选择受众", "audiencePlaceholder": "选择受众...", "phoneNumbers": "电话号码", "uploadFile": "上传包含电话号码的Excel文件", "uploadFileDescription": "Excel文件必须在第一列包含电话号码（跳过标题行）", "uploadFileButton": "点击选择Excel文件", "uploadFileSupport": "支持.xlsx, .xls", "uploadSuccess": "已上传{{count}}个电话号码", "uploadError": "读取Excel文件时出错。请检查文件格式。", "downloadTemplate": "下载模板", "addPhoneButton": "+ 添加号码", "addPhoneInstruction": "点击添加号码来添加电话号码", "phonePlaceholder": "0912345678", "templateDataField": "字段名称", "templateDataValue": "输入值...", "templateDataValuePlaceholder": "<值>", "addTemplateField": "+ 添加数据字段", "sendingMode": "发送模式"}, "sendingMode": {"normal": "普通发送 - 通过标准机制发送ZNS消息", "overLimit": "超限发送 - 允许OA发送ZNS标签3超限的机制"}, "customerList": {"audience": "从受众列表中选择", "upload": "上传Excel文件", "manual": "手动输入"}, "validation": {"nameRequired": "活动名称是必需的", "nameMaxLength": "活动名称不能超过255个字符", "descriptionMaxLength": "描述不能超过1000个字符", "integrationRequired": "请选择Zalo OA账号", "templateRequired": "请选择ZNS模板", "customerListTypeRequired": "请选择客户列表设置方法", "phoneInvalid": "无效的电话号码", "sendingModeRequired": "请选择发送模式", "scheduleTypeRequired": "请选择计划类型", "scheduledTimeInvalid": "计划时间必须大于当前时间"}, "templateData": {"fallback": {"customerName": "客户", "message": "系统通知"}}, "table": {"name": "活动名称", "template": "模板", "status": "状态", "audience": "受众", "metrics": "统计", "createdAt": "创建日期"}, "actions": {"start": "开始", "pause": "暂停", "resume": "恢复", "stop": "停止", "duplicate": "复制"}, "status": {"draft": "草稿", "scheduled": "已安排", "running": "运行中", "paused": "已暂停", "completed": "已完成", "cancelled": "已取消", "failed": "失败"}, "delete": {"title": "删除活动", "message": "您确定要删除活动\"{{name}}\"吗？"}}}, "common": {"moduleTitle": "营销", "all": "全部", "active": "活跃", "inactive": "不活跃", "draft": "草稿", "add": "添加新", "edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "search": "搜索", "filter": "筛选", "actions": "操作", "status": "状态", "name": "名称", "description": "描述", "createdAt": "创建时间", "updatedAt": "更新时间", "type": "类型", "id": "ID", "followers": "粉丝", "manage": "管理"}, "audience": {"title": "受众管理", "description": "管理目标受众群体", "manage": "管理受众", "create": "创建新受众", "edit": {"title": "编辑", "save": "保存", "cancel": "取消", "success": "保存成功", "successMessage": "受众信息已更新", "error": "保存错误", "errorMessage": "保存受众信息时发生错误"}, "detail": "受众详情", "name": "受众名称", "email": "邮箱", "phone": "电话号码", "address": "地址", "tags": "标签", "channel": "渠道", "platform": {"zalo": "<PERSON><PERSON>", "zaloPersonal": "Zalo个人", "facebook": "Facebook", "email": "邮箱", "phone": "电话", "web": "网站"}, "type": "类型", "status": "状态", "totalContacts": "联系人总数", "attributes": "属性", "confirmDelete": "您确定要删除此受众吗？", "types": {"customer": "客户", "lead": "潜在客户", "subscriber": "订阅者", "custom": "自定义"}, "statuses": {"active": "活跃", "inactive": "不活跃", "draft": "草稿"}, "form": {"addTitle": "添加新受众", "nameLabel": "受众名称", "namePlaceholder": "输入受众名称", "emailPlaceholder": "输入邮箱", "phonePlaceholder": "输入电话号码", "addressPlaceholder": "输入地址", "tagsPlaceholder": "选择标签...", "descriptionLabel": "描述", "descriptionPlaceholder": "输入受众描述", "typeLabel": "类型", "statusLabel": "状态", "attributesLabel": "属性", "addAttribute": "添加属性", "attributeName": "属性名称", "attributeValue": "值"}, "validation": {"nameRequired": "受众名称是必填的", "emailInvalid": "邮箱格式无效"}, "avatar": {"clickToChange": "点击更换头像", "invalidFileType": "请选择图片文件", "fileTooLarge": "文件过大。请选择小于5MB的文件", "uploadSuccess": "头像上传成功", "uploadError": "上传头像时发生错误"}, "interactionHistory": {"title": "互动历史与状态", "email": "邮箱", "zalo": "<PERSON><PERSON>", "phone": "电话", "lastSent": "最后发送", "lastMessage": "最后消息", "lastCall": "最后通话", "openRate": "打开率", "clickRate": "点击率", "responseRate": "回复率", "deliveryRate": "发送成功率", "callDuration": "通话时长", "smsCount": "短信数量", "status": {"active": "活跃", "inactive": "未连接", "verified": "已验证"}}, "socialInfo": {"title": "社交信息", "profileUrl": "个人资料链接", "lastActivity": "最后活动", "username": "用户名", "followers": "关注者", "connections": "连接数", "handle": "用户名", "status": {"connected": "已连接", "notConnected": "未连接"}}}, "zaloArticle": {"title": "标题", "image": "图片", "createDate": "创建日期", "updateDate": "更新日期", "status": "状态", "statusShow": "已发布", "statusHide": "隐藏", "statusFailed": "失败", "selectOA": "选择官方账号创建文章", "selectOAPlaceholder": "选择官方账号...", "selectOARequired": "请选择官方账号", "stats": "统计", "views": "浏览量", "likes": "点赞", "shares": "分享", "view": "查看", "selectOfficialAccount": "选择官方账号", "selectOfficialAccountPlaceholder": "选择官方账号...", "filterByType": "按类型筛选", "selectTypePlaceholder": "选择文章类型...", "deleteConfirm": "确认删除文章", "deleteDescription": "您确定要删除这篇文章吗？此操作无法撤销。", "type": {"normal": "文章", "video": "视频"}, "form": {"editTitle": "编辑文章", "createTitle": "创建新文章", "officialAccount": "官方账号", "selectOAError": "请选择官方账号", "coverImage": "封面图片", "uploadCoverImage": "点击添加封面图片", "editCoverImage": "编辑图片", "articleTitle": "文章标题", "titlePlaceholder": "输入文章标题", "author": "作者", "authorPlaceholder": "输入作者姓名", "description": "描述", "descriptionPlaceholder": "输入文章描述", "content": "文章内容", "contentPlaceholder": "输入文章内容...", "relatedArticles": "相关文章", "addRelated": "添加", "hideRelated": "隐藏", "editRelated": "编辑", "selectRelatedDescription": "选择相关文章（最多5篇）：", "addRelatedPlaceholder": "点击添加相关文章", "saveDraft": "保存草稿", "schedule": "定时发布", "publish": "发布", "uploading": "上传中...", "save": "保存", "cancel": "取消"}, "preview": {"selectOAPlaceholder": "选择官方账号", "addTitle": "点击添加标题", "addAuthor": "点击添加作者", "addDescription": "点击添加描述", "addContent": "点击添加文章内容...", "viewsCount": "次浏览", "moreArticles": "篇其他文章"}, "scheduler": {"title": "定时发布", "description": "选择时间自动发布文章", "selectDateTime": "选择日期和时间", "selectScheduleTime": "请选择定时时间", "confirmSchedule": "确认定时"}, "notifications": {"imageUploadSuccess": "图片上传成功", "imageUploadError": "图片上传时发生错误", "createSuccess": "文章创建成功", "createError": "创建文章时发生错误", "updateSuccess": "文章更新成功", "updateError": "更新文章时发生错误"}}, "zaloVideo": {"status": "状态", "published": "已发布", "draft": "草稿", "failed": "失败", "retry": "重新发布", "retrySuccess": "重新发布视频成功", "retryError": "重新发布视频时发生错误"}, "zalo": {"title": "<PERSON><PERSON>", "description": "集成Zalo官方账号，发送ZNS消息", "totalAccounts": "账户", "manage": "管理Zalo", "comingSoon": "功能正在开发中。请稍后再试！", "articles": {"title": "Zalo文章", "description": "管理Zalo官方账号上的文章", "table": {"title": "标题", "type": "类型", "status": "状态", "author": "作者", "stats": "统计", "publishTime": "发布时间"}, "type": {"title": "类型", "normal": "普通文章", "text": "文本", "image": "图片", "video": "视频", "link": "链接"}, "status": {"draft": "草稿", "published": "已发布", "archived": "已归档", "deleted": "已删除", "failed": "失败"}, "actions": {"show": "显示", "hide": "隐藏", "retry": "重新发布"}, "showSuccess": "文章显示成功", "hideSuccess": "文章隐藏成功", "showError": "显示文章时发生错误", "hideError": "隐藏文章时发生错误", "retrySuccess": "重新发布文章成功", "retryError": "重新发布文章时发生错误", "deleteError": "删除文章时发生错误", "syncSuccess": "成功同步所有OA的文章", "syncError": "同步文章时发生错误，请重试。"}, "modules": {"znsTemplates": {"title": "ZNS模板", "description": "管理客户ZNS通知模板", "sync": "从Zalo同步模板", "table": {"templateName": "模板名称", "status": "状态", "templateType": "模板类型", "createdAt": "创建时间", "actions": "操作"}, "status": {"pending": "待审核", "approved": "已审核", "rejected": "已拒绝", "disabled": "已禁用"}, "filter": {"status": "按状态筛选", "type": "按类型筛选"}, "tag": {"transaction": "交易", "promotion": "促销", "otp": "验证码"}}, "oaTemplates": {"title": "OA消息模板", "description": "创建和管理官方账号消息模板"}, "officialAccount": {"title": "OA账户", "description": "管理Zalo官方账号和连接"}, "personalAccount": {"title": "个人账户", "description": "管理个人Zalo账户和集成", "table": {"stt": "序号", "accountName": "账户名称", "status": "状态"}, "actions": {"addIntegration": "添加新集成", "relogin": "重新登录", "checkStatus": "检查登录状态"}, "messages": {"reloginSuccess": "重新登录成功", "reloginError": "重新登录时出错", "statusChecked": "状态已检查", "statusCheckError": "检查状态时出错", "deleteSuccess": "集成删除成功", "deleteError": "删除集成时出错"}, "deleteModal": {"title": "确认删除", "description": "您确定要删除{{count}}个选定的集成吗？"}, "integration": {"title": "Zalo个人集成", "description": "连接您的个人Zalo账户以使用营销功能", "oauth": {"title": "OAuth登录", "description": "推荐方法 - 通过Zalo安全登录", "instructions": "点击下面的按钮登录Zalo并授权应用程序", "loginButton": "使用Zalo登录"}, "manual": {"title": "手动集成", "description": "手动输入认证信息"}, "form": {"integrationName": "集成名称", "integrationNamePlaceholder": "输入此集成的名称...", "authCode": "认证码", "authCodeDescription": "来自Zalo开发者控制台的认证码", "authCodePlaceholder": "输入认证码...", "redirectUri": "重定向URI", "redirectUriDescription": "认证成功后的回调URL", "redirectUriPlaceholder": "输入重定向URI...", "createButton": "创建集成"}, "messages": {"createSuccess": "集成创建成功", "createError": "创建集成时出错", "oauthComingSoon": "OAuth功能正在开发中"}, "errors": {"nameRequired": "集成名称是必需的", "authCodeRequired": "认证码是必需的", "redirectUriRequired": "重定向URI是必需的"}}}, "znsCampaigns": {"title": "ZNS活动", "description": "创建和管理ZNS发送活动"}, "oaMessages": {"title": "OA消息", "description": "通过官方账号发送和管理消息"}, "followers": {"title": "粉丝管理", "description": "跟踪和管理OA粉丝"}, "groups": {"title": "群组管理", "description": "管理和组织用户群组"}, "analytics": {"title": "分析", "description": "报告和活动效果分析"}, "automation": {"title": "自动化", "description": "设置消息自动化和工作流"}, "content": {"title": "内容管理", "description": "管理文章和营销视频"}}, "overview": {"title": "Zalo营销", "description": "管理Zalo官方账号和ZNS活动", "connectAccount": "连接OA", "noAccounts": "暂无账户", "noAccountsDescription": "连接Zalo官方账号开始使用", "connectFirstAccount": "连接第一个账户", "connectedAccounts": "已连接账户", "connectedAccountsDescription": "管理已连接的Zalo官方账号", "viewAllAccounts": "查看所有账户", "stats": {"totalAccounts": "OA总数", "activeAccounts": "活跃", "totalFollowers": "粉丝总数", "newFollowersToday": "今日+12", "messagesSent": "已发送消息", "messagesToday": "今日+89", "engagementRate": "互动率", "increaseFromLastWeek": "较上周+2.1%"}}, "accounts": {"title": "管理Zalo OA", "description": "连接和管理Zalo官方账号", "connectNew": "连接新OA", "searchPlaceholder": "按OA名称搜索...", "filters": {"title": "筛选器", "advanced": "高级筛选"}, "list": {"title": "账户列表", "description": "共{{count}}个账户", "noData": "暂无账户"}, "table": {"name": "OA名称", "oaId": "OA ID", "followers": "粉丝", "status": "状态", "lastUpdate": "最后更新", "actions": "操作"}, "actions": {"refreshToken": "刷新令牌"}, "connect": {"title": "连接Zalo OA", "description": "输入信息连接Zalo官方账号", "instructions": "要连接Zalo OA，您需要管理员权限并从Zalo开发者控制台获取信息。", "learnMore": "了解更多", "oaId": "OA ID", "oaIdDescription": "Zalo官方账号ID（可在Zalo OA管理器中找到）", "oaIdPlaceholder": "输入OA ID...", "name": "OA名称", "nameDescription": "官方账号的显示名称", "namePlaceholder": "输入显示名称...", "accessToken": "Access Token", "accessTokenDescription": "来自Zalo开发者控制台的访问令牌", "accessTokenPlaceholder": "输入access token...", "refreshToken": "Refresh <PERSON>", "refreshTokenDescription": "用于续期访问令牌的刷新令牌", "refreshTokenPlaceholder": "输入refresh token...", "avatar": "头像URL（可选）", "avatarDescription": "OA头像图片URL", "avatarPlaceholder": "https://...", "submit": "连接", "help": {"title": "需要帮助？", "description": "参考Zalo官方账号连接指南", "viewDocs": "查看文档", "step1": "1. 访问Zalo开发者控制台并创建应用", "step2": "2. 从官方账号管理获取OA ID", "step3": "3. 生成访问令牌和刷新令牌", "step4": "4. 如需要，配置webhook URL"}, "page": {"title": "连接Zalo官方账号", "description": "选择适合您需求的连接方式", "methods": {"oauth": {"title": "<PERSON><PERSON> v4", "value": "推荐", "description": "通过<PERSON>alo <PERSON> v4连接"}, "api": {"title": "Zalo API Explorer", "value": "手动", "description": "使用Access Token和Refresh Token连接", "formTitle": "Zalo API Explorer", "formDescription": "从Zalo开发者控制台输入Access Token和Refresh Token", "accessTokenLabel": "Access Token *", "accessTokenPlaceholder": "输入access token", "refreshTokenLabel": "Refresh <PERSON> *", "refreshTokenPlaceholder": "输入refresh token", "connectButton": "连接", "backButton": "返回"}}, "success": "官方账号连接成功！", "error": "连接时发生错误"}}}, "followers": {"title": "管理粉丝", "description": "{{name}}的粉丝", "descriptionDefault": "管理粉丝列表", "export": "导出", "sync": "同步", "searchPlaceholder": "按姓名、电话搜索...", "stats": {"totalFollowers": "粉丝总数", "activeFollowers": "活跃", "newThisWeek": "本周新增", "selected": "已选择"}, "bulkActions": {"title": "批量操作", "description": "对{{count}}个选中粉丝执行操作", "addTag": "添加标签", "sendMessage": "发送消息", "addTagVip": "添加\"VIP\"标签", "addTagNewCustomer": "添加\"新客户\"标签", "sendBulkMessage": "批量发送消息"}, "table": {"name": "姓名", "phone": "电话", "followDate": "关注日期", "lastInteraction": "最后互动", "tags": "标签", "status": "状态", "actions": "操作"}}, "groups": {"title": "群组管理", "description": "管理Zalo群组和成员", "searchPlaceholder": "搜索群组...", "stats": {"totalGroups": "群组总数", "activeGroups": "活跃群组", "totalMembers": "成员总数", "newMembers": "新成员"}, "table": {"name": "群组名称", "members": "成员", "status": "状态", "createdAt": "创建时间", "lastActivity": "最后活动", "actions": "操作", "performance": "效果"}, "filters": {"title": "筛选", "byOA": "按OA", "byStatus": "按状态", "byActivity": "按活动"}}, "zns": {"title": "ZNS模板", "description": "管理Zalo通知服务模板", "createTemplate": "创建模板", "searchPlaceholder": "搜索模板...", "createNew": "新建", "collapse": "收起", "campaign": {"title": "ZNS活动", "create": "创建活动", "createSuccess": "活动创建成功", "createNew": "新建活动", "subtitle": "选择您要创建的活动类型", "configureSubtitle": "配置活动详情", "createButton": "创建活动", "form": {"name": "活动名称", "namePlaceholder": "输入活动名称", "description": "描述", "descriptionPlaceholder": "输入活动描述（可选）", "integration": "官方账号", "integrationPlaceholder": "选择官方账号...", "templatePlaceholder": "选择ZNS模板...", "template": "ZNS模板", "audience": "受众", "audiencePlaceholder": "选择受众...", "schedule": "计划", "schedulePlaceholder": "选择计划时间...", "now": "立即发送", "later": "定时发送"}, "list": {"title": "活动列表", "description": "共{{count}}个活动"}, "actions": {"start": "开始", "pause": "暂停", "resume": "恢复", "cancel": "取消"}, "status": {"draft": "草稿", "scheduled": "已计划", "running": "运行中", "paused": "已暂停", "cancelled": "已取消", "completed": "已完成"}}, "stats": {"totalTemplates": "模板总数", "approved": "已审核", "pending": "待审核", "rejected": "已拒绝", "avgCost": "平均费用", "perMessage": "每条消息", "newTemplates": "+2个新模板", "readyToUse": "可使用", "underReview": "审核中"}, "table": {"template": "模板", "templateId": "模板ID", "status": "状态", "quality": "质量", "cost": "费用", "params": "参数", "updated": "更新", "actions": "操作"}, "status": {"approved": "已审核", "pending": "待审核", "rejected": "已拒绝", "disabled": "已禁用"}, "quality": {"high": "高", "normal": "普通", "low": "低"}, "sync": {"success": "成功同步 {{count}} 个 ZNS 模板从 Zalo API", "error": "同步 ZNS 模板失败：{{error}}"}, "create": {"title": "创建ZNS模板", "success": "模板创建成功", "description": "创建新的ZNS通知模板", "instructions": {"title": "ZNS模板创建指南", "description": "模板将发送到Zalo进行审核后才能使用。内容必须符合Zalo的ZNS规定。使用{param_name}标记动态参数。"}, "form": {"oaLabel": "选择官方账号", "oaHelp": "选择OA创建模板", "oaPlaceholder": "选择官方账号...", "nameLabel": "模板名称", "nameHelp": "模板的描述性名称（仅内部使用）", "namePlaceholder": "例如：订单确认，促销通知...", "contentLabel": "模板内容", "contentHelp": "ZNS消息内容。使用{param_name}表示动态参数", "contentPlaceholder": "例如：您好{customer_name}，您的订单#{order_id}已确认，总价值{total_amount}越南盾。感谢您的购买！"}}, "template": {"form": {"steps": {"basicInfo": "基本信息", "components": "内容声明", "review": "提交审核"}, "validation": {"incompleteTemplate": "需要根据要求完善模板"}, "basicInfo": {"title": "基本信息", "description": "声明以下信息以创建ZNS", "templateName": {"label": "ZNS模板名称", "placeholder": "例如：订单确认通知", "required": "模板名称不能为空"}, "officialAccount": {"label": "选择官方账号", "placeholder": "选择官方账号...", "required": "请选择官方账号", "loading": "正在加载OA列表..."}, "contentType": {"label": "选择ZNS内容类型", "placeholder": "选择内容类型", "required": "请选择内容类型"}, "templateType": {"label": "选择模板类型", "placeholder": "选择模板类型", "required": "请选择模板类型"}, "note": {"label": "备注", "placeholder": "关于模板的附加备注..."}}, "components": {"title": "内容声明", "description": "设计您的ZNS内容", "sections": {"header": "ZNS头部", "body": "ZNS内容", "footer": "操作按钮"}, "empty": "请设计", "notImplemented": "组件未实现"}, "review": {"title": "提交审核", "description": "查看信息并提交模板以供审核", "templateInfo": {"title": "模板信息", "name": "名称", "type": "类型", "tag": "标签", "note": "备注", "components": "组件", "parameters": "参数"}, "parameters": {"title": "模板参数", "name": "参数", "type": "参数类型", "sampleValue": "示例值", "empty": "未找到参数"}}, "actions": {"continue": "继续", "create": "创建", "back": "返回", "cancel": "取消", "creating": "创建中..."}}, "types": {"1": "自定义ZNS", "2": "身份验证ZNS", "3": "付款请求ZNS", "4": "付款ZNS", "5": "优惠券ZNS"}, "tags": {"TRANSACTION": "交易（级别1）", "CUSTOMER_CARE": "客户服务（级别2）", "PROMOTION": "促销（级别3）", "OTP": "OTP（级别1）"}}, "parameters": {"types": {"customer": "客户姓名（30）", "phone": "电话号码（15）", "address": "地址（200）", "id": "ID代码（30）", "personal": "人称代词（30）", "status": "交易状态（30）", "contact": "联系信息（50）", "time": "性别/称谓（5）", "product": "产品名称/品牌（200）", "amount": "数量/金额（20）", "duration": "持续时间（20）", "otp": "OTP（10）", "url": "URL（200）", "money": "货币（VND）（12）", "bank_note": "银行转账备注（90）"}, "sampleValues": {"customer": "张三", "phone": "**********", "address": "胡志明市第1区ABC街123号", "id": "CODE123", "personal": "先生/女士", "status": "已确认", "contact": "<EMAIL>", "time": "先生", "product": "iPhone 15 Pro Max", "amount": "2", "duration": "30分钟", "otp": "123456", "url": "https://example.com", "money": "1,500,000", "bank_note": "订单#12345付款", "default": "值{{paramName}}"}, "displayNames": {"customer": "客户姓名", "phone": "电话号码", "address": "地址", "id": "ID代码", "personal": "人称代词", "status": "交易状态", "contact": "联系信息", "time": "性别/称谓", "product": "产品名称/品牌", "amount": "数量/金额", "duration": "持续时间", "otp": "OTP代码", "url": "URL", "money": "货币（VND）", "bank_note": "银行转账备注"}, "otpDisplayName": "OTP验证码"}, "componentSelector": {"components": {"images": {"name": "图片", "description": "向ZNS添加图片"}, "logo": {"name": "标志", "description": "品牌标志"}, "title": {"name": "标题", "description": "消息主标题"}, "paragraph": {"name": "段落", "description": "文本内容"}, "otp": {"name": "OTP", "description": "OTP验证码"}, "table": {"name": "表格", "description": "以表格格式显示数据"}, "voucher": {"name": "优惠券", "description": "折扣码/优惠券"}, "payment": {"name": "付款", "description": "付款信息"}, "rating": {"name": "评级", "description": "服务评级"}, "buttons": {"name": "按钮", "description": "操作按钮"}}, "defaultData": {"title": "示例标题", "paragraph": "示例段落内容", "otp": "123456", "button": "了解更多", "rating": {"1": "非常不满意", "2": "不满意", "3": "一般", "4": "满意", "5": "非常满意"}}, "displayNames": {"LOGO": "标志", "IMAGES": "图片", "TITLE": "标题", "PARAGRAPH": "段落", "TABLE": "表格", "OTP": "OTP代码", "VOUCHER": "优惠券", "PAYMENT": "付款", "RATING": "评级", "BUTTONS": "按钮"}, "defaultContent": {"title": "示例标题", "paragraph": "示例段落内容"}, "sections": {"header": "头部 - 标志，图片", "body": "主体 - ZNS内容", "footer": "底部 - 操作按钮", "headerRequired": "标志，图片 *", "ingredients": "成分"}, "templateChange": {"modal": {"title": "确认更改模板类型", "warning": "您确定要更改模板类型吗？", "description": "所有当前组件数据将被删除且无法恢复。您需要重新设置所有内容。", "cancel": "取消", "confirm": "确认"}}, "componentNames": {"logo": "标志", "images": "图片", "title": "标题", "paragraph": "段落", "table": "表格", "otp": "OTP代码", "voucher": "优惠券", "payment": "支付", "rating": "评分", "buttons": "按钮"}, "validation": {"headerLogoImageExclusive": "头部只能包含标志或图片，不能同时包含两者", "required": "{{component}}是必需的", "minRequired": "{{component}}至少需要{{min}}个组件（当前：{{current}}）", "maxExceeded": "{{component}}最多允许{{max}}个组件（当前：{{current}}）", "voucherHeaderRequired": "优惠券模板必须至少有一个：标志或图片"}}, "components": {"common": {"delete": "删除组件", "required": "必填", "optional": "可选", "maxLength": "最多{{max}}个字符"}, "title": {"label": "标题", "help": "消息主标题", "placeholder": "输入标题..."}, "buttons": {"label": "操作按钮", "help": "操作按钮", "types": {"1": "在应用中打开URL", "2": "拨打电话", "3": "在浏览器中打开URL", "4": "分享", "5": "复制", "6": "打开应用", "7": "注册", "8": "订购", "9": "其他"}, "fields": {"type": "按钮类型", "title": "按钮内容", "url": "链接URL", "phone": "电话号码"}, "placeholders": {"selectType": "选择按钮类型", "title": "了解更多", "url": "输入URL（例如：https://example.com）", "phone": "输入电话号码（例如：**********）"}, "validation": {"phoneValid": "输入有效的电话号码", "urlValid": "输入完整的URL，包括http://或https://"}}, "images": {"label": "图片", "help": "只允许添加1张图片", "upload": {"placeholder": "拖拽或上传图片 - PNG、JPG格式。尺寸：16:9，最大500KB", "replace": "替换当前图片", "uploading": "上传中...", "uploaded": "✓ 已上传", "error": "错误：{{error}}"}, "validation": {"notImage": "文件{{name}}不是图片"}}, "logo": {"label": "标志", "help": "明暗主题标志"}, "paragraph": {"label": "段落", "help": "文本内容", "placeholder": "感谢您在我们店购买{{product_name}}产品。"}, "otp": {"label": "包含OTP代码的内容", "help": "包含OTP代码的内容"}, "table": {"label": "表格", "help": "以表格格式显示数据", "description": "表格和段落组件可以更改位置", "addRow": "添加行", "placeholders": {"title": "订单编号", "value": "{{order_code}}"}}, "voucher": {"label": "优惠券", "help": "折扣码/优惠券", "description": "优惠券信息", "fields": {"title": "标题", "condition": "适用条件", "startDate": "开始日期", "endDate": "到期日期", "voucherCode": "优惠券代码", "displayCode": "显示优惠券代码"}, "placeholders": {"title": "节省70,000đ", "condition": "适用于200K以上订单", "startDate": "选择开始日期", "endDate": "选择到期日期", "voucherCode": "&lt;voucher_code&gt;"}, "displayTypes": {"barcode": "条形码", "qrcode": "二维码", "textcode": "文本代码"}}, "payment": {"label": "支付", "help": "支付信息", "description": "支付信息", "fields": {"bank": "银行", "accountName": "账户名称", "accountNumber": "账户号码", "amount": "金额（VND）", "note": "转账备注"}, "placeholders": {"selectBank": "-- 选择银行", "accountName": "黎辉明智", "accountNumber": "**************", "amount": "<amount>", "note": "<amount>"}, "validation": {"accountNameRequired": "账户名称不能为空", "accountNameLength": "账户名称必须为1-100个字符", "accountNumberRequired": "账户号码不能为空", "accountNumberLength": "账户号码必须为1-100个字符", "amountInvalid": "无效金额", "amountMin": "最低金额为2,000 VND", "amountMax": "最高金额为500,000,000 VND", "noteMaxLength": "转账备注最多90个字符"}, "loading": {"banks": "正在加载银行列表..."}, "errors": {"loadBanks": "无法加载银行列表", "loadBanksGeneral": "加载银行列表时出错"}, "hints": {"accountName": "最少1个字符，最多100个字符", "accountNumber": "最少1个字符，最多100个字符"}}, "rating": {"label": "评分", "help": "服务评分", "description": "服务评分", "title": "评分", "fields": {"scale": "评分等级", "title": "标题", "action": "操作", "question": "问题", "answers": "答案", "thanks": "感谢信息", "description": "描述"}, "placeholders": {"title": "输入标题", "question": "例如：我们可以改进什么？", "answer1": "更好的包装", "answer2": "更好的产品质量", "thanks": "感谢您的反馈！", "description": "您的所有反馈都很宝贵。我们将继续努力为您提供更好的服务。"}, "actions": {"addDetail": "添加详情", "addAnswer": "添加"}, "defaultTitles": {"1": "非常不满意", "2": "不满意", "3": "一般", "4": "满意", "5": "非常满意"}}}, "preview": {"title": "预览", "phone": "手机", "message": "消息", "parameters": "参数", "parameterName": "参数名称", "parameterType": "类型", "sampleValue": "示例值", "required": "必填"}, "messages": {"success": {"create": "ZNS模板创建成功！", "update": "ZNS模板更新成功！", "delete": "ZNS模板删除成功！"}, "error": {"create": "创建ZNS模板失败。请重试。", "update": "更新ZNS模板失败。请重试。", "delete": "删除ZNS模板失败。请重试。", "loadOA": "无法加载官方账号列表", "loadTags": "无法加载模板标签列表", "selectOA": "请选择官方账号"}}, "actions": {"create": "新建", "edit": "编辑", "delete": "删除", "preview": "预览", "save": "保存", "cancel": "取消", "next": "下一步", "previous": "上一步", "submit": "提交审核", "add": "添加", "remove": "移除", "upload": "上传", "select": "选择"}, "validation": {"templateNameRequired": "模板名称是必需的", "templateTypeRequired": "模板类型是必需的", "componentRequired": "至少需要一个组件", "titleRequired": "标题是必需的", "contentRequired": "内容是必需的", "imageRequired": "图片是必需的", "buttonTitleRequired": "按钮标题是必需的", "buttonUrlRequired": "按钮网址是必需的", "parameterNameRequired": "参数名称是必需的", "parameterTypeRequired": "参数类型是必需的", "required": "此字段是必需的", "maxLength": "不能超过{max}个字符", "minLength": "至少需要{min}个字符", "invalidUrl": "无效的网址", "invalidEmail": "无效的邮箱", "invalidPhone": "无效的电话号码"}, "templates": {"custom": "自定义", "authentication": "身份验证", "payment": "支付", "voucher": "优惠券", "rating": "评级", "selectType": "选择模板类型", "step1": "步骤1：选择类型", "step2": "步骤2：设计", "step3": "步骤3：参数", "step4": "步骤4：预览"}, "notifications": {"createSuccess": "模板创建成功", "createError": "模板创建失败", "updateSuccess": "模板更新成功", "updateError": "模板更新失败", "deleteSuccess": "模板删除成功", "deleteError": "模板删除失败", "uploadSuccess": "上传成功", "uploadError": "上传失败"}}, "oaManagement": {"title": "Zalo OA管理", "navigation": {"content": "内容", "zns": "ZNS"}, "tabs": {"articles": "文章", "znsTemplates": "ZNS模板", "znsConfiguration": "ZNS配置", "znsResources": "ZNS资源"}, "configuration": {"title": "ZNS配置", "description": "配置ZNS模板的设置", "settings": "设置", "general": "常规", "advanced": "高级", "save": "保存配置", "reset": "重置", "export": "导出配置", "import": "导入配置"}, "resources": {"title": "ZNS资源", "description": "管理图片、标志和其他资源", "images": "图片", "logos": "标志", "files": "文件", "upload": "上传", "manage": "管理", "delete": "删除", "edit": "编辑", "usage": "使用次数"}, "chips": {"officialAccount": "官方账号", "templatesZalo": "Zalo模板", "templateTags": "模板标签", "rating": "评级"}, "templateTags": {"title": "模板标签", "description": "官方账号允许发送的ZNS内容类型：", "loading": "正在加载模板标签...", "noData": "没有可用的模板标签", "note": "根据OA的ZNS发送质量，Zalo将自动调整OA可以发送的内容类型。", "types": {"TRANSACTION": "交易（1级）", "CUSTOMER_CARE": "客户服务（2级）", "PROMOTION": "促销（3级）"}}, "templates": {"title": "Zalo模板", "loading": "正在加载模板...", "noData": "没有可用的模板", "table": {"templateId": "模板ID", "templateName": "模板名称", "status": "状态", "templateTag": "模板标签", "price": "价格", "createdTime": "创建时间", "actions": "操作"}, "actions": {"viewDetail": "查看详情", "ratings": "评级"}}, "ratings": {"title": "模板ID {{templateId}} 的评级", "loading": "正在加载评级数据...", "noData": "此模板没有可用的评级数据", "backToTemplates": "← 返回模板", "description": "模板 {{templateId}} 的评级数据"}, "templateDetail": {"title": "模板详情", "loading": "正在加载模板详情...", "notFound": "未找到模板", "basicInfo": {"title": "基本信息", "templateId": "模板ID", "templateName": "模板名称", "status": "状态", "templateTag": "模板标签", "price": "价格"}, "additionalDetails": {"title": "附加详情", "templateQuality": "模板质量", "timeout": "超时", "applyTemplateQuota": "应用模板配额", "previewUrl": "预览URL", "viewPreview": "查看预览"}, "parameters": {"title": "参数", "count": "参数 ({{count}})", "type": "类型：", "length": "长度：", "acceptNull": "接受空值：", "required": "必需", "yes": "是", "no": "否"}, "buttons": {"title": "按钮", "count": "按钮 ({{count}})", "type": "类型 {{type}}", "content": "内容："}, "rejectionReason": {"title": "拒绝原因"}, "actions": {"close": "关闭"}, "status": {"REJECT": "已拒绝", "APPROVED": "已批准", "PENDING": "待审核"}, "minutes": "分钟"}}, "znsImages": {"form": {"title": "添加ZNS图片", "subtitle": "根据Zalo规定上传ZNS模板的图片/标志", "requirements": "Zalo要求：", "formatRequirement": "格式：PNG、JPG", "sizeRequirement": "最大大小：500 KB", "ratioRequirement": "宽高比：16:9（推荐：1920x1080px）", "limitRequirement": "限制：5000张图片/月/应用", "files": "选择图片", "uploadPlaceholder": "拖拽或点击上传图片（PNG、JPG - 最大500KB）", "description": "描述", "descriptionPlaceholder": "输入图片描述...", "submit": "上传", "invalidFile": "无效文件", "fileRequired": "请至少选择一个文件", "fileValidation": "文件必须是PNG/JPG格式且不超过500KB", "aspectRatio": "图片必须是16:9宽高比", "descriptionRequired": "描述是必需的", "descriptionTooLong": "描述不能超过255个字符", "invalidDimensions": "图片必须是16:9宽高比。推荐尺寸：1920x1080px", "useTemplate": "使用模板", "editImage": "编辑图片", "needsEditing": "图片需要编辑", "editNow": "立即编辑", "fileTooLarge": "文件过大，需要压缩或编辑", "wrongRatio": "宽高比不正确，需要裁剪"}, "table": {"title": "ZNS图片列表", "filename": "文件名", "description": "描述", "type": "类型", "size": "大小", "created": "创建时间", "actions": "操作", "noDescription": "无描述"}}, "imageEditor": {"title": "编辑图片", "loading": "正在加载图片...", "crop": "裁剪", "filter": "滤镜", "preview": "预览", "cropControls": "裁剪控制", "autoFit": "自动适配比例", "filterControls": "滤镜", "brightness": "亮度", "contrast": "对比度", "saturation": "饱和度", "blur": "模糊", "save": "保存"}, "templates": {"title": "选择图片模板", "znsDesc": "ZNS图片模板（16:9）", "logoDesc": "标志模板（400x96）", "customText": "自定义文本", "textPlaceholder": "输入您的文本...", "preview": "预览", "selectToPreview": "选择模板进行预览", "promoTemplate1": "促销1", "promoDesc1": "蓝色渐变促销模板", "promoTemplate2": "促销2", "promoDesc2": "红色渐变促销模板", "infoTemplate1": "通知1", "infoDesc1": "简单通知模板", "logoTemplate1": "简单标志1", "logoDesc1": "蓝色渐变背景标志", "logoTemplate2": "简单标志2", "logoDesc2": "白色背景标志", "logoTemplate3": "现代标志", "logoDesc3": "现代设计标志"}, "personalCampaigns": {"title": "Zalo个人活动", "description": "管理Zalo个人活动", "table": {"name": "活动名称", "type": "活动类型", "messageType": "消息类型", "status": "状态", "progress": "进度", "createdAt": "创建时间"}, "campaignTypes": {"sendAll": "批量发送消息", "crawlFriends": "抓取好友列表", "crawlGroups": "抓取群组列表", "sendFriendRequest": "发送好友请求"}, "messageTypes": {"text": "文本", "image": "图片", "qr_code": "二维码"}, "status": {"draft": "草稿", "scheduled": "已安排", "active": "运行中", "paused": "已暂停", "completed": "已完成", "failed": "失败"}, "actions": {"start": "开始", "pause": "暂停"}, "messages": {"createSuccess": "活动创建成功"}, "create": {"title": "创建新的Zalo个人活动", "subtitle": "选择您要创建的活动类型", "configureSubtitle": "配置活动详情", "formNotImplemented": "此活动类型的表单正在开发中。"}, "forms": {"crawFriendsList": {"title": "抓取好友列表", "description": "创建活动以从Zalo账户收集好友列表", "campaignName": "活动名称", "campaignNamePlaceholder": "输入抓取好友列表活动名称", "accounts": "账户列表", "accountsPlaceholder": "选择Zalo账户以抓取好友列表", "submit": "创建活动"}}}, "oaCampaigns": {"title": "OA活动", "description": "管理Zalo OA活动", "table": {"name": "活动名称", "type": "活动类型", "status": "状态", "progress": "进度", "createdAt": "创建时间"}, "actions": {"start": "开始", "pause": "暂停"}, "messages": {"createSuccess": "活动创建成功"}, "create": {"title": "创建新的OA活动", "subtitle": "选择您要创建的活动类型", "configureSubtitle": "配置活动详情", "createButton": "创建活动"}, "addMemberToGroup": {"title": "添加成员到群组", "description": "添加成员到群组的表单正在开发中。"}, "sendOAMessage": {"title": "发送OA消息", "description": "发送OA消息的表单正在开发中。"}, "sendZNSMessage": {"title": "发送ZNS消息", "description": "发送ZNS消息的表单正在开发中。"}}, "znsCampaigns": {"title": "ZNS活动", "description": "管理Zalo通知服务活动"}}, "customField": {"configId": "字段标识符名称", "title": "自定义字段", "description": "管理自定义字段", "adminDescription": "管理系统自定义字段", "add": "添加自定义字段", "edit": "编辑自定义字段", "dataType": "数据类型", "dataTypes": {"text": "文本", "number": "数字", "boolean": "真/假", "date": "日期", "select": "选择", "object": "对象"}, "addForm": "添加新自定义字段", "editForm": "编辑自定义字段", "component": "组件类型", "components": {"input": "输入框", "textarea": "文本区域", "select": "下拉选择", "checkbox": "复选框", "radio": "单选按钮", "date": "日期", "number": "数字", "file": "文件", "multiSelect": "多选"}, "type": "数据类型", "type.string": "文本", "type.number": "数字", "type.boolean": "是/否", "type.date": "日期", "type.object": "对象", "type.array": "数组", "types": {"text": "文本", "number": "数字", "boolean": "是/否", "date": "日期", "select": "选择框", "object": "对象", "array": "数组", "string": "文本"}, "name": "字段名称", "label": "标签", "placeholder": "占位符", "defaultValue": "默认值", "options": "选项", "required": "必填", "validation": {"minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "min": "最小值", "max": "最大值"}, "form": {"fieldKeyLabel": "标识符字段", "fieldKey": "字段键", "fieldKeyPlaceholder": "full_name", "displayName": "显示名称", "tags": "标签", "componentRequired": "请选择组件类型", "labelRequired": "请输入标签", "typeRequired": "请选择数据类型", "idRequired": "请输入字段标识符名称", "labelPlaceholder": "输入显示标签", "descriptionPlaceholder": "输入此字段的描述", "placeholderPlaceholder": "输入占位符", "defaultValuePlaceholder": "输入默认值", "optionsPlaceholder": "输入选项，用逗号分隔或JSON格式", "selectOptionsPlaceholder": "输入格式：名称|值，每对值一行。例如：\na|1\nb|2", "booleanDefaultPlaceholder": "选择默认值", "dateDefaultPlaceholder": "选择默认日期", "description": "描述", "labelTagRequired": "请至少添加一个标签", "fieldIdLabel": "字段标识符名称", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "字段显示名称", "displayNamePlaceholder": "输入此字段的显示名称", "displayNameRequired": "请输入字段显示名称", "labelInputPlaceholder": "输入标签并按回车", "tagsCount": "个标签已添加", "patternSuggestions": "常用模式建议：", "defaultValue": "默认值", "minLength": "最小长度", "maxLength": "最大长度", "minValue": "最小值", "maxValue": "最大值", "pattern": "模式", "options": "选项", "min": "最小值", "max": "最大值", "placeholder": "占位符", "required": "必填", "labels": "标签", "showAdvancedSettings": "显示高级设置"}, "createSuccess": "自定义字段创建成功", "createError": "创建自定义字段时出错", "updateSuccess": "自定义字段更新成功", "updateError": "更新自定义字段时出错", "deleteSuccess": "自定义字段删除成功", "deleteError": "删除自定义字段时出错", "loadError": "加载自定义字段时出错", "booleanValues": {"true": "是", "false": "否"}, "patterns": {"email": "电子邮件", "phoneVN": "越南电话", "phoneIntl": "国际电话", "postalCodeVN": "越南邮政编码", "lettersOnly": "仅字母", "numbersOnly": "仅数字", "alphanumeric": "字母和数字", "noSpecialChars": "无特殊字符", "url": "URL", "ipv4": "IPv4", "strongPassword": "强密码", "vietnameseName": "越南姓名", "studentId": "学生ID", "nationalId": "身份证", "taxCode": "税号", "dateFormat": "日期 (dd/mm/yyyy)", "timeFormat": "时间 (hh:mm)", "hexColor": "十六进制颜色", "base64": "Base64", "uuid": "UUID", "filename": "文件名", "urlSlug": "URL Slug", "variableName": "变量名", "creditCard": "信用卡号", "qrCode": "二维码", "gpsCoordinate": "GPS坐标", "rgbColor": "RGB颜色", "domain": "域名", "decimal": "小数", "barcode": "条形码"}, "confirmDeleteMessage": "您确定要删除此自定义字段吗？", "confirmBulkDeleteMessage": "您确定要删除{{count}}个选中的自定义字段吗？", "bulkDeleteSuccess": "成功删除{{count}}个自定义字段", "bulkDeleteError": "删除自定义字段时发生错误", "selectedItems": "已选择{{count}}项", "totalFields": "自定义字段总数", "manage": "管理自定义字段", "noDescription": "无描述"}, "tag": {"name": "标签名称", "color": "颜色", "objectCount": "对象数量", "validation": {"nameRequired": "标签名称是必填的", "colorInvalid": "颜色代码必须是HEX格式（例如：#FF0000）"}}, "emailMarketing": {"title": "电子邮件", "description": "管理电子邮件活动和模板", "totalTemplates": "模板总数", "manage": "管理电子邮件"}}, "segment": {"title": "细分管理", "description": "根据条件细分客户", "manage": "管理细分", "create": "创建新细分", "edit": "编辑细分", "detail": "细分详情", "name": "细分名称", "audience": "受众", "status": "状态", "totalContacts": "联系人总数", "conditions": "条件", "confirmDelete": "您确定要删除此细分吗？", "totalSegments": "细分总数", "statuses": {"active": "活跃", "inactive": "不活跃", "draft": "草稿"}, "operators": {"equals": "等于", "not_equals": "不等于", "contains": "包含", "not_contains": "不包含", "greater_than": "大于", "less_than": "小于", "between": "介于"}, "form": {"title": "细分信息", "name": "细分名称", "namePlaceholder": "输入细分名称...", "description": "描述", "descriptionPlaceholder": "输入细分描述...", "conditions": "条件", "conditionsDescription": "设置条件来细分客户", "addGroup": "添加条件组", "addCondition": "添加条件", "removeGroup": "删除组", "removeCondition": "删除条件", "field": "字段", "operator": "运算符", "value": "值", "valuePlaceholder": "输入值...", "and": "且", "or": "或", "operators": {"equals": "等于", "not_equals": "不等于", "contains": "包含", "not_contains": "不包含", "starts_with": "开始于", "ends_with": "结束于", "greater_than": "大于", "less_than": "小于", "greater_than_or_equal": "大于等于", "less_than_or_equal": "小于等于", "is_empty": "为空", "is_not_empty": "不为空"}, "validation": {"nameRequired": "细分名称是必填的", "nameMinLength": "细分名称至少需要2个字符", "nameMaxLength": "细分名称不能超过100个字符", "descriptionMaxLength": "描述不能超过500个字符", "conditionsRequired": "至少需要一个条件", "fieldRequired": "字段是必填的", "operatorRequired": "运算符是必填的", "valueRequired": "值是必填的"}, "buttons": {"save": "保存", "cancel": "关闭", "saveTooltip": "保存细分", "cancelTooltip": "取消并关闭表单"}, "systemField": "系统字段"}, "selectCustomField": "选择字段..."}, "campaign": {"title": "活动管理", "description": "创建和管理营销活动", "manage": "管理活动", "create": "创建新活动", "edit": "编辑活动", "detail": "活动详情", "name": "活动名称", "type": "类型", "status": "状态", "segment": "细分", "audience": "受众", "totalContacts": "联系人总数", "startDate": "开始日期", "endDate": "结束日期", "confirmDelete": "您确定要删除此活动吗？", "activeCampaigns": "活跃活动", "types": {"email": "电子邮件", "sms": "短信", "push": "推送通知", "social": "社交媒体", "multi_channel": "多渠道"}, "statuses": {"draft": "草稿", "scheduled": "已计划", "running": "运行中", "paused": "已暂停", "completed": "已完成", "cancelled": "已取消"}, "metrics": {"sent": "已发送", "delivered": "已送达", "opened": "已打开", "clicked": "已点击", "converted": "已转化", "bounced": "已退回", "unsubscribed": "已取消订阅"}, "form": {"nameLabel": "活动名称", "namePlaceholder": "输入活动名称", "descriptionLabel": "描述", "descriptionPlaceholder": "输入活动描述", "typeLabel": "类型", "statusLabel": "状态", "segmentLabel": "细分", "segmentPlaceholder": "选择细分", "startDateLabel": "开始日期", "endDateLabel": "结束日期", "endDateOptional": "（可选）"}}, "tags": {"title": "标签管理", "description": "管理受众标签", "addNew": "添加新标签", "edit": "编辑标签", "name": "标签名称", "status": "状态", "confirmDelete": "您确定要删除此标签吗？", "statuses": {"active": "活跃", "inactive": "不活跃"}, "form": {"name": "标签名称", "namePlaceholder": "输入标签名称", "color": "颜色代码", "colorPlaceholder": "选择标签颜色", "randomColor": "随机颜色"}, "validation": {"nameRequired": "标签名称是必填的", "colorInvalid": "颜色代码必须是HEX格式（例如：#FF0000）"}, "deleteSuccess": "标签删除成功", "deleteError": "删除标签失败", "selectToDelete": "请选择要删除的标签", "bulkDeleteSuccess": "成功删除{{count}}个标签", "bulkDeleteError": "批量删除标签失败", "confirmBulkDeleteMessage": "您确定要删除{{count}}个选中的标签吗？", "totalTags": "标签总数", "manage": "管理标签"}, "tag": {"name": "标签名称", "color": "颜色", "objectCount": "对象数量", "validation": {"nameRequired": "标签名称是必填的", "colorInvalid": "颜色代码必须是HEX格式（例如：#FF0000）"}}, "customField": {"configId": "字段标识符名称", "title": "自定义字段", "description": "管理自定义字段", "adminDescription": "管理系统自定义字段", "add": "添加自定义字段", "edit": "编辑自定义字段", "addForm": "添加新自定义字段", "editForm": "编辑自定义字段", "component": "组件类型", "components": {"input": "输入框", "textarea": "文本区域", "select": "下拉选择", "checkbox": "复选框", "radio": "单选按钮", "date": "日期", "number": "数字", "file": "文件", "multiSelect": "多选"}, "type": "数据类型", "type.string": "文本", "type.number": "数字", "type.boolean": "是/否", "type.date": "日期", "type.object": "对象", "type.array": "数组", "types": {"text": "文本", "number": "数字", "boolean": "是/否", "date": "日期", "select": "选择框", "object": "对象", "array": "数组", "string": "文本"}, "name": "字段名称", "label": "标签", "placeholder": "占位符", "defaultValue": "默认值", "options": "选项", "required": "必填", "validation": {"minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "min": "最小值", "max": "最大值"}, "form": {"componentRequired": "请选择组件类型", "labelRequired": "请输入标签", "typeRequired": "请选择数据类型", "idRequired": "请输入字段标识符名称", "labelPlaceholder": "输入显示标签", "descriptionPlaceholder": "输入此字段的描述", "placeholderPlaceholder": "输入占位符", "defaultValuePlaceholder": "输入默认值", "optionsPlaceholder": "输入选项，用逗号分隔或JSON格式", "selectOptionsPlaceholder": "输入格式：名称|值，每对值一行。例如：\na|1\nb|2", "booleanDefaultPlaceholder": "选择默认值", "dateDefaultPlaceholder": "选择默认日期", "description": "描述", "labelTagRequired": "请至少添加一个标签", "fieldIdLabel": "字段标识符名称", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "字段显示名称", "displayNamePlaceholder": "输入此字段的显示名称", "displayNameRequired": "请输入字段显示名称", "labelInputPlaceholder": "输入标签并按回车", "tagsCount": "个标签已添加", "patternSuggestions": "常用模式建议：", "defaultValue": "默认值", "minLength": "最小长度", "maxLength": "最大长度", "minValue": "最小值", "maxValue": "最大值", "pattern": "模式", "options": "选项", "min": "最小值", "max": "最大值", "placeholder": "占位符", "required": "必填", "labels": "标签", "showAdvancedSettings": "显示高级设置"}, "createSuccess": "自定义字段创建成功", "createError": "创建自定义字段时出错", "updateSuccess": "自定义字段更新成功", "updateError": "更新自定义字段时出错", "deleteSuccess": "自定义字段删除成功", "deleteError": "删除自定义字段时出错", "loadError": "加载自定义字段时出错", "booleanValues": {"true": "是", "false": "否"}, "patterns": {"email": "电子邮件", "phoneVN": "越南电话", "phoneIntl": "国际电话", "postalCodeVN": "越南邮政编码", "lettersOnly": "仅字母", "numbersOnly": "仅数字", "alphanumeric": "字母和数字", "noSpecialChars": "无特殊字符", "url": "URL", "ipv4": "IPv4", "strongPassword": "强密码", "vietnameseName": "越南姓名", "studentId": "学生ID", "nationalId": "身份证", "taxCode": "税号", "dateFormat": "日期 (dd/mm/yyyy)", "timeFormat": "时间 (hh:mm)", "hexColor": "十六进制颜色", "base64": "Base64", "uuid": "UUID", "filename": "文件名", "urlSlug": "URL Slug", "variableName": "变量名", "creditCard": "信用卡号", "qrCode": "二维码", "gpsCoordinate": "GPS坐标", "rgbColor": "RGB颜色", "domain": "域名", "decimal": "小数", "barcode": "条形码"}, "confirmDeleteMessage": "您确定要删除此自定义字段吗？", "confirmBulkDeleteMessage": "您确定要删除{{count}}个选中的自定义字段吗？", "bulkDeleteSuccess": "成功删除{{count}}个自定义字段", "bulkDeleteError": "删除自定义字段时发生错误", "selectedItems": "已选择{{count}}项", "totalFields": "自定义字段总数", "manage": "管理自定义字段", "noDescription": "无描述"}, "reports": {"title": "报告", "description": "查看营销报告和统计数据", "overview": "概览", "campaigns": "活动", "audiences": "受众", "segments": "细分", "performance": "性能", "period": "时间段", "exportData": "导出数据", "metrics": {"totalCampaigns": "活动总数", "activeCampaigns": "活跃活动", "totalAudiences": "受众总数", "totalSegments": "细分总数", "conversionRate": "转化率", "engagementRate": "参与率"}, "periods": {"today": "今天", "yesterday": "昨天", "thisWeek": "本周", "lastWeek": "上周", "thisMonth": "本月", "lastMonth": "上月", "custom": "自定义"}}, "templateEmail": {"title": "电子邮件模板管理", "description": "创建和管理电子邮件模板", "manage": "管理电子邮件模板", "addNew": "添加新电子邮件模板", "edit": "编辑电子邮件模板", "detail": "电子邮件模板详情", "name": "模板名称", "subject": "电子邮件主题", "content": "电子邮件内容", "type": "模板类型", "status": "状态", "confirmDelete": "您确定要删除此电子邮件模板吗？", "types": {"welcome": "欢迎", "newsletter": "新闻通讯", "promotion": "促销", "notification": "通知", "transactional": "交易", "custom": "自定义"}, "statuses": {"active": "活跃", "inactive": "不活跃", "draft": "草稿"}, "form": {"nameLabel": "模板名称", "namePlaceholder": "输入模板名称", "descriptionLabel": "描述", "descriptionPlaceholder": "输入模板描述", "subjectLabel": "电子邮件主题", "subjectPlaceholder": "输入电子邮件主题", "contentLabel": "电子邮件内容", "contentPlaceholder": "输入电子邮件内容", "typeLabel": "模板类型", "statusLabel": "状态"}}, "emailMarketing": {"title": "电子邮件营销", "description": "管理电子邮件活动和模板", "totalTemplates": "模板总数", "manage": "管理电子邮件"}, "smsMarketing": {"title": "短信", "description": "发送和管理短信活动", "totalCampaigns": "活动总数", "manage": "管理短信", "comingSoon": "短信营销功能正在开发中。请稍后再试！"}, "googleAds": {"title": "Google Ads", "description": "从系统集成和管理Google Ads活动", "totalAccounts": "账户", "manage": "管理Google Ads", "comingSoon": "功能正在开发中。请稍后再试！"}, "articles": {"title": "文章管理", "description": "创建和管理营销文章", "list": "文章列表", "listDescription": "管理您的所有营销文章", "create": "创建文章", "edit": "编辑文章", "update": "更新文章", "publish": "发布", "archive": "归档", "articleTitle": "标题", "titlePlaceholder": "输入文章标题", "excerpt": "摘要", "excerptPlaceholder": "输入简短摘要", "author": "作者", "authorPlaceholder": "输入作者姓名", "content": "内容", "contentPlaceholder": "输入文章内容", "imageUrl": "特色图片", "imageUrlPlaceholder": "输入图片URL或上传", "tags": "标签", "tagsPlaceholder": "输入标签并按回车", "relatedArticles": "相关文章", "selectRelatedArticles": "选择最多6篇相关文章", "basicInfo": "基本信息", "metadata": "附加信息", "stats": "统计", "views": "浏览量", "likes": "点赞数", "status": {"draft": "草稿", "published": "已发布", "archived": "已归档"}, "selectStatus": "选择状态", "deleteConfirm": "删除文章", "deleteDescription": "您确定要删除这篇文章吗？", "bulkDeleteConfirm": "删除{{count}}篇文章", "bulkDeleteDescription": "您确定要删除选中的文章吗？", "validation": {"titleRequired": "标题是必填项", "authorRequired": "作者是必填项", "contentRequired": "内容是必填项", "maxRelatedArticles": "最多6篇相关文章"}}, "videos": {"title": "视频管理", "description": "创建和管理营销视频", "list": "视频列表", "listDescription": "管理您的所有营销视频", "create": "创建视频", "edit": "编辑视频", "update": "更新视频", "publish": "发布", "archive": "归档", "videoTitle": "标题", "titlePlaceholder": "输入视频标题", "videoDescription": "描述", "descriptionPlaceholder": "输入视频描述", "url": "视频URL", "urlPlaceholder": "输入视频URL", "thumbnailUrl": "缩略图", "thumbnailUrlPlaceholder": "输入缩略图URL", "duration": "时长", "durationDescription": "格式：5:30 或 330（秒）", "selectCategory": "选择分类", "tags": "标签", "tagsPlaceholder": "输入标签并按回车", "basicInfo": "基本信息", "videoDetails": "视频详情", "metadata": "附加信息", "stats": "统计", "views": "浏览量", "likes": "点赞数", "status": {"draft": "草稿", "published": "已发布", "archived": "已归档"}, "category": {"tutorial": "教程", "productDemo": "产品演示", "testimonial": "推荐", "promotional": "推广", "educational": "教育", "entertainment": "娱乐"}, "selectStatus": "选择状态", "deleteConfirm": "删除视频", "deleteDescription": "您确定要删除这个视频吗？", "bulkDeleteConfirm": "删除{{count}}个视频", "bulkDeleteDescription": "您确定要删除选中的视频吗？", "validation": {"titleRequired": "标题是必填项", "urlRequired": "视频URL是必填项", "invalidUrl": "无效的URL", "invalidDuration": "无效的时长"}}, "email": {"title": "邮件营销", "description": "管理邮件营销活动和模板", "overview": {"title": "邮件营销", "description": "管理邮件营销活动和模板", "createCampaign": "创建活动", "manageTemplates": "管理模板", "viewReports": "查看报告", "stats": {"totalCampaigns": "总活动数", "activeCampaigns": "进行中", "totalTemplates": "总模板数", "readyToUse": "可使用", "emailsSent": "已发送邮件", "thisMonth": "本月", "openRate": "打开率", "averageRate": "平均率"}, "quickActions": {"createCampaign": "创建邮件活动", "createCampaignDesc": "开始新的邮件营销活动", "manageTemplates": "管理模板", "manageTemplatesDesc": "创建和编辑邮件模板", "viewAnalytics": "查看分析", "viewAnalyticsDesc": "跟踪邮件活动效果"}, "recentActivity": {"title": "最近活动", "viewAll": "查看全部", "campaignSent": "活动已发送", "templateCreated": "模板已创建", "reportGenerated": "报告已生成"}, "gettingStarted": {"title": "开始邮件营销", "description": "创建您的第一个邮件模板来开始营销活动", "createTemplate": "创建第一个模板"}}, "templates": {"title": "邮件模板", "description": "创建和管理邮件营销模板", "createTemplate": "创建模板", "stats": {"totalTemplates": "总模板数", "newTemplates": "+3 个新模板", "active": "活跃", "readyToUse": "可使用", "draft": "草稿", "incomplete": "未完成", "testSent": "已发送测试", "thisWeek": "本周"}, "table": {"template": "模板", "type": "类型", "status": "状态", "tags": "标签", "variables": "变量", "updated": "更新时间", "actions": "操作"}, "status": {"active": "活跃", "draft": "草稿", "archived": "已归档"}, "preview": {"title": "模板预览", "variables": "模板变量：", "required": "必填"}, "create": {"title": "创建邮件模板", "description": "创建新的邮件营销模板"}, "edit": {"title": "编辑邮件模板", "description": "编辑现有的邮件营销模板"}, "form": {"editors": {"title": "选择编辑器类型", "emailBuilder": {"name": "邮件构建器", "description": "可视化拖拽编辑器"}, "richTextEditor": {"name": "富文本编辑器", "description": "所见即所得文本编辑器"}, "code": {"name": "HTML代码", "description": "原始HTML编辑器"}}, "richTextEditor": {"label": "富文本内容", "placeholder": "输入邮件内容..."}, "basicInfo": {"title": "基本信息"}, "name": {"label": "模板名称", "placeholder": "例如：1月通讯，黑色星期五促销..."}, "type": {"label": "模板类型", "placeholder": "选择模板类型"}, "subject": {"label": "邮件主题", "placeholder": "例如：🎉 专为您准备的特别优惠！"}, "content": {"title": "邮件内容", "designMode": "设计", "codeMode": "HTML"}, "htmlContent": {"label": "HTML内容", "placeholder": "<!DOCTYPE html>\\n<html>\\n<head>\\n  <title>邮件模板</title>\\n</head>\\n<body>\\n  <h1>您好 {customer_name}！</h1>\\n  <p>感谢您订阅我们的通讯。</p>\\n</body>\\n</html>"}, "textContent": {"label": "文本内容（可选）", "placeholder": "您好 {customer_name}！\\n\\n感谢您订阅我们的通讯..."}, "variables": {"title": "变量", "addButton": "添加变量", "name": "变量名", "type": "类型", "typePlaceholder": "选择类型", "defaultValue": "默认值", "defaultValuePlaceholder": "客户", "description": "描述", "descriptionPlaceholder": "客户姓名"}, "tags": {"title": "标签", "label": "标签", "placeholder": "输入标签并按回车"}, "submitButton": "创建模板", "instructions": {"title": "邮件模板创建指南", "description": "使用邮件构建器创建美观的邮件内容。使用 {variable_name} 语法添加动态变量。预览文本将在收件箱预览中显示。"}, "validation": {"nameRequired": "模板名称是必填的", "subjectRequired": "邮件主题是必填的", "contentRequired": "HTML内容是必填的"}}}, "campaigns": {"syncStatus": {"tooltip": "同步状态"}, "form": {"name": {"label": "活动名称", "placeholder": "例如：黑色星期五2024促销"}, "description": {"label": "活动描述", "placeholder": "简要描述此活动..."}, "audience": {"totalCount": "总收件人数：", "calculating": "计算中...", "table": {"title": "收件人列表", "subtitle": "来自所选细分的受众列表", "selectedSubtitle": "已选择的受众列表", "name": "姓名", "email": "邮箱", "phone": "电话号码", "createdAt": "创建时间", "noName": "无姓名", "noEmail": "无邮箱", "noPhone": "无电话"}}}, "customer": {"table": {"title": "客户列表", "subtitle": "已选择的客户列表", "name": "姓名", "email": "邮箱", "phone": "电话号码", "noName": "无姓名", "noEmail": "无邮箱", "noPhone": "无电话"}}}, "facebook": {"title": "Facebook营销", "description": "管理所有Facebook服务", "pages": {"title": "Facebook页面管理", "description": "管理和监控您的企业Facebook页面", "name": "页面名称", "followers": "关注者", "likes": "点赞数", "engagement": "互动", "insights": "洞察", "managePosts": "管理帖子", "connect": "连接Facebook页面"}, "ads": {"title": "Facebook广告", "description": "管理和优化Facebook广告活动"}, "analytics": {"title": "Facebook分析", "description": "分析Facebook广告和页面效果"}, "instagram": {"title": "Instagram商业", "description": "管理Instagram商业账户"}, "pixel": {"title": "Facebook像素", "description": "跟踪和分析网站"}, "messenger": {"title": "Facebook Messenger", "description": "消息和聊天机器人"}, "pageMetadata": {"title": "页面公共元数据", "description": "分析Facebook页面的公共数据", "searchPlaceholder": "搜索Facebook页面...", "searchButton": "搜索", "noResults": "未找到页面", "loading": "搜索中...", "pageInfo": {"title": "页面信息", "name": "页面名称", "category": "类别", "description": "描述", "website": "网站", "phone": "电话", "email": "邮箱", "address": "地址", "founded": "成立时间", "verified": "已验证", "checkmark": "蓝色认证"}, "engagement": {"title": "互动指标", "likes": "点赞数", "followers": "关注者", "checkins": "签到数", "talkingAbout": "正在讨论", "posts": "帖子", "photos": "照片", "videos": "视频", "events": "活动"}, "analytics": {"title": "详细分析", "overview": "概览", "comparison": "对比", "insights": "洞察", "trends": "趋势", "demographics": "人口统计", "performance": "表现", "competitorAnalysis": "竞争对手分析"}, "comparison": {"title": "页面对比", "addPage": "添加页面进行对比", "removePage": "移除页面", "compareMetrics": "对比指标", "exportReport": "导出报告", "noPages": "没有页面可对比"}, "insights": {"title": "洞察报告", "generateReport": "生成报告", "downloadReport": "下载报告", "shareReport": "分享报告", "reportType": "报告类型", "dateRange": "日期范围", "metrics": "指标", "summary": "摘要", "recommendations": "建议"}, "permissions": {"title": "访问权限", "description": "此功能需要Facebook的页面公共元数据访问权限", "requestPermission": "请求权限", "permissionGranted": "已授权", "permissionPending": "待审核", "permissionDenied": "被拒绝"}, "demo": {"title": "功能演示", "description": "探索Facebook页面分析功能", "samplePages": "示例页面", "tryFeature": "试用功能"}}, "connection": {"status": "连接状态"}}, "verified": "已验证", "status": {"active": "活跃", "inactive": "不活跃", "connected": "已连接"}, "analytics": {"name": "名称", "impressions": "展示次数", "reach": "覆盖人数", "clicks": "点击次数", "spend": "花费", "conversions": "转化次数", "performance": "效果", "efficiency": "效率", "detailedReport": "详细报告", "export": "导出", "breakdown": "详细分析", "exportAll": "导出全部", "refresh": "刷新", "filters": "筛选器", "detailedAnalytics": "详细分析", "performanceTrends": "效果趋势", "totalImpressions": "总展示次数", "totalReach": "总覆盖人数", "totalClicks": "总点击次数", "totalSpend": "总花费", "totalConversions": "总转化次数", "avgCTR": "平均CTR", "avgCPC": "平均CPC", "avgROAS": "平均ROAS", "dateRange": {"title": "日期范围", "today": "今天", "yesterday": "昨天", "last7days": "过去7天", "last30days": "过去30天", "thisMonth": "本月", "lastMonth": "上月", "custom": "自定义"}, "metrics": {"title": "指标", "all": "全部", "performance": "效果", "engagement": "互动", "conversion": "转化", "cost": "成本"}}, "performanceOverview": "效果概览", "quickActions": "快速操作", "recentCampaigns": "最近活动", "conversions": "转化", "actions": {"createCampaign": "创建活动", "createCampaignDesc": "创建新的广告活动", "manageAudiences": "管理受众", "manageAudiencesDesc": "创建和管理客户受众", "viewAnalytics": "查看分析", "viewAnalyticsDesc": "跟踪活动效果", "adAccountSettings": "账户设置", "adAccountSettingsDesc": "管理广告账户设置"}, "metrics": {"totalSpend": "总花费", "impressions": "展示次数", "clicks": "点击次数", "conversions": "转化次数", "avgCTR": "平均CTR", "avgCPC": "平均CPC", "avgROAS": "平均ROAS"}, "facebookAds": {"analytics": {"title": "Facebook广告分析", "description": "跟踪效果并优化广告活动", "overview": "效果概览", "topCampaigns": "高效果活动", "export": "导出报告", "chartPlaceholder": "效果图表", "chartDescription": "集成图表库后将在此显示详细图表", "totalSpend": "总花费", "totalSpendDesc": "与上期相比", "impressions": "展示次数", "impressionsDesc": "广告总展示次数", "clicks": "点击次数", "clicksDesc": "广告总点击次数", "ctr": "点击率(CTR)", "ctrDesc": "点击次数 / 展示次数", "cpc": "每次点击费用(CPC)", "cpcDesc": "花费 / 点击次数", "conversions": "转化次数", "conversionsDesc": "总转化次数", "roas": "广告支出回报率", "roasDesc": "广告支出回报率", "reach": "覆盖人数", "reachDesc": "覆盖的人数", "periods": {"1d": "今天", "7d": "过去7天", "30d": "过去30天", "90d": "过去90天", "custom": "自定义"}}, "metrics": {"spend": "花费", "clicks": "点击"}}}, "seedingGroups": {"form": {"create": {"title": "配置Seeding Group"}}}}