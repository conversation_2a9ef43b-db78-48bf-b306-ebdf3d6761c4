// Types for Zalo Personal Campaigns API

/**
 * Enum cho loại chiến dịch Zalo Personal
 */
export enum ZaloPersonalCampaignType {
  SEND_ALL = 'send_all',
  CRAWL_FRIENDS = 'crawl_friends',
  CRAWL_GROUPS = 'crawl_groups',
  SEND_FRIEND_REQUEST = 'send_friend_request',
}

/**
 * Enum cho trạng thái chiến dịch
 */
export enum ZaloPersonalCampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

/**
 * Message type enum
 */
export enum ZaloPersonalMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  QR_CODE = 'qr_code',
}

/**
 * Content structure for campaign messages
 */
export interface ZaloPersonalCampaignContent {
  text?: string;
  imageBase64?: string;
  qrCodeBase64?: string;
  caption?: string;
}

/**
 * DTO chiến dịch <PERSON>alo Personal - Updated to match API response
 */
export interface ZaloPersonalCampaignDto {
  id: number;
  userId: number;
  integrationId: string;
  name: string;
  description?: string;
  type: ZaloPersonalCampaignType;
  messageType: ZaloPersonalMessageType;
  content: ZaloPersonalCampaignContent;
  recipientList: string[];
  segmentId?: number;
  audienceIds: string[];
  status: ZaloPersonalCampaignStatus;
  errorMessage?: string;
  scheduledAt?: number;
  startedAt?: number;
  completedAt?: number;
  cancelledAt?: number;
  totalRecipients: number;
  successCount: number;
  failureCount: number;
  jobIds: string[];
  messageDelay: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * Query parameters cho danh sách chiến dịch - Updated to match API
 */
export interface ZaloPersonalCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  integrationId?: string;
  status?: ZaloPersonalCampaignStatus;
  messageType?: ZaloPersonalMessageType;
}

/**
 * Response DTO cho danh sách chiến dịch - Updated to match API
 */
export interface ZaloPersonalCampaignResponseDto {
  code: number;
  message: string;
  result: {
    items: ZaloPersonalCampaignDto[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };
  };
}

/**
 * DTO tạo chiến dịch mới - Updated to match API structure
 */
export interface CreateZaloPersonalCampaignDto {
  integrationId: string;
  name: string;
  description?: string;
  messageType: ZaloPersonalMessageType;
  content: ZaloPersonalCampaignContent;
  recipientList: string[];
  segmentId?: number;
  audienceIds?: string[];
  scheduledAt?: number;
  messageDelay?: number;
}

/**
 * DTO cập nhật chiến dịch - Updated to match API structure
 */
export interface UpdateZaloPersonalCampaignDto {
  name?: string;
  description?: string;
  status?: ZaloPersonalCampaignStatus;
  messageType?: ZaloPersonalMessageType;
  content?: ZaloPersonalCampaignContent;
  recipientList?: string[];
  segmentId?: number;
  audienceIds?: string[];
  scheduledAt?: number;
  messageDelay?: number;
}

/**
 * DTO tạo chiến dịch Crawl Friends List - API specification
 */
export interface CreateCrawlFriendsListCampaignDto {
  integrationId: string;
  campaignType: 'crawl_friends';
  name: string;
  description?: string;
  headless?: boolean;
  delayBetweenRequests: number;
}

/**
 * DTO tạo chiến dịch Crawl Groups - API specification
 */
export interface CreateCrawlGroupsCampaignDto {
  integrationId: string;
  campaignType: 'crawl_groups';
  name: string;
  description?: string;
  headless?: boolean;
  delayBetweenRequests: number;
}

/**
 * Labels cho các loại chiến dịch (deprecated - sử dụng getCampaignTypeLabel thay thế)
 */
export const ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS = {
  [ZaloPersonalCampaignType.SEND_ALL]: 'Gửi tin nhắn hàng loạt',
  [ZaloPersonalCampaignType.CRAWL_FRIENDS]: 'Craw danh sách bạn bè',
  [ZaloPersonalCampaignType.CRAWL_GROUPS]: 'Craw danh sách nhóm',
  [ZaloPersonalCampaignType.SEND_FRIEND_REQUEST]: 'Gửi lời mời kết bạn',
} as const;

/**
 * Lấy label loại chiến dịch theo ngôn ngữ
 */
export const getCampaignTypeLabel = (type: ZaloPersonalCampaignType, t: any): string => {
  const typeKeys = {
    [ZaloPersonalCampaignType.SEND_ALL]: 'marketing:zalo.personalCampaigns.campaignTypes.sendAll',
    [ZaloPersonalCampaignType.CRAWL_FRIENDS]: 'marketing:zalo.personalCampaigns.campaignTypes.crawlFriends',
    [ZaloPersonalCampaignType.CRAWL_GROUPS]: 'marketing:zalo.personalCampaigns.campaignTypes.crawlGroups',
    [ZaloPersonalCampaignType.SEND_FRIEND_REQUEST]: 'marketing:zalo.personalCampaigns.campaignTypes.sendFriendRequest',
  };

  const fallbacks = {
    [ZaloPersonalCampaignType.SEND_ALL]: 'Gửi tin nhắn hàng loạt',
    [ZaloPersonalCampaignType.CRAWL_FRIENDS]: 'Craw danh sách bạn bè',
    [ZaloPersonalCampaignType.CRAWL_GROUPS]: 'Craw danh sách nhóm',
    [ZaloPersonalCampaignType.SEND_FRIEND_REQUEST]: 'Gửi lời mời kết bạn',
  };

  return t(typeKeys[type], fallbacks[type]);
};

/**
 * Labels cho trạng thái chiến dịch (deprecated - sử dụng getStatusLabel thay thế)
 */
export const ZALO_PERSONAL_CAMPAIGN_STATUS_LABELS = {
  [ZaloPersonalCampaignStatus.DRAFT]: 'Nháp',
  [ZaloPersonalCampaignStatus.SCHEDULED]: 'Đã lên lịch',
  [ZaloPersonalCampaignStatus.ACTIVE]: 'Đang chạy',
  [ZaloPersonalCampaignStatus.PAUSED]: 'Tạm dừng',
  [ZaloPersonalCampaignStatus.COMPLETED]: 'Hoàn thành',
  [ZaloPersonalCampaignStatus.FAILED]: 'Thất bại',
} as const;

/**
 * Lấy label trạng thái theo ngôn ngữ
 */
export const getStatusLabel = (status: ZaloPersonalCampaignStatus, t: any): string => {
  const statusKeys = {
    [ZaloPersonalCampaignStatus.DRAFT]: 'marketing:zalo.personalCampaigns.status.draft',
    [ZaloPersonalCampaignStatus.SCHEDULED]: 'marketing:zalo.personalCampaigns.status.scheduled',
    [ZaloPersonalCampaignStatus.ACTIVE]: 'marketing:zalo.personalCampaigns.status.active',
    [ZaloPersonalCampaignStatus.PAUSED]: 'marketing:zalo.personalCampaigns.status.paused',
    [ZaloPersonalCampaignStatus.COMPLETED]: 'marketing:zalo.personalCampaigns.status.completed',
    [ZaloPersonalCampaignStatus.FAILED]: 'marketing:zalo.personalCampaigns.status.failed',
  };

  const fallbacks = {
    [ZaloPersonalCampaignStatus.DRAFT]: 'Nháp',
    [ZaloPersonalCampaignStatus.SCHEDULED]: 'Đã lên lịch',
    [ZaloPersonalCampaignStatus.ACTIVE]: 'Đang chạy',
    [ZaloPersonalCampaignStatus.PAUSED]: 'Tạm dừng',
    [ZaloPersonalCampaignStatus.COMPLETED]: 'Hoàn thành',
    [ZaloPersonalCampaignStatus.FAILED]: 'Thất bại',
  };

  return t(statusKeys[status], fallbacks[status]);
};

/**
 * Màu sắc cho trạng thái chiến dịch
 */
export const ZALO_PERSONAL_CAMPAIGN_STATUS_COLORS = {
  [ZaloPersonalCampaignStatus.DRAFT]: 'gray',
  [ZaloPersonalCampaignStatus.SCHEDULED]: 'orange',
  [ZaloPersonalCampaignStatus.ACTIVE]: 'green',
  [ZaloPersonalCampaignStatus.PAUSED]: 'yellow',
  [ZaloPersonalCampaignStatus.COMPLETED]: 'blue',
  [ZaloPersonalCampaignStatus.FAILED]: 'red',
} as const;
