<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Click Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .modal-backdrop {
            position: fixed;
            inset: 0;
            z-index: 9500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .modal-body {
            padding: 24px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            position: relative;
            z-index: 10;
        }
        .template-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            z-index: 20;
            pointer-events: auto;
        }
        .template-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            transform: translateY(-2px);
        }
        .template-card.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .template-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }
        .template-icon {
            width: 48px;
            height: 48px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        .template-title {
            font-size: 18px;
            font-weight: 600;
            margin: 8px 0 4px 0;
            color: #1f2937;
        }
        .template-description {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.4;
        }
        .debug-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1f2937;
            color: white;
            padding: 16px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            z-index: 10000;
        }
        .close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }
        .close-btn:hover {
            color: #1f2937;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        <div>Debug Info:</div>
        <div id="debugLog">Ready...</div>
    </div>

    <div class="modal-backdrop" id="modalBackdrop">
        <div class="modal-content" id="modalContent">
            <div class="modal-header">
                <h2>Template Selector Debug</h2>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>Click on any template card below. Check the debug info in the top-right corner.</p>
                
                <div class="grid" id="templateGrid">
                    <!-- Templates will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        const templates = [
            {
                id: 'newsletter',
                name: 'Newsletter Cơ Bản',
                description: 'Template newsletter đơn giản với header, nội dung chính và footer',
                icon: '📧'
            },
            {
                id: 'promotion',
                name: 'Khuyến Mãi Đặc Biệt',
                description: 'Template thông báo khuyến mãi với thiết kế bắt mắt',
                icon: '🎉'
            },
            {
                id: 'welcome',
                name: 'Chào Mừng Thành Viên Mới',
                description: 'Template chào mừng người dùng mới đăng ký',
                icon: '👋'
            }
        ];

        let selectedTemplate = null;
        let debugLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`${timestamp}: ${message}`);
            if (debugLog.length > 10) debugLog.shift();
            
            const debugElement = document.getElementById('debugLog');
            debugElement.innerHTML = debugLog.join('<br>');
            console.log(message);
        }

        function renderTemplates() {
            const grid = document.getElementById('templateGrid');
            grid.innerHTML = '';
            
            templates.forEach(template => {
                const card = document.createElement('button');
                card.type = 'button';
                card.className = `template-card ${selectedTemplate?.id === template.id ? 'selected' : ''}`;
                
                // Add multiple event listeners for debugging
                card.addEventListener('mousedown', (e) => {
                    log(`MouseDown: ${template.name}`);
                });
                
                card.addEventListener('mouseup', (e) => {
                    log(`MouseUp: ${template.name}`);
                });
                
                card.addEventListener('click', (e) => {
                    log(`Click: ${template.name}`);
                    e.preventDefault();
                    e.stopPropagation();
                    selectTemplate(template);
                });
                
                card.innerHTML = `
                    <div class="template-header">
                        <div class="template-icon">${template.icon}</div>
                    </div>
                    <div class="template-title">${template.name}</div>
                    <div class="template-description">${template.description}</div>
                `;
                
                grid.appendChild(card);
            });
        }

        function selectTemplate(template) {
            log(`Template selected: ${template.name}`);
            selectedTemplate = template;
            renderTemplates();
        }

        function closeModal() {
            log('Modal close requested');
            document.getElementById('modalBackdrop').style.display = 'none';
        }

        // Add backdrop click handler
        document.getElementById('modalBackdrop').addEventListener('click', (e) => {
            if (e.target === document.getElementById('modalBackdrop')) {
                log('Backdrop clicked');
                // Don't close modal for debugging
            }
        });

        // Add modal content click handler to prevent propagation
        document.getElementById('modalContent').addEventListener('click', (e) => {
            log('Modal content clicked');
            e.stopPropagation();
        });

        // Initialize
        log('Page loaded');
        renderTemplates();
    </script>
</body>
</html>
