import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Controller, useFormContext, FieldValues, SubmitHandler } from 'react-hook-form';
import { Card, Form, FormItem, Input, IconCard, Textarea } from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { getZaloPersonalIntegrations } from '../../../api/zalo/zaloPersonalApi';
import { useCreateCrawlFriendsCampaign } from '../../../hooks/zalo/useZaloPersonalCampaigns';
import { useSmartNotification } from '@/shared';
import type { ZaloPersonalIntegrationQueryDto } from '../../../types/zaloPersonal';
import type { CreateCrawlFriendsListCampaignDto } from '../../../types/zaloPersonalCampaign';
import type { SelectOption } from '@/shared/components/common/Select/Select';

// Form data interface - Updated to match API specification
type CrawFriendsListFormData = CreateCrawlFriendsListCampaignDto;

// Form validation schema - Updated to match API specification
const crawFriendsListFormSchema = z.object({
  integrationId: z.string().min(1, 'Vui lòng chọn tài khoản'),
  campaignType: z.literal('crawl_friends'),
  name: z.string().min(1, 'Tên chiến dịch là bắt buộc'),
  description: z.string().optional(),
  headless: z.boolean().optional().default(true),
  delayBetweenRequests: z.number().min(1, 'Thời gian delay phải lớn hơn 0').max(60, 'Thời gian delay không được vượt quá 60 giây'),
});

interface CrawFriendsListFormProps {
  onSubmit: (data: CrawFriendsListFormData) => void;
  onCancel?: () => void;
}

// Wrapper component cho AsyncSelectWithPagination để tích hợp với React Hook Form
const AsyncSelectFormField: React.FC<{
  name: string;
  loadOptions: any;
  placeholder?: string;
  multiple?: boolean;
  fullWidth?: boolean;
  itemsPerPage?: number;
  debounceTime?: number;
}> = ({ name, loadOptions, placeholder, multiple, fullWidth, itemsPerPage, debounceTime }) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <AsyncSelectWithPagination
          value={value}
          onChange={onChange}
          loadOptions={loadOptions}
          placeholder={placeholder}
          multiple={multiple}
          fullWidth={fullWidth}
          itemsPerPage={itemsPerPage}
          debounceTime={debounceTime}
          error={error?.message}
        />
      )}
    />
  );
};

const CrawFriendsListForm: React.FC<CrawFriendsListFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success, error } = useSmartNotification();
  const createCrawlFriendsCampaign = useCreateCrawlFriendsCampaign();

  const defaultValues: CrawFriendsListFormData = {
    integrationId: '',
    campaignType: 'crawl_friends' as const,
    name: '',
    description: '',
    headless: true,
    delayBetweenRequests: 3,
  };

  // Load options function for AsyncSelectWithPagination
  const loadAccountOptions = async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      const queryParams: ZaloPersonalIntegrationQueryDto = {
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
      };

      // Sử dụng API service thay vì fetch trực tiếp
      const response = await getZaloPersonalIntegrations(queryParams);

      // Transform data to SelectOption format
      const items: SelectOption[] = (response.result?.items || []).map(item => ({
        value: item.id,
        label: item.integrationName || item.metadata?.profile?.name || `Tài khoản ${item.id}`,
        data: item as unknown as Record<string, unknown>,
      }));

      return {
        items,
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || 1,
        hasMore:
          (response.result?.meta?.currentPage || 1) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading account options:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }
  };

  const handleSubmit: SubmitHandler<FieldValues> = async (data) => {
    try {
      const formData = data as CrawFriendsListFormData;

      // Call API to create crawl friends campaign
      await createCrawlFriendsCampaign.mutateAsync(formData);

      success({
        message: t('marketing:zalo.personalCampaigns.forms.crawFriendsList.createSuccess', 'Tạo chiến dịch thành công!'),
      });

      // Call parent onSubmit callback
      onSubmit(formData);
    } catch (err) {
      console.error('Error creating crawl friends campaign:', err);
      error({
        message: t('marketing:zalo.personalCampaigns.forms.crawFriendsList.createError', 'Có lỗi xảy ra khi tạo chiến dịch'),
      });
    }
  };

  return (
    <Card>
      <Form
        schema={crawFriendsListFormSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
      >
        <div className="space-y-6">
          {/* Campaign Name */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawFriendsList.campaignName',
              'Tên chiến dịch'
            )}
            name="name"
            required
          >
            <Input
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawFriendsList.campaignNamePlaceholder',
                'Nhập tên chiến dịch craw danh sách bạn bè'
              )}
              fullWidth
            />
          </FormItem>

          {/* Description */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawFriendsList.description',
              'Mô tả chiến dịch'
            )}
            name="description"
          >
            <Textarea
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawFriendsList.descriptionPlaceholder',
                'Nhập mô tả cho chiến dịch craw danh sách bạn bè'
              )}
              fullWidth
              rows={3}
            />
          </FormItem>

          {/* Account Selection */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawFriendsList.account',
              'Tài khoản Zalo'
            )}
            name="integrationId"
            required
          >
            <AsyncSelectFormField
              name="integrationId"
              loadOptions={loadAccountOptions}
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawFriendsList.accountPlaceholder',
                'Chọn tài khoản Zalo để craw danh sách bạn bè'
              )}
              multiple={false}
              fullWidth
              itemsPerPage={10}
              debounceTime={300}
            />
          </FormItem>

          {/* Delay Between Requests */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawFriendsList.delayBetweenRequests',
              'Thời gian delay giữa các request (giây)'
            )}
            name="delayBetweenRequests"
            required
          >
            <Input
              type="number"
              min={1}
              max={60}
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawFriendsList.delayPlaceholder',
                'Nhập thời gian delay (1-60 giây)'
              )}
              fullWidth
            />
          </FormItem>

          {/* Action IconCards */}
          <div className="flex justify-end space-x-3 pt-4">
            {onCancel && (
              <IconCard
                icon="x"
                variant="secondary"
                size="md"
                title={t('common:cancel', 'Hủy')}
                onClick={onCancel}
              />
            )}
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              type="submit"
              title={t('marketing:zalo.personalCampaigns.forms.crawFriendsList.submit', 'Tạo chiến dịch')}
              isLoading={createCrawlFriendsCampaign.isPending}
              disabled={createCrawlFriendsCampaign.isPending}
            />
          </div>
        </div>
      </Form>
    </Card>
  );
};

export default CrawFriendsListForm;
