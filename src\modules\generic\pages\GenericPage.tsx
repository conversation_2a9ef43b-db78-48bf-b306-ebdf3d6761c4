/**
 * Generic Page
 * <PERSON><PERSON> hiển thị widgets ở chế độ siêu tối giản với WebSocket real-time sync
 */

import React, { useEffect, useState, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import GenericWorkspace from '../components/GenericWorkspace';
import { useGenericWebSocket } from '../hooks/useGenericWebSocket';
import { GenericLayout, GenericWidget } from '../types';

import { Loading } from '@/shared/components/common';
import '../styles/generic.css';

const GenericPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  // Get session ID from URL params or create new one
  useEffect(() => {
    const initializeSession = async () => {
      try {
        let sessionId = searchParams.get('sessionId');
        console.log('🔍 Current sessionId from URL:', sessionId);

        if (!sessionId) {
          console.log('🆕 Creating new session...');
          // Fallback to client-side session ID since backend might not be ready
          const fallbackSessionId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          sessionId = fallbackSessionId;
          console.log('✅ Generated client session ID:', sessionId);

          // Update URL with new session ID
          setSearchParams({ sessionId }, { replace: true });
        }

        setCurrentSessionId(sessionId);
        console.log('✅ Session initialized:', sessionId);
      } catch (error) {
        console.error('❌ Failed to initialize session:', error);
        // Fallback to client-side session ID
        const fallbackSessionId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        setCurrentSessionId(fallbackSessionId);
        setSearchParams({ sessionId: fallbackSessionId }, { replace: true });
      } finally {
        setIsInitializing(false);
      }
    };

    initializeSession();
  }, [searchParams, setSearchParams]);

  // WebSocket integration
  const {
    widgets,
    layout,
    isLoading: isLoadingState,
    isConnected,
    updateLayout,
    requestSync,
  } = useGenericWebSocket({
    sessionId: currentSessionId || undefined,
    autoConnect: !!currentSessionId,
    onWidgetAdded: (widget: GenericWidget) => {
      console.log('🎉 Widget added from backend:', widget);
    },
    onWidgetRemoved: (widgetId: string) => {
      console.log('🗑️ Widget removed from backend:', widgetId);
    },
    onLayoutUpdated: (newLayout: GenericLayout[]) => {
      console.log('📐 Layout updated from backend:', newLayout);
    },
    onStateSync: (widgets: GenericWidget[], layout: GenericLayout[]) => {
      console.log('🔄 State synced from backend:', { widgets, layout });
    },
  });

  // Handle layout changes from user interaction
  const handleLayoutChange = useCallback((newLayout: GenericLayout[]) => {
    console.log('📐 Layout changed by user:', newLayout);
    updateLayout(newLayout);
  }, [updateLayout]);

  // Handle keyboard shortcuts for debugging
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle shortcuts when Ctrl/Cmd is pressed
      if (!event.ctrlKey && !event.metaKey) return;

      switch (event.key) {
        case 'r':
          // Ctrl+R: Request sync
          event.preventDefault();
          console.log('🔄 Requesting state sync...');
          requestSync();
          break;
        
        case 'i':
          // Ctrl+I: Show info
          event.preventDefault();
          console.log('ℹ️ Generic Page Info:', {
            sessionId: currentSessionId,
            isConnected,
            widgetCount: widgets.length,
            layoutItems: layout.length,
          });
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentSessionId, isConnected, widgets.length, layout.length, requestSync]);

  // Show loading while initializing
  if (isInitializing || isLoadingState) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading />
      </div>
    );
  }

  return (
    <div className="generic-page">
      {/* Connection Status Indicator (only show when disconnected) */}
      {!isConnected && (
        <div className="fixed top-2 right-2 z-50 bg-red-500 text-white px-3 py-1 rounded-md text-sm">
          Disconnected
        </div>
      )}

      {/* Debug Info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-2 left-2 z-50 bg-black/80 text-white px-2 py-1 rounded text-xs font-mono">
          Session: {currentSessionId?.slice(-8)} | Widgets: {widgets.length} | Connected: {isConnected ? '✅' : '❌'}
        </div>
      )}

      {/* Main Workspace */}
      <div className="w-full h-full p-2">
        <GenericWorkspace
          widgets={widgets}
          layout={layout}
          onLayoutChange={handleLayoutChange}
          isDraggable={false} // Disable dragging for pure view mode
          isResizable={false} // Disable resizing for pure view mode
          className="h-full"
        />
      </div>

      {/* Empty State */}
      {widgets.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center text-muted-foreground">
            <div className="text-6xl mb-4">📊</div>
            <h2 className="text-xl font-medium mb-2">No widgets to display</h2>
            <p className="text-sm">
              Widgets will appear here when added from the backend
            </p>
            <p className="text-xs mt-2 font-mono">
              Session: {currentSessionId}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default GenericPage;
