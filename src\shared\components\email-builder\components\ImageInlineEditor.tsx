import React, { useRef, useState, useEffect } from 'react';
import { Upload, Image as ImageIcon, X, AlignLeft, AlignCenter, AlignRight } from 'lucide-react';

interface ImageInlineEditorProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  borderRadius?: number;
  textAlign?: 'left' | 'center' | 'right';
  onUpdate: (updates: {
    src?: string;
    alt?: string;
    width?: number;
    height?: number;
    borderRadius?: number;
    textAlign?: 'left' | 'center' | 'right';
  }) => void;
  onClickOutside?: () => void;
  className?: string;
}

const ImageInlineEditor: React.FC<ImageInlineEditorProps> = ({
  src,
  alt,
  width = 300,
  height = 200,
  borderRadius = 0,
  textAlign = 'left',
  onUpdate,
  onClickOutside,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [localSrc, setLocalSrc] = useState(src);
  const [localAlt, setLocalAlt] = useState(alt);
  const [localWidth, setLocalWidth] = useState(width);
  const [localHeight, setLocalHeight] = useState(height);
  const [localBorderRadius, setLocalBorderRadius] = useState(borderRadius);
  const [localTextAlign, setLocalTextAlign] = useState(textAlign);

  console.log('ImageInlineEditor rendered with:', {
    src,
    alt,
    width,
    height,
    borderRadius,
    textAlign,
    hasOnClickOutside: !!onClickOutside,
  });

  useEffect(() => {
    if (!onClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        // Don't close if clicking on related elements
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.image-inline-editor') ||
            element.closest('button') ||
            element.closest('input') ||
            element.closest('select') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'INPUT');

        if (!isToolbarClick) {
          onClickOutside();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClickOutside]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        const newSrc = e.target?.result as string;
        setLocalSrc(newSrc);
        onUpdate({ src: newSrc });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleWidthChange = (newWidth: number) => {
    setLocalWidth(newWidth);
    onUpdate({ width: newWidth });
  };

  const handleHeightChange = (newHeight: number) => {
    setLocalHeight(newHeight);
    onUpdate({ height: newHeight });
  };

  const handleBorderRadiusChange = (newRadius: number) => {
    setLocalBorderRadius(newRadius);
    onUpdate({ borderRadius: newRadius });
  };

  const handleAltChange = (newAlt: string) => {
    setLocalAlt(newAlt);
    onUpdate({ alt: newAlt });
  };

  const handleTextAlignChange = (newAlign: 'left' | 'center' | 'right') => {
    setLocalTextAlign(newAlign);
    onUpdate({ textAlign: newAlign });
  };

  return (
    <div
      ref={editorRef}
      className={`image-inline-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* Upload Button */}
        <button
          className="flex items-center gap-1 px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs"
          onClick={handleUploadClick}
          title="Upload new image"
        >
          <Upload size={12} />
          Upload
        </button>

        {/* Width Input */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">W:</label>
          <input
            type="number"
            value={localWidth}
            onChange={e => handleWidthChange(Number(e.target.value))}
            className="w-16 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="50"
            max="800"
          />
        </div>

        {/* Height Input */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">H:</label>
          <input
            type="number"
            value={localHeight}
            onChange={e => handleHeightChange(Number(e.target.value))}
            className="w-16 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="50"
            max="600"
          />
        </div>

        {/* Border Radius Input */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Radius:</label>
          <input
            type="number"
            value={localBorderRadius}
            onChange={e => handleBorderRadiusChange(Number(e.target.value))}
            className="w-16 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="0"
            max="50"
          />
        </div>

        {/* Divider */}
        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500"></div>

        {/* Alignment Buttons */}
        <div className="flex items-center gap-1">
          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'left'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('left')}
            title="Align Left"
          >
            <AlignLeft size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'center'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('center')}
            title="Align Center"
          >
            <AlignCenter size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'right'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('right')}
            title="Align Right"
          >
            <AlignRight size={12} />
          </button>
        </div>
      </div>

      {/* Image Preview */}
      <div className="p-3 bg-white dark:bg-gray-800">
        <div
          className="flex"
          style={{
            justifyContent:
              localTextAlign === 'center'
                ? 'center'
                : localTextAlign === 'right'
                  ? 'flex-end'
                  : 'flex-start',
          }}
        >
          {localSrc ? (
            <img
              src={localSrc}
              alt={localAlt}
              style={{
                width: `${localWidth}px`,
                height: `${localHeight}px`,
                borderRadius: `${localBorderRadius}px`,
                objectFit: 'cover',
              }}
              className="border border-gray-200 dark:border-gray-600"
            />
          ) : (
            <div
              style={{
                width: `${localWidth}px`,
                height: `${localHeight}px`,
                borderRadius: `${localBorderRadius}px`,
              }}
              className="flex items-center justify-center bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600"
            >
              <div className="text-center text-gray-500 dark:text-gray-400">
                <ImageIcon size={24} className="mx-auto mb-1" />
                <div className="text-xs">No Image</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        className="hidden"
      />
    </div>
  );
};

export default ImageInlineEditor;
