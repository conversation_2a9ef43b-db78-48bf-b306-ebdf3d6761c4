/**
 * Simple Generic Page for testing
 */

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const SimpleGenericPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [widgets, setWidgets] = useState<any[]>([]);

  // Initialize session
  useEffect(() => {
    let currentSessionId = searchParams.get('sessionId');
    
    if (!currentSessionId) {
      currentSessionId = `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setSearchParams({ sessionId: currentSessionId }, { replace: true });
    }
    
    setSessionId(currentSessionId);
    console.log('✅ Session ID:', currentSessionId);
  }, [searchParams, setSearchParams]);

  // WebSocket connection
  useEffect(() => {
    if (!sessionId) return;

    const wsUrl = `ws://localhost:8081/ws/generic?sessionId=${sessionId}`;
    console.log('🔌 Connecting to:', wsUrl);

    const websocket = new WebSocket(wsUrl);

    websocket.onopen = () => {
      console.log('✅ WebSocket connected');
      setIsConnected(true);
    };

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📥 Received:', data);

        if (data.type === 'add_widget') {
          setWidgets(prev => [...prev, data.payload.widget]);
        } else if (data.type === 'remove_widget') {
          setWidgets(prev => prev.filter(w => w.id !== data.payload.widgetId));
        } else if (data.type === 'sync_state') {
          setWidgets(data.payload.widgets || []);
        }
      } catch (error) {
        console.error('❌ Error parsing message:', error);
      }
    };

    websocket.onclose = () => {
      console.log('🔌 WebSocket disconnected');
      setIsConnected(false);
    };

    websocket.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      setIsConnected(false);
    };

    return () => {
      websocket.close();
    };
  }, [sessionId]);

  return (
    <div className="w-full bg-background text-foreground p-4">
      <div className="mb-4">
        <h1 className="text-2xl font-bold mb-2">Simple Generic Page</h1>
        <div className="text-sm text-muted-foreground">
          Session: {sessionId} | 
          Status: {isConnected ? '✅ Connected' : '❌ Disconnected'} | 
          Widgets: {widgets.length}
        </div>
      </div>

      {widgets.length === 0 ? (
        <div className="flex items-center justify-center h-64 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="text-center">
            <div className="text-4xl mb-2">📊</div>
            <div className="text-lg font-medium">No widgets</div>
            <div className="text-sm text-muted-foreground">
              Widgets will appear here when added from backend
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {widgets.map((widget) => (
            <div
              key={widget.id}
              className="p-4 border rounded-lg bg-card"
            >
              <h3 className="font-medium mb-2">{widget.title}</h3>
              <div className="text-sm text-muted-foreground">
                Type: {widget.type}
              </div>
              {widget.props && (
                <div className="mt-2 text-xs">
                  <pre>{JSON.stringify(widget.props, null, 2)}</pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Debug info */}
      <div className="fixed bottom-4 left-4 text-xs font-mono bg-black text-white p-2 rounded">
        Session: {sessionId?.slice(-8)} | Connected: {isConnected ? '✅' : '❌'} | Widgets: {widgets.length}
      </div>
    </div>
  );
};

export default SimpleGenericPage;
