/**
 * Generic Workspace Component
 * <PERSON><PERSON><PERSON> thị widgets ở chế độ siêu tối g<PERSON>n (ultra-minimal)
 */

import React, { useMemo, useCallback } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { DashboardCard } from '@/modules/dashboard';
import { GenericWidget, GenericLayout } from '../types';
import { DashboardWidget } from '@/modules/dashboard/types';

// Layout type for react-grid-layout
interface Layout {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
}
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface GenericWorkspaceProps {
  widgets: GenericWidget[];
  layout?: GenericLayout[];
  onLayoutChange?: (layout: GenericLayout[]) => void;
  isDraggable?: boolean;
  isResizable?: boolean;
  className?: string;
}

const GenericWorkspace: React.FC<GenericWorkspaceProps> = ({
  widgets,
  layout = [],
  onLayoutChange,
  isDraggable = false,
  isResizable = false,
  className = '',
}) => {
  // Convert GenericWidget to DashboardWidget for compatibility
  const dashboardWidgets: DashboardWidget[] = useMemo(() => {
    return widgets.map(widget => ({
      ...widget,
      // Ensure all required DashboardWidget properties are present
      content: widget.content || undefined,
      isEmpty: widget.isEmpty || false,
      config: widget.config || undefined,
      props: widget.props || {},
    }));
  }, [widgets]);

  // Convert layout to react-grid-layout format
  const gridLayouts = useMemo(() => {
    const layoutMap: { [key: string]: Layout[] } = {};
    
    // Create layout for each breakpoint
    const breakpoints = ['lg', 'md', 'sm', 'xs', 'xxs'];
    
    breakpoints.forEach(breakpoint => {
      layoutMap[breakpoint] = layout.map(item => ({
        i: item.i,
        x: item.x,
        y: item.y,
        w: item.w,
        h: item.h,
        minW: item.minW,
        minH: item.minH,
        maxW: item.maxW,
        maxH: item.maxH,
      }));
    });

    return layoutMap;
  }, [layout]);

  // Handle layout change
  const handleLayoutChange = useCallback((currentLayout: Layout[]) => {
    if (!onLayoutChange) return;

    // Convert back to GenericLayout format
    const newLayout: GenericLayout[] = currentLayout.map(item => ({
      i: item.i,
      x: item.x,
      y: item.y,
      w: item.w,
      h: item.h,
      minW: item.minW,
      minH: item.minH,
      maxW: item.maxW,
      maxH: item.maxH,
    }));

    onLayoutChange(newLayout);
  }, [onLayoutChange]);

  // Grid configuration
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 };
  const cols = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 };

  return (
    <div className={`w-full h-full ${className}`}>
      <ResponsiveGridLayout
        className="layout"
        layouts={gridLayouts}
        breakpoints={breakpoints}
        cols={cols}
        rowHeight={60}
        isDraggable={isDraggable}
        isResizable={isResizable}
        onLayoutChange={handleLayoutChange}
        margin={[8, 8]} // Minimal margin for ultra-minimal look
        containerPadding={[0, 0]} // No container padding
        autoSize={true}
        useCSSTransforms={true}
        preventCollision={false}
        compactType="vertical"
        verticalCompact={true}
      >
        {dashboardWidgets.map(widget => (
          <div key={widget.id} className="generic-widget-container">
            <DashboardCard
              widgets={[widget]}
              onLayoutChange={() => {}} // No layout change handling at widget level
              mode="view" // Always in view mode
              displayMode="ultra-minimal" // Ultra-minimal display mode
              isDraggable={isDraggable}
              isResizable={isResizable}
              smartLayoutMode={true}
              autoHeightMode={false} // Disable auto height for consistent grid
              className="h-full w-full"
            />
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
};

export default GenericWorkspace;
