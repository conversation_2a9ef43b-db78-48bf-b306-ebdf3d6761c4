import React, { useState } from 'react';
import { z } from 'zod';
import {
  FormItem,
  Button,
  Typography,
  Icon,
} from '@/shared/components/common';
import { ResponsiveGrid } from '@/shared';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';

import type { SelectOption } from '@/shared/components/common/Select/Select';
import type { CreateSeedingGroupDto, CreateSeedingGroupAccountDto } from '../../types/seedingGroup';
import { ZaloService } from '../../services/zalo.service';
import { ZaloGroupsService } from '../../services/zalo-groups.service';
import { getZaloPersonalIntegrations } from '../../api/zalo/zaloPersonalApi';
import type { ZaloPersonalIntegrationQueryDto } from '../../types/zaloPersonal';
import { getAgentList } from '@/modules/ai-agents/api/agent-list.api';

// Form validation schema
const seedingGroupFormSchema = z.object({
  oaAccountId: z.string().min(1, 'Vui lòng chọn tài khoản OA'),
  oaAgentId: z.string().min(1, 'Vui lòng chọn agent cho tài khoản OA'),
  groupId: z.string().min(1, 'Vui lòng chọn nhóm'),
  accounts: z
    .array(
      z.object({
        personalAccountId: z.string().min(1, 'Vui lòng chọn tài khoản cá nhân'),
        agentId: z.string().min(1, 'Vui lòng chọn agent'),
      })
    )
    .min(1, 'Vui lòng thêm ít nhất một tài khoản cá nhân'),
});

interface SeedingGroupFormProps {
  onSubmit: (data: CreateSeedingGroupDto) => void;
  onCancel: () => void;
}

const SeedingGroupForm: React.FC<SeedingGroupFormProps> = ({ onSubmit, onCancel }) => {
  const [, setFormErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    oaAccountId: '',
    oaAgentId: '',
    groupId: '',
    accounts: [] as Array<{
      personalAccountId: string;
      agentId: string;
    }>,
  });

  // Load OA accounts function
  const loadOAAccountOptions = async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
      };

      const response = await ZaloService.getPaginatedAccounts(queryParams);
      const data = response.result;

      const items = (data?.items || []).map(account => ({
        value: account.id.toString(),
        label: account.name || account.oaId,
        data: account as unknown as Record<string, unknown>,
      }));

      return {
        items,
        totalItems: data?.meta?.totalItems || 0,
        totalPages: data?.meta?.totalPages || 1,
        currentPage: data?.meta?.currentPage || 1,
        hasMore: (data?.meta?.currentPage || 1) < (data?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading OA accounts:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }
  };

  // Load groups function
  const loadGroupOptions = async (params: { search?: string; page?: number; limit?: number }) => {
    if (!formData.oaAccountId) {
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }

    try {
      const queryParams = {
        integrationId: formData.oaAccountId,
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
      };

      const response = await ZaloGroupsService.getZaloGroups(queryParams);
      const data = response.result;

      const items = (data?.items || []).map(group => ({
        value: group.id,
        label: group.groupName,
        data: group as unknown as Record<string, unknown>,
      }));

      return {
        items,
        totalItems: data?.meta?.totalItems || 0,
        totalPages: data?.meta?.totalPages || 1,
        currentPage: data?.meta?.currentPage || 1,
        hasMore: (data?.meta?.currentPage || 1) < (data?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading groups:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }
  };

  // Load personal accounts function
  const loadPersonalAccountOptions = async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const queryParams: ZaloPersonalIntegrationQueryDto = {
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
      };

      const response = await getZaloPersonalIntegrations(queryParams);

      const items: SelectOption[] = (response.result?.items || []).map(item => ({
        value: item.id,
        label: item.integrationName || item.metadata?.profile?.name || `Tài khoản ${item.id}`,
        data: item as unknown as Record<string, unknown>,
      }));

      return {
        items,
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || 1,
        hasMore:
          (response.result?.meta?.currentPage || 1) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading personal accounts:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }
  };

  // Load agent options
  const loadAgentOptions = async (params: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    try {
      const response = await getAgentList({
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
      });

      const items: SelectOption[] = (response.items || []).map(item => ({
        value: item.id,
        label: item.name || `Agent ${item.id}`,
        data: item as unknown as Record<string, unknown>,
      }));

      const totalPages = Math.ceil((response.total || 0) / (params.limit || 10));

      return {
        items,
        totalItems: response.total || 0,
        totalPages,
        currentPage: response.page || 1,
        hasMore: (response.page || 1) < totalPages,
      };
    } catch (error) {
      console.error('Error loading agents:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }
  };

  // Handle field changes
  const handleFieldChange = (field: keyof CreateSeedingGroupDto, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Reset group when OA changes
    if (field === 'oaAccountId') {
      setFormData(prev => ({
        ...prev,
        groupId: '',
      }));
    }
  };

  // Handle account changes
  const handleAccountChange = (
    index: number,
    field: keyof CreateSeedingGroupAccountDto,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      accounts: prev.accounts.map((account, i) =>
        i === index ? { ...account, [field]: value } : account
      ),
    }));
  };

  // Add new account
  const handleAddAccount = () => {
    setFormData(prev => ({
      ...prev,
      accounts: [...prev.accounts, { personalAccountId: '', agentId: '' }],
    }));
  };

  // Remove account
  const handleRemoveAccount = (index: number) => {
    setFormData(prev => ({
      ...prev,
      accounts: prev.accounts.filter((_, i) => i !== index),
    }));
  };

  // Handle form submission
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      const validatedData = seedingGroupFormSchema.parse(formData);
      onSubmit(validatedData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            errors[err.path.join('.')] = err.message;
          }
        });
        setFormErrors(errors as any);
      }
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div className="space-y-4">
            <FormItem label="Tài khoản Zalo OA" name="oaAccountId" required>
              <AsyncSelectWithPagination
                value={formData.oaAccountId}
                onChange={value => handleFieldChange('oaAccountId', value as string)}
                loadOptions={loadOAAccountOptions}
                placeholder="Chọn tài khoản Zalo OA"
                fullWidth
              />
            </FormItem>

            <FormItem label="Agent cho tài khoản OA" name="oaAgentId" required>
              <AsyncSelectWithPagination
                value={formData.oaAgentId}
                onChange={value => handleFieldChange('oaAgentId', value as string)}
                loadOptions={loadAgentOptions}
                placeholder="Chọn agent cho tài khoản OA"
                fullWidth
              />
            </FormItem>

            <FormItem label="Nhóm Zalo" name="groupId" required>
              <AsyncSelectWithPagination
                value={formData.groupId}
                onChange={value => handleFieldChange('groupId', value as string)}
                loadOptions={loadGroupOptions}
                placeholder="Chọn nhóm Zalo"
                disabled={!formData.oaAccountId}
                fullWidth
              />
            </FormItem>
          </div>

          <div className="flex items-center justify-between mb-4">
            <Typography variant="h4">Tài khoản cá nhân và Agent</Typography>
            <Button
              type="button"
              variant="outline"
              onClick={handleAddAccount}
              className="flex items-center gap-2"
            >
              <Icon name="plus" size="sm" />
              Thêm tài khoản
            </Button>
          </div>

          {formData.accounts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Typography variant="body2">
                Chưa có tài khoản nào. Bấm "Thêm tài khoản" để bắt đầu.
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {formData.accounts.map((account, index) => (
                <div key={index} className="border border-border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-4">
                    <Typography variant="h5">Tài khoản {index + 1}</Typography>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveAccount(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Icon name="trash-2" size="sm" />
                    </Button>
                  </div>

                  <ResponsiveGrid maxColumns={{ xs: 1, md: 2 }} gap={4}>
                    <FormItem
                      label="Tài khoản cá nhân"
                      name={`accounts.${index}.personalAccountId`}
                      required
                    >
                      <AsyncSelectWithPagination
                        value={account.personalAccountId}
                        onChange={value =>
                          handleAccountChange(index, 'personalAccountId', value as string)
                        }
                        loadOptions={loadPersonalAccountOptions}
                        placeholder="Chọn tài khoản cá nhân"
                        fullWidth
                      />
                    </FormItem>

                    <FormItem label="Agent" name={`accounts.${index}.agentId`} required>
                      <AsyncSelectWithPagination
                        value={account.agentId}
                        onChange={value => handleAccountChange(index, 'agentId', value as string)}
                        loadOptions={loadAgentOptions}
                        placeholder="Chọn agent cho tài khoản này"
                        fullWidth
                      />
                    </FormItem>
                  </ResponsiveGrid>
                </div>
              ))}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Hủy
            </Button>
            <Button type="submit" variant="primary">
              Lưu cấu hình
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SeedingGroupForm;
