import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, IconCard, Button, Icon } from '@/shared/components/common';
import { ResponsiveGrid, useSmartNotification } from '@/shared';
import {
  ZaloPersonalCampaignType,
  ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS,
} from '../../types/zaloPersonalCampaign';
import FriendRequestToPhoneForm from './forms/FriendRequestToPhoneForm';
import CrawFriendsListForm from './forms/CrawFriendsListForm';
import CrawGroupMembersForm from './forms/CrawGroupMembersForm';
// import { useCreateZaloPersonalCampaign } from '../../hooks/zalo/useZaloPersonalCampaigns';

interface CreateZaloPersonalCampaignFormProps {
  onSuccess?: () => void;
  onCancel: () => void;
}

const CreateZaloPersonalCampaignForm: React.FC<CreateZaloPersonalCampaignFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { showNotification } = useSmartNotification();
  const [selectedType, setSelectedType] = useState<ZaloPersonalCampaignType | null>(null);

  // TODO: Implement actual campaign creation logic
  // const createMutation = useCreateZaloPersonalCampaign();

  // Danh sách các loại chiến dịch với icon và mô tả
  const campaignTypes = [
    {
      type: ZaloPersonalCampaignType.SEND_ALL,
      label: ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS[ZaloPersonalCampaignType.SEND_ALL],
      icon: 'send' as const,
      description: 'Gửi tin nhắn hàng loạt đến danh sách người nhận',
    },
    {
      type: ZaloPersonalCampaignType.CRAWL_FRIENDS,
      label: ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS[ZaloPersonalCampaignType.CRAWL_FRIENDS],
      icon: 'download' as const,
      description: 'Craw danh sách bạn bè từ tài khoản Zalo',
    },
    {
      type: ZaloPersonalCampaignType.CRAWL_GROUPS,
      label: ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS[ZaloPersonalCampaignType.CRAWL_GROUPS],
      icon: 'users' as const,
      description: 'Craw danh sách nhóm từ tài khoản Zalo',
    },
    {
      type: ZaloPersonalCampaignType.SEND_FRIEND_REQUEST,
      label: ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS[ZaloPersonalCampaignType.SEND_FRIEND_REQUEST],
      icon: 'user-plus' as const,
      description: 'Gửi lời mời kết bạn đến danh sách người dùng',
    },
  ];

  const handleTypeSelect = (type: ZaloPersonalCampaignType) => {
    setSelectedType(type);
    showNotification(
      'info',
      `Đã chọn loại chiến dịch: ${ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS[type]}`
    );
  };

  const handleBackToSelection = () => {
    setSelectedType(null);
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted:', data);
    showNotification('success', 'Tạo chiến dịch thành công');
    onSuccess?.();
  };

  const handleCancel = () => {
    setSelectedType(null);
    onCancel();
  };

  // Render form based on selected type
  if (selectedType) {
    return (
      <div className="w-full bg-background text-foreground">
        {/* Header */}
        <div className="bg-background p-4 flex items-center gap-3">
          <IconCard
            icon="arrow-left"
            variant="ghost"
            size="md"
            onClick={handleBackToSelection}
            title={t('common:back', 'Quay lại')}
          />
          <Typography variant="h6" className="font-medium">
            {ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS[selectedType]}
          </Typography>
        </div>

        {/* Form Content */}
        <div>
          {selectedType === ZaloPersonalCampaignType.SEND_FRIEND_REQUEST && (
            <FriendRequestToPhoneForm
              onSubmit={handleFormSubmit}
              onCancel={handleBackToSelection}
            />
          )}

          {selectedType === ZaloPersonalCampaignType.CRAWL_FRIENDS && (
            <CrawFriendsListForm onSubmit={handleFormSubmit} onCancel={handleBackToSelection} />
          )}

          {selectedType === ZaloPersonalCampaignType.CRAWL_GROUPS && (
            <CrawGroupMembersForm onSubmit={handleFormSubmit} onCancel={handleBackToSelection} />
          )}

          {/* TODO: Add other campaign type forms */}
          {selectedType !== ZaloPersonalCampaignType.SEND_FRIEND_REQUEST &&
            selectedType !== ZaloPersonalCampaignType.CRAWL_FRIENDS &&
            selectedType !== ZaloPersonalCampaignType.CRAWL_GROUPS && (
              <div className="text-center py-8">
                <Typography variant="body1" className="text-muted-foreground">
                  {t(
                    'marketing:zalo.personalCampaigns.create.formNotImplemented',
                    'Form cho loại chiến dịch này đang được phát triển.'
                  )}
                </Typography>
                <Button variant="outline" onClick={handleBackToSelection} className="mt-4">
                  {t('common:back', 'Quay lại')}
                </Button>
              </div>
            )}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="bg-background border-b border-border p-4 flex items-center gap-3">
        <IconCard
          icon="arrow-left"
          variant="ghost"
          size="md"
          onClick={handleCancel}
          title={t('common:back', 'Quay lại')}
        />
        <Typography variant="h6" className="font-medium">
          {t('marketing:zalo.personalCampaigns.create.title', 'Tạo chiến dịch mới')}
        </Typography>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="mb-6">
          <Typography variant="h6" className="font-semibold mb-2">
            {t('marketing:zalo.personalCampaigns.create.selectType', 'Chọn loại chiến dịch')}
          </Typography>
        </div>

        {/* Campaign Type Grid */}
        <ResponsiveGrid maxColumns={{ xs: 1, md: 2, lg: 2 }} gap={3}>
          {campaignTypes.map(campaign => (
            <Card
              key={campaign.type}
              className="p-4 cursor-pointer hover:shadow-md transition-shadow border border-border hover:border-primary/20"
              onClick={() => handleTypeSelect(campaign.type)}
            >
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <Icon name={campaign.icon} size="lg" className="text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <Typography variant="body1" className="font-medium text-foreground">
                    {campaign.label}
                  </Typography>
                  <Typography variant="caption" className="text-muted mt-1">
                    {campaign.description}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-6 border-t border-border justify-end mt-8">
          <IconCard
            icon="close"
            variant="secondary"
            size="lg"
            onClick={handleCancel}
            title={t('common:cancel', 'Hủy')}
          />
        </div>
      </div>
    </div>
  );
};

export default CreateZaloPersonalCampaignForm;
