import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Controller, useFormContext, FieldValues, SubmitHandler } from 'react-hook-form';
import { Card, Form, FormItem, Input, IconCard, Textarea } from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { getZaloPersonalIntegrations } from '../../../api/zalo/zaloPersonalApi';
import { useCreateCrawlGroupsCampaign } from '../../../hooks/zalo/useZaloPersonalCampaigns';
import { useSmartNotification } from '@/shared';
import type { ZaloPersonalIntegrationQueryDto } from '../../../types/zaloPersonal';
import type { CreateCrawlGroupsCampaignDto } from '../../../types/zaloPersonalCampaign';
import type { SelectOption } from '@/shared/components/common/Select/Select';

// Form data interface - Updated to match API specification
type CrawGroupMembersFormData = CreateCrawlGroupsCampaignDto;

// Form validation schema - Updated to match API specification
const crawGroupMembersFormSchema = z.object({
  integrationId: z.string().min(1, 'Vui lòng chọn tài khoản'),
  campaignType: z.literal('crawl_groups'),
  name: z.string().min(1, 'Tên chiến dịch là bắt buộc'),
  description: z.string().optional(),
  headless: z.boolean().optional().default(true),
  delayBetweenRequests: z.number().min(1, 'Thời gian delay phải lớn hơn 0').max(60, 'Thời gian delay không được vượt quá 60 giây'),
});

interface CrawGroupMembersFormProps {
  onSubmit: (data: CrawGroupMembersFormData) => void;
  onCancel?: () => void;
}

// Wrapper component cho AsyncSelectWithPagination để tích hợp với React Hook Form
const AsyncSelectFormField: React.FC<{
  name: string;
  loadOptions: any;
  placeholder?: string;
  multiple?: boolean;
  fullWidth?: boolean;
  itemsPerPage?: number;
  debounceTime?: number;
}> = ({ name, loadOptions, placeholder, multiple, fullWidth, itemsPerPage, debounceTime }) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <AsyncSelectWithPagination
          value={value}
          onChange={onChange}
          loadOptions={loadOptions}
          placeholder={placeholder}
          multiple={multiple}
          fullWidth={fullWidth}
          itemsPerPage={itemsPerPage}
          debounceTime={debounceTime}
          error={error?.message}
        />
      )}
    />
  );
};



const CrawGroupMembersForm: React.FC<CrawGroupMembersFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success, error } = useSmartNotification();
  const createCrawlGroupsCampaign = useCreateCrawlGroupsCampaign();

  const defaultValues: CrawGroupMembersFormData = {
    integrationId: '',
    campaignType: 'crawl_groups' as const,
    name: '',
    description: '',
    headless: true,
    delayBetweenRequests: 3,
  };

  // Load options function for AsyncSelectWithPagination
  const loadAccountOptions = async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      const queryParams: ZaloPersonalIntegrationQueryDto = {
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
      };

      // Sử dụng API service thay vì fetch trực tiếp
      const response = await getZaloPersonalIntegrations(queryParams);

      // Transform data to SelectOption format
      const items: SelectOption[] = (response.result?.items || []).map(item => ({
        value: item.id,
        label: item.integrationName || item.metadata?.profile?.name || `Tài khoản ${item.id}`,
        data: item as unknown as Record<string, unknown>,
      }));

      return {
        items,
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || 1,
        hasMore:
          (response.result?.meta?.currentPage || 1) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading account options:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
        hasMore: false,
      };
    }
  };

  const handleSubmit: SubmitHandler<FieldValues> = async (data) => {
    try {
      const formData = data as CrawGroupMembersFormData;

      // Call API to create crawl groups campaign
      await createCrawlGroupsCampaign.mutateAsync(formData);

      success({
        message: t('marketing:zalo.personalCampaigns.forms.crawGroupMembers.createSuccess', 'Tạo chiến dịch thành công!'),
      });

      // Call parent onSubmit callback
      onSubmit(formData);
    } catch (err) {
      console.error('Error creating crawl groups campaign:', err);
      error({
        message: t('marketing:zalo.personalCampaigns.forms.crawGroupMembers.createError', 'Có lỗi xảy ra khi tạo chiến dịch'),
      });
    }
  };

  return (
    <Card>
      <Form
        schema={crawGroupMembersFormSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
      >
        <div className="space-y-6">
          {/* Campaign Name */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawGroupMembers.campaignName',
              'Tên chiến dịch'
            )}
            name="name"
            required
          >
            <Input
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawGroupMembers.campaignNamePlaceholder',
                'Nhập tên chiến dịch craw nhóm'
              )}
              fullWidth
            />
          </FormItem>

          {/* Description */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawGroupMembers.description',
              'Mô tả chiến dịch'
            )}
            name="description"
          >
            <Textarea
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawGroupMembers.descriptionPlaceholder',
                'Nhập mô tả cho chiến dịch (tùy chọn)'
              )}
              fullWidth
              rows={3}
            />
          </FormItem>

          {/* Account Selection */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawGroupMembers.account',
              'Tài khoản Zalo'
            )}
            name="integrationId"
            required
          >
            <AsyncSelectFormField
              name="integrationId"
              loadOptions={loadAccountOptions}
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawGroupMembers.accountPlaceholder',
                'Chọn tài khoản Zalo để craw nhóm'
              )}
              multiple={false}
              fullWidth
              itemsPerPage={10}
              debounceTime={300}
            />
          </FormItem>

          {/* Delay Between Requests */}
          <FormItem
            label={t(
              'marketing:zalo.personalCampaigns.forms.crawGroupMembers.delayBetweenRequests',
              'Thời gian delay (giây)'
            )}
            name="delayBetweenRequests"
            required
          >
            <Input
              type="number"
              min={1}
              max={60}
              placeholder={t(
                'marketing:zalo.personalCampaigns.forms.crawGroupMembers.delayPlaceholder',
                'Nhập thời gian delay giữa các request (1-60 giây)'
              )}
              fullWidth
            />
          </FormItem>

          {/* Action IconCards */}
          <div className="flex justify-end space-x-3 pt-4">
            {onCancel && (
              <IconCard
                icon="x"
                variant="secondary"
                size="md"
                title={t('common:cancel', 'Hủy')}
                onClick={onCancel}
              />
            )}
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              type="submit"
              title={t('marketing:zalo.personalCampaigns.forms.crawGroupMembers.submit', 'Tạo chiến dịch')}
            />
          </div>
        </div>
      </Form>
    </Card>
  );
};

export default CrawGroupMembersForm;
