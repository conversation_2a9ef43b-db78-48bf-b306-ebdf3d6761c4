import React from 'react';
import { Typography } from '@/shared/components/common';
import { ELEMENT_TYPES } from '../constants';
import {
  Type as TypeIcon,
  Heading1 as HeadingIcon,
  Image as ImageIcon,
  Square as ButtonIcon,
  Minus as DividerIcon,
  ArrowUpDown as SpacerIcon,
  List as ListIcon,
  Link as LinkIcon,
  Share2 as SocialIcon,
  PanelTop as HeaderIcon,
  PanelLeft as FooterIcon,
  Layout as ColumnIcon,
  Columns as ColumnsIcon,
  Video as VideoIcon,
  Code as HtmlIcon,
} from 'lucide-react';

// Kích thước icon
const iconSize = 16;

interface EmailElementsPanelProps {
  onAddElement: (type: string) => void;
}

const EmailElementsPanel: React.FC<EmailElementsPanelProps> = ({ onAddElement }) => {
  // Xử lý khi kéo phần tử
  const handleDragStart = (e: React.DragEvent<HTMLElement>, elementType: string) => {
    e.dataTransfer.setData('text/plain', elementType);
    e.dataTransfer.effectAllowed = 'copy';
  };

  // Lấy icon tương ứng với loại phần tử
  const getElementIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <TypeIcon size={iconSize} />;
      case 'heading':
        return <HeadingIcon size={iconSize} />;
      case 'image':
        return <ImageIcon size={iconSize} />;
      case 'button':
        return <ButtonIcon size={iconSize} />;
      case 'divider':
        return <DividerIcon size={iconSize} />;
      case 'spacer':
        return <SpacerIcon size={iconSize} />;
      case 'list':
        return <ListIcon size={iconSize} />;
      case 'link':
        return <LinkIcon size={iconSize} />;
      case 'social':
        return <SocialIcon size={iconSize} />;
      case 'header':
        return <HeaderIcon size={iconSize} />;
      case 'footer':
        return <FooterIcon size={iconSize} />;
      case '1column':
        return <ColumnIcon size={iconSize} />;
      case '2columns':
        return <ColumnsIcon size={iconSize} />;
      case 'video':
        return <VideoIcon size={iconSize} />;
      case 'html':
        return <HtmlIcon size={iconSize} />;
      default:
        return <TypeIcon size={iconSize} />;
    }
  };

  return (
    <div className="h-full overflow-y-auto p-4">
      <Typography variant="h3" className="text-lg font-medium mb-4">
        Phần tử
      </Typography>

      <div className="grid grid-cols-2 gap-2 mb-6">
        {ELEMENT_TYPES.map(elementType => (
          <button
            key={elementType.type}
            type="button"
            className="cursor-pointer border border-gray-200 dark:border-gray-700 rounded-md p-3 hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-blue-500 hover:shadow-sm transition-all duration-200 flex flex-col items-center justify-center bg-white dark:bg-gray-900 relative z-10"
            draggable
            onDragStart={e => handleDragStart(e, elementType.type)}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              console.log('Element clicked:', elementType.type);
              onAddElement(elementType.type);
            }}
            style={{
              pointerEvents: 'auto',
              minHeight: '80px',
            }}
          >
            <div className="mb-1 text-gray-700 dark:text-gray-300">
              {getElementIcon(elementType.type)}
            </div>
            <Typography
              variant="body2"
              className="text-xs text-center text-gray-700 dark:text-gray-300"
            >
              {elementType.label}
            </Typography>
          </button>
        ))}
      </div>
    </div>
  );
};

export default EmailElementsPanel;
