/**
 * Demo WebSocket Server for Generic Page
 * Chạy: node src/modules/generic/demo/websocket-server.mjs
 */

import WebSocket, { WebSocketServer } from 'ws';
import http from 'http';
import url from 'url';

// Tạo HTTP server
const server = http.createServer();

// Tạo WebSocket server
const wss = new WebSocketServer({
  server,
  path: '/ws/generic'
});

// Store sessions và widgets
const sessions = new Map();
const widgets = new Map();

// Widget templates để demo
const WIDGET_TEMPLATES = [
  {
    id: 'demo-widget-1',
    title: 'Demo Data Count',
    type: 'data-count',
    x: 0, y: 0, w: 4, h: 3,
    props: { count: 1234, label: 'Total Users' }
  },
  {
    id: 'demo-widget-2', 
    title: 'Demo Chart',
    type: 'chart-widget',
    x: 4, y: 0, w: 8, h: 4,
    props: { chartType: 'line', data: [1, 2, 3, 4, 5] }
  },
  {
    id: 'demo-widget-3',
    title: 'Demo Stats',
    type: 'stats-widget', 
    x: 0, y: 3, w: 6, h: 3,
    props: { value: 98.5, unit: '%', trend: 'up' }
  }
];

// Utility functions
function generateSessionId() {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function broadcastToSession(sessionId, event) {
  const sessionClients = sessions.get(sessionId) || new Set();
  const message = JSON.stringify(event);
  
  sessionClients.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(message);
    }
  });
  
  console.log(`📤 Broadcast to session ${sessionId}:`, event.type);
}

function getSessionWidgets(sessionId) {
  return Array.from(widgets.values()).filter(w => w.sessionId === sessionId);
}

function getSessionLayout(sessionId) {
  return getSessionWidgets(sessionId).map(w => ({
    i: w.id,
    x: w.x,
    y: w.y, 
    w: w.w,
    h: w.h
  }));
}

// WebSocket connection handler
wss.on('connection', (ws, request) => {
  const query = url.parse(request.url, true).query;
  const sessionId = query.sessionId || generateSessionId();
  
  console.log(`🔌 New connection for session: ${sessionId}`);
  
  // Add client to session
  if (!sessions.has(sessionId)) {
    sessions.set(sessionId, new Set());
  }
  sessions.get(sessionId).add(ws);
  
  // Send initial state
  const sessionWidgets = getSessionWidgets(sessionId);
  const sessionLayout = getSessionLayout(sessionId);
  
  ws.send(JSON.stringify({
    type: 'sync_state',
    payload: {
      widgets: sessionWidgets,
      layout: sessionLayout
    },
    sessionId,
    timestamp: new Date().toISOString()
  }));
  
  // Handle messages
  ws.on('message', (data) => {
    try {
      const event = JSON.parse(data);
      console.log(`📥 Received from ${sessionId}:`, event.type);
      
      switch (event.type) {
        case 'add_widget':
          handleAddWidget(sessionId, event);
          break;
          
        case 'remove_widget':
          handleRemoveWidget(sessionId, event);
          break;
          
        case 'update_layout':
          handleUpdateLayout(sessionId, event);
          break;
          
        case 'sync_state':
          handleSyncState(sessionId, event);
          break;
          
        default:
          console.log(`❓ Unknown event type: ${event.type}`);
      }
    } catch (error) {
      console.error('❌ Error parsing message:', error);
    }
  });
  
  // Handle disconnect
  ws.on('close', () => {
    console.log(`🔌 Disconnected from session: ${sessionId}`);
    sessions.get(sessionId)?.delete(ws);
    
    // Clean up empty sessions
    if (sessions.get(sessionId)?.size === 0) {
      sessions.delete(sessionId);
    }
  });
  
  // Demo: Auto-add widgets after 3 seconds
  setTimeout(() => {
    if (sessionWidgets.length === 0) {
      console.log(`🎭 Demo: Adding widgets to session ${sessionId}`);
      
      WIDGET_TEMPLATES.forEach((template, index) => {
        setTimeout(() => {
          const widget = {
            ...template,
            id: `${template.id}-${Date.now()}`,
            sessionId,
            createdAt: new Date().toISOString()
          };
          
          widgets.set(widget.id, widget);
          
          broadcastToSession(sessionId, {
            type: 'add_widget',
            payload: { widget },
            sessionId,
            timestamp: new Date().toISOString()
          });
        }, index * 1000);
      });
    }
  }, 3000);
});

// Event handlers
function handleAddWidget(sessionId, event) {
  const widget = {
    ...event.payload.widget,
    sessionId,
    createdAt: new Date().toISOString()
  };
  
  widgets.set(widget.id, widget);
  
  // Broadcast to all clients in session
  broadcastToSession(sessionId, {
    type: 'add_widget',
    payload: { widget },
    sessionId,
    timestamp: new Date().toISOString()
  });
}

function handleRemoveWidget(sessionId, event) {
  const widgetId = event.payload.widgetId;
  widgets.delete(widgetId);
  
  broadcastToSession(sessionId, {
    type: 'remove_widget',
    payload: { widgetId },
    sessionId,
    timestamp: new Date().toISOString()
  });
}

function handleUpdateLayout(sessionId, event) {
  const layout = event.payload.layout;
  
  // Update widget positions
  layout.forEach(item => {
    const widget = widgets.get(item.i);
    if (widget && widget.sessionId === sessionId) {
      widget.x = item.x;
      widget.y = item.y;
      widget.w = item.w;
      widget.h = item.h;
    }
  });
  
  broadcastToSession(sessionId, {
    type: 'update_layout',
    payload: { layout },
    sessionId,
    timestamp: new Date().toISOString()
  });
}

function handleSyncState(sessionId, event) {
  const sessionWidgets = getSessionWidgets(sessionId);
  const sessionLayout = getSessionLayout(sessionId);
  
  broadcastToSession(sessionId, {
    type: 'sync_state',
    payload: {
      widgets: sessionWidgets,
      layout: sessionLayout
    },
    sessionId,
    timestamp: new Date().toISOString()
  });
}

// Start server
const PORT = process.env.PORT || 8081;
server.listen(PORT, () => {
  console.log(`🚀 Generic WebSocket Server running on port ${PORT}`);
  console.log(`📡 WebSocket endpoint: ws://localhost:${PORT}/ws/generic`);
  console.log(`🎯 Test URL: http://localhost:5174/generic`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  wss.close(() => {
    server.close(() => {
      console.log('✅ Server closed');
      process.exit(0);
    });
  });
});
