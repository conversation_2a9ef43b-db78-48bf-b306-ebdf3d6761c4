import React, { useState } from 'react';
import { EmailElement as EmailElementType } from '../types';
import { ArrowUp, ArrowDown, Trash2 } from 'lucide-react';
import RichTextEditor from './RichTextEditor';
import ImageInlineEditor from './ImageInlineEditor';
import ButtonInlineEditor from './ButtonInlineEditor';
import VideoInlineEditor from './VideoInlineEditor';
import ListInlineEditor from './ListInlineEditor';

interface EmailElementProps {
  element: EmailElementType;
  index: number;
  isSelected: boolean;
  onSelect: (element: EmailElementType, index: number) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onUpdateElement: (property: string, value: unknown) => void;
  totalElements: number;
}

const EmailElement: React.FC<EmailElementProps> = ({
  element,
  index,
  isSelected,
  onSelect,
  onDelete,
  onMoveUp,
  onMoveDown,
  onUpdateElement,
  totalElements,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  // Render phần tử dựa trên loại
  const renderElement = () => {
    switch (element.type) {
      case 'text': {
        const textStyle = {
          fontSize: element.style?.fontSize || 16,
          fontWeight: element.style?.fontWeight || 'normal',
          color: element.style?.color || '#333333',
          textAlign: (element.style?.textAlign as React.CSSProperties['textAlign']) || 'left',
          lineHeight: element.style?.lineHeight || 1.5,
          paddingTop: element.style?.paddingTop || 8,
          paddingBottom: element.style?.paddingBottom || 8,
          paddingLeft: element.style?.paddingLeft || 16,
          paddingRight: element.style?.paddingRight || 16,
          marginTop: element.style?.marginTop,
          marginBottom: element.style?.marginBottom,
          marginLeft: element.style?.marginLeft,
          marginRight: element.style?.marginRight,
          ...(element.style as React.CSSProperties),
        };

        console.log('Text element style for index', index, ':', textStyle);

        return (
          <div
            className="min-h-[20px] outline-none"
            style={textStyle}
            dangerouslySetInnerHTML={{
              __html: element.content || 'Nhấp đôi để chỉnh sửa văn bản này',
            }}
            onDoubleClick={e => {
              e.stopPropagation();
              // Xử lý chỉnh sửa nội dung text
            }}
          />
        );
      }

      case 'heading': {
        const HeadingTag = element.headingType || 'h2';
        return React.createElement(HeadingTag, {
          className: 'min-h-[24px] outline-none',
          style: {
            fontSize:
              element.style?.fontSize ||
              (element.headingType === 'h1'
                ? 32
                : element.headingType === 'h2'
                  ? 24
                  : element.headingType === 'h3'
                    ? 20
                    : 18),
            fontWeight: element.style?.fontWeight || 'bold',
            color: element.style?.color || '#111111',
            textAlign: (element.style?.textAlign as React.CSSProperties['textAlign']) || 'center',
            lineHeight: element.style?.lineHeight || 1.2,
            paddingTop: element.style?.paddingTop || 16,
            paddingBottom: element.style?.paddingBottom || 16,
            paddingLeft: element.style?.paddingLeft || 16,
            paddingRight: element.style?.paddingRight || 16,
            marginTop: element.style?.marginTop,
            marginBottom: element.style?.marginBottom || 16,
            marginLeft: element.style?.marginLeft,
            marginRight: element.style?.marginRight,
            fontFamily: element.style?.fontFamily || 'Arial, sans-serif',
            ...(element.style as React.CSSProperties),
          },
          dangerouslySetInnerHTML: { __html: element.content || 'Tiêu đề mẫu' },
          onDoubleClick: (e: React.MouseEvent<HTMLElement>) => {
            e.stopPropagation();
            // Xử lý chỉnh sửa nội dung heading
          },
        });
      }

      case 'image':
        return (
          <div
            className="relative overflow-hidden"
            style={{
              paddingTop: element.style?.paddingTop || 8,
              paddingBottom: element.style?.paddingBottom || 8,
              paddingLeft: element.style?.paddingLeft || 0,
              paddingRight: element.style?.paddingRight || 0,
              marginTop: element.style?.marginTop,
              marginBottom: element.style?.marginBottom,
              marginLeft: element.style?.marginLeft,
              marginRight: element.style?.marginRight,
              borderRadius: element.style?.borderRadius || 4,
              textAlign: (element.style?.textAlign as React.CSSProperties['textAlign']) || 'left',
              ...(element.style as React.CSSProperties),
            }}
          >
            <img
              src={element.src || 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu'}
              alt={element.alt || 'Hình ảnh'}
              className="inline-block transition-transform hover:scale-105"
              style={{
                width: element.style?.width || '100%',
                height: element.style?.height || 'auto',
                maxWidth: '100%',
                maxHeight: element.style?.maxHeight || '400px',
                objectFit: 'cover',
                borderRadius: element.style?.borderRadius || 4,
              }}
              onDoubleClick={e => {
                e.stopPropagation();
                // Xử lý chỉnh sửa hình ảnh
              }}
            />
          </div>
        );

      case 'button':
        return (
          <div
            style={{
              textAlign: (element.style?.textAlign as React.CSSProperties['textAlign']) || 'center',
              paddingTop: element.style?.paddingTop || 16,
              paddingBottom: element.style?.paddingBottom || 16,
              paddingLeft: element.style?.paddingLeft || 16,
              paddingRight: element.style?.paddingRight || 16,
              marginTop: element.style?.marginTop,
              marginBottom: element.style?.marginBottom,
              marginLeft: element.style?.marginLeft,
              marginRight: element.style?.marginRight,
            }}
          >
            <a
              href={element.url || '#'}
              className="inline-block transition-all duration-200 hover:shadow-lg hover:scale-105 cursor-pointer"
              style={{
                backgroundColor: element.style?.backgroundColor || '#3b82f6',
                color: element.style?.color || '#ffffff',
                padding: '12px 24px',
                borderRadius: element.style?.borderRadius || 6,
                textDecoration: 'none',
                fontWeight: (element.style?.fontWeight as string) || '600',
                fontSize: element.style?.fontSize || 14,
                width: element.style?.width as string,
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                display: 'inline-block',
              }}
              onClick={e => e.preventDefault()}
            >
              {element.text || 'Nút nhấn'}
            </a>
          </div>
        );

      case 'divider':
        return (
          <hr
            className="border-gray-300 dark:border-gray-600 my-4"
            style={element.style as React.CSSProperties}
          />
        );

      case 'spacer':
        return (
          <div
            className="min-h-[20px]"
            style={{
              height: element.style?.height || '20px',
              ...(element.style as React.CSSProperties),
            }}
          >
            &nbsp;
          </div>
        );

      case 'list': {
        const listType = (element.listType as 'ul' | 'ol') || 'ul';
        const listStyle = (element.listStyle as string) || (listType === 'ul' ? 'disc' : 'decimal');
        const items = (element.items as string[]) || ['Mục danh sách'];

        const listStyles: React.CSSProperties = {
          listStyleType: listStyle as any,
          paddingLeft: '20px',
          margin: '0',
          textAlign: (element.style?.textAlign as any) || 'left',
          lineHeight: element.style?.lineHeight || 1.6,
          color: element.style?.color,
          fontSize: element.style?.fontSize,
        };

        const ListComponent = listType === 'ul' ? 'ul' : 'ol';

        return (
          <div>
            <style>{`
              .email-list-${listType} {
                list-style-type: ${listStyle} !important;
                padding-left: 20px !important;
                margin: 0 !important;
                text-align: ${(element.style?.textAlign as any) || 'left'} !important;
                line-height: ${element.style?.lineHeight || 1.6} !important;
                ${element.style?.color ? `color: ${element.style.color} !important;` : ''}
                ${element.style?.fontSize ? `font-size: ${element.style.fontSize}px !important;` : ''}
              }
            `}</style>
            {React.createElement(
              ListComponent,
              {
                className: `email-list-${listType}`,
                style: listStyles,
              },
              items.map((item, index) => React.createElement('li', { key: index }, item))
            )}
          </div>
        );
      }

      case 'link':
        return (
          <a
            href={element.url || '#'}
            style={element.style as React.CSSProperties}
            onClick={e => e.preventDefault()}
          >
            {element.text || 'Liên kết'}
          </a>
        );

      case 'social':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{
              __html:
                element.content ||
                '<div style="text-align: center;"><a href="#" style="margin: 0 10px;">Facebook</a><a href="#" style="margin: 0 10px;">Twitter</a><a href="#" style="margin: 0 10px;">Instagram</a></div>',
            }}
          />
        );

      case 'header':
      case 'footer':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '' }}
          />
        );

      case '1column':
        return (
          <div
            className="w-full border border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 min-h-[100px]"
            style={element.style as React.CSSProperties}
          >
            {element.children && element.children.length > 0 ? (
              element.children.map(child => (
                <div key={child.id} className="mb-2 last:mb-0">
                  <EmailElement
                    element={child}
                    index={-1}
                    isSelected={false}
                    onSelect={() => {}}
                    onDelete={() => {}}
                    onMoveUp={() => {}}
                    onMoveDown={() => {}}
                    onUpdateElement={() => {}}
                    totalElements={0}
                  />
                </div>
              ))
            ) : (
              <div className="flex items-center justify-center h-20 text-gray-400 dark:text-gray-500 text-sm">
                <div className="text-center">
                  <div className="w-8 h-8 mx-auto mb-2 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                  </div>
                  Thả phần tử vào đây
                </div>
              </div>
            )}
          </div>
        );

      case '2columns': {
        // Tìm cột trái và phải
        const leftColumn = element.children?.find(child => child.columnPosition === 'left');
        const rightColumn = element.children?.find(child => child.columnPosition === 'right');

        return (
          <div
            className="grid grid-cols-2 gap-4 p-4 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg min-h-[100px]"
            style={element.style as React.CSSProperties}
          >
            {/* Cột trái */}
            <div
              className="border border-dashed border-gray-200 dark:border-gray-700 rounded p-3 min-h-[80px]"
              style={leftColumn?.style as React.CSSProperties}
            >
              {leftColumn && leftColumn.children && leftColumn.children.length > 0 ? (
                leftColumn.children.map(child => (
                  <div key={child.id} className="mb-2 last:mb-0">
                    <EmailElement
                      element={child}
                      index={-1}
                      isSelected={false}
                      onSelect={() => {}}
                      onDelete={() => {}}
                      onMoveUp={() => {}}
                      onMoveDown={() => {}}
                      onUpdateElement={() => {}}
                      totalElements={0}
                    />
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500 text-sm">
                  <div className="text-center">
                    <div className="w-6 h-6 mx-auto mb-1 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      <svg
                        className="w-3 h-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                    </div>
                    Cột trái
                  </div>
                </div>
              )}
            </div>

            {/* Cột phải */}
            <div
              className="border border-dashed border-gray-200 dark:border-gray-700 rounded p-3 min-h-[80px]"
              style={rightColumn?.style as React.CSSProperties}
            >
              {rightColumn && rightColumn.children && rightColumn.children.length > 0 ? (
                rightColumn.children.map(child => (
                  <div key={child.id} className="mb-2 last:mb-0">
                    <EmailElement
                      element={child}
                      index={-1}
                      isSelected={false}
                      onSelect={() => {}}
                      onDelete={() => {}}
                      onMoveUp={() => {}}
                      onMoveDown={() => {}}
                      onUpdateElement={() => {}}
                      totalElements={0}
                    />
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500 text-sm">
                  <div className="text-center">
                    <div className="w-6 h-6 mx-auto mb-1 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      <svg
                        className="w-3 h-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                    </div>
                    Cột phải
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      }

      default:
        return <div>{element.type}</div>;
    }
  };

  return (
    <div
      className={`email-element relative group transition-all duration-200 ${
        isSelected
          ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50/30 dark:bg-blue-900/10'
          : 'hover:ring-1 hover:ring-blue-300 hover:ring-opacity-50'
      } ${
        (element.type === 'text' ||
          element.type === 'heading' ||
          element.type === 'image' ||
          element.type === 'button' ||
          element.type === 'video' ||
          element.type === 'list') &&
        !isEditing
          ? 'cursor-pointer hover:bg-blue-50/20 dark:hover:bg-blue-900/5'
          : 'cursor-pointer'
      }`}
      onClick={() => {
        console.log('Element clicked:', element.type, 'at index:', index);
        onSelect(element, index);

        // Auto-start editing for text, heading, image, button, video, and list elements on single click
        if (
          (element.type === 'text' ||
            element.type === 'heading' ||
            element.type === 'image' ||
            element.type === 'button' ||
            element.type === 'video' ||
            element.type === 'list') &&
          !isEditing
        ) {
          console.log('Starting inline editing for element:', element.type);
          setTimeout(() => {
            setIsEditing(true);
          }, 150);
        }
      }}
    >
      {/* Element content */}
      <div className="relative">
        {isEditing && (element.type === 'text' || element.type === 'heading') ? (
          <RichTextEditor
            value={element.content || ''}
            onChange={content => {
              onUpdateElement('content', content);
            }}
            placeholder={element.type === 'text' ? 'Nhập nội dung văn bản' : 'Nhập tiêu đề'}
            className="w-full"
            onClickOutside={() => setIsEditing(false)}
          />
        ) : isEditing && element.type === 'image' ? (
          <ImageInlineEditor
            src={element.src || ''}
            alt={element.alt || ''}
            width={(element.style?.width as number) || 300}
            height={(element.style?.height as number) || 200}
            borderRadius={(element.style?.borderRadius as number) || 0}
            textAlign={(element.style?.textAlign as 'left' | 'center' | 'right') || 'left'}
            onUpdate={updates => {
              if (updates.src !== undefined) {
                onUpdateElement('src', updates.src);
              }
              if (updates.alt !== undefined) {
                onUpdateElement('alt', updates.alt);
              }
              if (updates.width !== undefined) {
                onUpdateElement('style', { ...element.style, width: updates.width });
              }
              if (updates.height !== undefined) {
                onUpdateElement('style', { ...element.style, height: updates.height });
              }
              if (updates.borderRadius !== undefined) {
                onUpdateElement('style', { ...element.style, borderRadius: updates.borderRadius });
              }
              if (updates.textAlign !== undefined) {
                onUpdateElement('style', { ...element.style, textAlign: updates.textAlign });
              }
            }}
            onClickOutside={() => setIsEditing(false)}
            className="w-full"
          />
        ) : isEditing && element.type === 'button' ? (
          <ButtonInlineEditor
            text={element.text || 'Button'}
            url={element.url || '#'}
            backgroundColor={(element.style?.backgroundColor as string) || '#007bff'}
            color={(element.style?.color as string) || '#ffffff'}
            fontSize={(element.style?.fontSize as number) || 16}
            paddingX={(element.style?.paddingLeft as number) || 24}
            paddingY={(element.style?.paddingTop as number) || 12}
            borderRadius={(element.style?.borderRadius as number) || 6}
            textAlign={(element.style?.textAlign as 'left' | 'center' | 'right') || 'center'}
            onUpdate={updates => {
              if (updates.text !== undefined) {
                onUpdateElement('text', updates.text);
              }
              if (updates.url !== undefined) {
                onUpdateElement('url', updates.url);
              }
              if (updates.backgroundColor !== undefined) {
                onUpdateElement('style', {
                  ...element.style,
                  backgroundColor: updates.backgroundColor,
                });
              }
              if (updates.color !== undefined) {
                onUpdateElement('style', { ...element.style, color: updates.color });
              }
              if (updates.fontSize !== undefined) {
                onUpdateElement('style', { ...element.style, fontSize: updates.fontSize });
              }
              if (updates.paddingX !== undefined) {
                onUpdateElement('style', {
                  ...element.style,
                  paddingLeft: updates.paddingX,
                  paddingRight: updates.paddingX,
                });
              }
              if (updates.paddingY !== undefined) {
                onUpdateElement('style', {
                  ...element.style,
                  paddingTop: updates.paddingY,
                  paddingBottom: updates.paddingY,
                });
              }
              if (updates.borderRadius !== undefined) {
                onUpdateElement('style', { ...element.style, borderRadius: updates.borderRadius });
              }
              if (updates.textAlign !== undefined) {
                onUpdateElement('style', { ...element.style, textAlign: updates.textAlign });
              }
            }}
            onClickOutside={() => setIsEditing(false)}
            className="w-full"
          />
        ) : isEditing && element.type === 'video' ? (
          <VideoInlineEditor
            src={element.src || ''}
            width={(element.style?.width as number) || 400}
            height={(element.style?.height as number) || 300}
            textAlign={(element.style?.textAlign as 'left' | 'center' | 'right') || 'center'}
            onUpdate={updates => {
              if (updates.src !== undefined) {
                onUpdateElement('src', updates.src);
              }
              if (updates.width !== undefined) {
                onUpdateElement('style', { ...element.style, width: updates.width });
              }
              if (updates.height !== undefined) {
                onUpdateElement('style', { ...element.style, height: updates.height });
              }
              if (updates.textAlign !== undefined) {
                onUpdateElement('style', { ...element.style, textAlign: updates.textAlign });
              }
            }}
            onClickOutside={() => setIsEditing(false)}
            className="w-full"
          />
        ) : isEditing && element.type === 'list' ? (
          <ListInlineEditor
            items={(element.items as string[]) || ['Mục danh sách']}
            listType={(element.listType as 'ul' | 'ol') || 'ul'}
            listStyle={(element.listStyle as string) || 'disc'}
            textAlign={(element.style?.textAlign as 'left' | 'center' | 'right') || 'left'}
            lineHeight={(element.style?.lineHeight as number) || 1.6}
            onUpdate={updates => {
              if (updates.items !== undefined) {
                onUpdateElement('items', updates.items);
              }
              if (updates.listType !== undefined) {
                onUpdateElement('listType', updates.listType);
              }
              if (updates.listStyle !== undefined) {
                onUpdateElement('listStyle', updates.listStyle);
              }
              if (updates.textAlign !== undefined) {
                onUpdateElement('style', { ...element.style, textAlign: updates.textAlign });
              }
              if (updates.lineHeight !== undefined) {
                onUpdateElement('style', { ...element.style, lineHeight: updates.lineHeight });
              }
            }}
            onClickOutside={() => setIsEditing(false)}
            className="w-full"
          />
        ) : (
          renderElement()
        )}
      </div>

      {/* Selection indicator */}
      {isSelected && !isEditing && (
        <>
          {/* Top border indicator */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-blue-500"></div>

          {/* Toolbar */}
          <div
            className={`absolute ${index === 0 ? '-top-6 left-0' : '-top-8 left-0'} flex items-center gap-1 bg-blue-500 text-white px-2 py-1 rounded-t text-xs font-medium shadow-lg z-20`}
          >
            <span className="capitalize">{element.type}</span>
            <div className="flex items-center gap-0.5 ml-2">
              {index > 0 && (
                <button
                  className="w-5 h-5 flex items-center justify-center hover:bg-blue-600 rounded transition-colors"
                  onClick={e => {
                    e.stopPropagation();
                    onMoveUp();
                  }}
                  title="Di chuyển lên"
                >
                  <ArrowUp size={10} />
                </button>
              )}

              {index < totalElements - 1 && (
                <button
                  className="w-5 h-5 flex items-center justify-center hover:bg-blue-600 rounded transition-colors"
                  onClick={e => {
                    e.stopPropagation();
                    onMoveDown();
                  }}
                  title="Di chuyển xuống"
                >
                  <ArrowDown size={10} />
                </button>
              )}

              {/* Delete button */}
              <button
                className="w-6 h-6 flex items-center justify-center bg-red-500 hover:bg-red-600 text-white rounded transition-colors shadow-sm border border-red-600"
                onClick={e => {
                  e.stopPropagation();
                  e.preventDefault();
                  console.log('🗑️ DELETE BUTTON CLICKED!');
                  console.log('Element:', element.type, 'Index:', index);
                  console.log('Total elements:', totalElements);
                  console.log('Is last element:', index === totalElements - 1);
                  console.log('onDelete function:', typeof onDelete);

                  if (typeof onDelete === 'function') {
                    console.log('Calling onDelete...');
                    onDelete();
                    console.log('onDelete called successfully');
                  } else {
                    console.error('onDelete is not a function!', onDelete);
                  }
                }}
                title="Xóa phần tử (Click để xóa)"
              >
                <Trash2 size={12} />
              </button>
            </div>
          </div>
        </>
      )}

      {/* Hover indicator */}
      {!isSelected && !isEditing && (
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="absolute top-0 left-0 right-0 h-0.5 bg-blue-300 pointer-events-none"></div>
          <div
            className={`absolute ${index === 0 ? '-top-5 left-0' : '-top-6 left-0'} bg-blue-400 text-white px-2 py-0.5 rounded-t text-xs font-medium z-10 flex items-center gap-2`}
          >
            <span className="pointer-events-none">{element.type}</span>
            {/* Quick delete button on hover */}
            <button
              className="w-4 h-4 flex items-center justify-center bg-red-500 hover:bg-red-600 text-white rounded transition-colors"
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
                console.log('🗑️ HOVER DELETE BUTTON CLICKED!');
                if (typeof onDelete === 'function') {
                  onDelete();
                }
              }}
              title="Xóa phần tử"
            >
              <Trash2 size={8} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailElement;
