import type { ApiResponseDto, PaginationMeta } from '@/shared/dto/response/api-response.dto';

/**
 * Seeding Group DTO
 */
export interface SeedingGroupDto {
  id: string;
  userId: number;
  name: string;
  description?: string;
  oaAccountId: string;
  groupId: string;
  status: SeedingGroupStatus;
  totalAccounts: number;
  activeAccounts: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Seeding Group Account DTO
 */
export interface SeedingGroupAccountDto {
  id: string;
  seedingGroupId: string;
  personalAccountId: string;
  personalAccountName: string;
  prompt: string;
  status: SeedingAccountStatus;
  lastActiveAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Seeding Group Status enum
 */
export enum SeedingGroupStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  STOPPED = 'STOPPED',
}

/**
 * Seeding Account Status enum
 */
export enum SeedingAccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ERROR = 'ERROR',
}

/**
 * Query parameters cho danh sách Seeding Groups
 */
export interface SeedingGroupQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: SeedingGroupStatus;
  oaAccountId?: string;
  groupId?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

/**
 * Response DTO cho danh sách Seeding Groups
 */
export interface SeedingGroupResponseDto extends ApiResponseDto {
  result: {
    items: SeedingGroupDto[];
    meta: PaginationMeta;
  };
}

/**
 * DTO để tạo Seeding Group mới
 */
export interface CreateSeedingGroupDto {
  oaAccountId: string;
  oaAgentId: string;
  groupId: string;
  accounts: CreateSeedingGroupAccountDto[];
}

/**
 * DTO để tạo Seeding Group Account
 */
export interface CreateSeedingGroupAccountDto {
  personalAccountId: string;
  agentId: string;
}

/**
 * DTO để cập nhật Seeding Group
 */
export interface UpdateSeedingGroupDto {
  name?: string;
  description?: string;
  status?: SeedingGroupStatus;
  accounts?: UpdateSeedingGroupAccountDto[];
}

/**
 * DTO để cập nhật Seeding Group Account
 */
export interface UpdateSeedingGroupAccountDto {
  id?: string;
  personalAccountId: string;
  prompt: string;
  status?: SeedingAccountStatus;
}

/**
 * Response DTO cho chi tiết Seeding Group
 */
export interface SeedingGroupDetailResponseDto extends ApiResponseDto {
  result: SeedingGroupDto & {
    accounts: SeedingGroupAccountDto[];
  };
}

/**
 * Labels cho status
 */
export const SEEDING_GROUP_STATUS_LABELS: Record<SeedingGroupStatus, string> = {
  [SeedingGroupStatus.DRAFT]: 'Bản nháp',
  [SeedingGroupStatus.ACTIVE]: 'Đang hoạt động',
  [SeedingGroupStatus.PAUSED]: 'Tạm dừng',
  [SeedingGroupStatus.STOPPED]: 'Đã dừng',
};

export const SEEDING_ACCOUNT_STATUS_LABELS: Record<SeedingAccountStatus, string> = {
  [SeedingAccountStatus.ACTIVE]: 'Hoạt động',
  [SeedingAccountStatus.INACTIVE]: 'Không hoạt động',
  [SeedingAccountStatus.ERROR]: 'Lỗi',
};

/**
 * Helper functions
 */
export const getSeedingGroupStatusLabel = (status: SeedingGroupStatus): string => {
  return SEEDING_GROUP_STATUS_LABELS[status] || status;
};

export const getSeedingAccountStatusLabel = (status: SeedingAccountStatus): string => {
  return SEEDING_ACCOUNT_STATUS_LABELS[status] || status;
};

/**
 * Status colors for UI
 */
export const SEEDING_GROUP_STATUS_COLORS: Record<SeedingGroupStatus, string> = {
  [SeedingGroupStatus.DRAFT]: 'gray',
  [SeedingGroupStatus.ACTIVE]: 'green',
  [SeedingGroupStatus.PAUSED]: 'yellow',
  [SeedingGroupStatus.STOPPED]: 'red',
};

export const SEEDING_ACCOUNT_STATUS_COLORS: Record<SeedingAccountStatus, string> = {
  [SeedingAccountStatus.ACTIVE]: 'green',
  [SeedingAccountStatus.INACTIVE]: 'gray',
  [SeedingAccountStatus.ERROR]: 'red',
};
