/**
 * Generic Page WebSocket Service
 * Quản lý real-time communication cho trang generic
 */

import {
  GenericWebSocketEvent,
  AddWidgetEvent,
  RemoveWidgetEvent,
  UpdateLayoutEvent,
  SyncStateEvent,
  GenericWidget,
  GenericLayout,
} from '../types';

type EventCallback = (event: GenericWebSocketEvent) => void;
type SpecificEventCallback<T> = (event: T) => void;

class GenericWebSocketService {
  private ws: WebSocket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private sessionId: string | null = null;
  private listeners = new Map<string, Set<EventCallback>>();
  private url: string;

  constructor() {
    // Sử dụng environment variable hoặc fallback tới backend NestJS
    const wsBaseUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3000';
    this.url = `${wsBaseUrl}/generic`; // Namespace /generic trong NestJS
    console.log('🔧 GenericWebSocketService initialized with URL:', this.url);
    console.log('🔧 Environment variables:', {
      VITE_WS_URL: import.meta.env.VITE_WS_URL,
      VITE_API_URL: import.meta.env.VITE_API_URL,
    });
  }

  /**
   * Kết nối tới WebSocket server
   */
  async connect(sessionId?: string): Promise<void> {
    if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      console.log('🔌 Already connected to Generic WebSocket');
      return;
    }

    try {
      this.sessionId = sessionId || this.generateSessionId();
      const url = `${this.url}?sessionId=${this.sessionId}`;

      console.log('🔌 Connecting to Generic WebSocket:', url);
      this.ws = new WebSocket(url);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      // Đợi connection
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.handleOpen();
          resolve();
        };

        this.ws!.onerror = error => {
          clearTimeout(timeout);
          reject(error);
        };
      });
    } catch (error) {
      console.error('❌ Failed to connect to Generic WebSocket:', error);
      throw error;
    }
  }

  /**
   * Ngắt kết nối
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.sessionId = null;
  }

  /**
   * Kiểm tra trạng thái kết nối
   */
  getConnectionStatus(): boolean {
    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Lấy session ID hiện tại
   */
  getSessionId(): string | null {
    return this.sessionId;
  }

  /**
   * Gửi event tới server
   */
  sendEvent(event: Omit<GenericWebSocketEvent, 'sessionId' | 'timestamp'>): void {
    if (!this.getConnectionStatus() || !this.sessionId) {
      console.warn('⚠️ WebSocket not connected, cannot send event:', event);
      return;
    }

    const fullEvent: GenericWebSocketEvent = {
      ...event,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
    };

    this.ws!.send(JSON.stringify(fullEvent));
    console.log('📤 Sent WebSocket event:', fullEvent);
  }

  /**
   * Subscribe to specific event types
   */
  subscribe(eventType: string, callback: EventCallback): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }

    this.listeners.get(eventType)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.listeners.get(eventType)?.delete(callback);
    };
  }

  /**
   * Subscribe to add widget events
   */
  onAddWidget(callback: SpecificEventCallback<AddWidgetEvent>): () => void {
    return this.subscribe('add_widget', callback as EventCallback);
  }

  /**
   * Subscribe to remove widget events
   */
  onRemoveWidget(callback: SpecificEventCallback<RemoveWidgetEvent>): () => void {
    return this.subscribe('remove_widget', callback as EventCallback);
  }

  /**
   * Subscribe to layout update events
   */
  onUpdateLayout(callback: SpecificEventCallback<UpdateLayoutEvent>): () => void {
    return this.subscribe('update_layout', callback as EventCallback);
  }

  /**
   * Subscribe to state sync events
   */
  onSyncState(callback: SpecificEventCallback<SyncStateEvent>): () => void {
    return this.subscribe('sync_state', callback as EventCallback);
  }

  /**
   * Send add widget command
   */
  addWidget(widget: GenericWidget, position?: { x: number; y: number }): void {
    this.sendEvent({
      type: 'add_widget',
      payload: { widget, position },
    });
  }

  /**
   * Send remove widget command
   */
  removeWidget(widgetId: string): void {
    this.sendEvent({
      type: 'remove_widget',
      payload: { widgetId },
    });
  }

  /**
   * Send layout update command
   */
  updateLayout(layout: GenericLayout[]): void {
    this.sendEvent({
      type: 'update_layout',
      payload: { layout },
    });
  }

  /**
   * Request state sync
   */
  requestSync(): void {
    this.sendEvent({
      type: 'sync_state',
      payload: {},
    });
  }

  // Private methods
  private handleOpen(): void {
    console.log('✅ Generic WebSocket connected');
    this.isConnected = true;
    this.reconnectAttempts = 0;
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data: GenericWebSocketEvent = JSON.parse(event.data);
      console.log('📥 Received WebSocket event:', data);

      // Notify listeners
      const listeners = this.listeners.get(data.type);
      if (listeners) {
        listeners.forEach(callback => callback(data));
      }
    } catch (error) {
      console.error('❌ Failed to parse WebSocket message:', error);
    }
  }

  private handleClose(): void {
    console.log('🔌 Generic WebSocket disconnected');
    this.isConnected = false;

    // Auto-reconnect
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      );

      setTimeout(() => {
        this.connect(this.sessionId || undefined);
      }, this.reconnectInterval);
    }
  }

  private handleError(error: Event): void {
    console.error('❌ Generic WebSocket error:', error);
  }

  private generateSessionId(): string {
    return `generic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const genericWebSocketService = new GenericWebSocketService();
export default genericWebSocketService;
