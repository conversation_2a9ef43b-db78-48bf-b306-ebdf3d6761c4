import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { scrollToSelector, scrollToTop } from '@/shared/utils/scroll';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  Table,
  StatusBadge,
  ActionMenu,
  ActionMenuItem,
  Typography,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import { NotificationUtil } from '@/shared/utils/notification';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import {
  useEmailTemplatesAdapter,
  useBulkDeleteEmailTemplatesAdapter,
} from '../../hooks/email/useEmailTemplatesAdapter';
import { CreateEmailTemplateForm } from '../../components/email/CreateEmailTemplateForm';
import { EditEmailTemplateForm } from '../../components/email/EditEmailTemplateForm';
import type {
  EmailTemplateQueryDto,
  EmailTemplateDto,
  EmailTemplateStatus,
  EmailTemplateType,
} from '../../types/email.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý Email Templates
 */
export function EmailTemplatesPage() {
  const { t } = useTranslation('marketing');
  const { success, error: showError } = useSmartNotification();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplateDto | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Sử dụng hook animation cho form
  const {
    isVisible: isCreateVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();
  const {
    isVisible: isEditVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Mutations
  const bulkDeleteMutation = useBulkDeleteEmailTemplatesAdapter();

  // Kiểm tra nếu có action=create trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'create') {
      showCreateForm();
      setSearchParams({});
    }
  }, [searchParams, showCreateForm, setSearchParams]);

  // Xử lý chỉnh sửa template với scroll đến form
  const handleEditTemplate = useCallback(
    (template: EmailTemplateDto) => {
      setSelectedTemplate(template);
      showEditForm();

      // Scroll đến form sau khi animation hoàn thành (350ms)
      setTimeout(() => {
        // Debug: Kiểm tra xem form có tồn tại không
        const formElement = document.querySelector('.email-template-edit-form');
        console.log('🔍 Form element found:', formElement);
        console.log('🔍 Current scroll position:', window.pageYOffset);

        if (formElement) {
          // Scroll đến form edit (không highlight)
          scrollToSelector('.email-template-edit-form', {
            behavior: 'smooth',
            offset: 0,
          });
          console.log(
            '✅ Scrolled to edit form using scrollToSelector for template:',
            template.name
          );
        } else {
          // Fallback: scroll lên đầu trang
          scrollToTop({
            offset: 0,
          });
          console.log('✅ Fallback: Scrolled to top for template edit:', template.name);
        }

        // Debug: Kiểm tra scroll position sau khi scroll
        setTimeout(() => {
          console.log('🔍 New scroll position:', window.pageYOffset);
        }, 500);
      }, 350); // Delay 350ms để chờ animation hoàn thành
    },
    [showEditForm]
  );

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('marketing:email.templates.selectToDelete', 'Vui lòng chọn template để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await bulkDeleteMutation.mutateAsync(selectedRowKeys as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error bulk deleting templates:', error);
    }
  }, [selectedRowKeys, bulkDeleteMutation]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<EmailTemplateDto>[]>(
    () => [
      {
        key: 'template',
        title: t('marketing:email.templates.table.template', 'Template'),
        dataIndex: 'name',
        render: (value: unknown, record: EmailTemplateDto) => (
          <div className="flex items-center space-x-3">
            <div>
              <Typography variant="body2" className="font-medium">
                {String(value || '')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {record.subject}
              </Typography>
            </div>
          </div>
        ),
      },
      {
        key: 'type',
        title: t('marketing:email.templates.table.type', 'Loại'),
        dataIndex: 'type',
        sortable: true,
        render: (value: unknown) => {
          const type = value as string;
          const typeVariants: Record<
            string,
            'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
          > = {
            NEWSLETTER: 'info',
            PROMOTIONAL: 'warning',
            TRANSACTIONAL: 'success',
            WELCOME: 'primary',
            ABANDONED_CART: 'danger',
            FOLLOW_UP: 'default',
          };
          return (
            <StatusBadge
              variant={
                typeVariants[type] && typeVariants[type] !== 'default'
                  ? (typeVariants[type] as 'warning' | 'info' | 'primary' | 'success' | 'danger')
                  : 'primary'
              }
              text={type}
            />
          );
        },
      },
      {
        key: 'status',
        title: t('marketing:email.templates.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          switch (status) {
            case 'ACTIVE':
              return (
                <StatusBadge
                  variant="success"
                  text={t('marketing:email.templates.status.active', 'Hoạt động')}
                />
              );
            case 'DRAFT':
              return (
                <StatusBadge
                  variant="warning"
                  text={t('marketing:email.templates.status.draft', 'Bản nháp')}
                />
              );
            case 'ARCHIVED':
              return (
                <StatusBadge
                  variant="info"
                  text={t('marketing:email.templates.status.archived', 'Đã lưu trữ')}
                />
              );
            default:
              return <StatusBadge variant="info" text={status} />;
          }
        },
      },
      {
        key: 'tags',
        title: t('marketing:email.templates.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags?.slice(0, 2).map((tag: string) => (
                <StatusBadge key={tag} variant="info" text={tag} />
              ))}
              {tags?.length > 2 && <StatusBadge variant="info" text={`+${tags.length - 2}`} />}
            </div>
          );
        },
      },
      {
        key: 'variables',
        title: t('marketing:email.templates.table.variables', 'Biến'),
        dataIndex: 'variables',
        render: (value: unknown) => {
          const variables = value as Array<{ name: string; type: string; required: boolean }>;
          return <Typography variant="caption">{variables?.length || 0} biến</Typography>;
        },
      },
      {
        key: 'actions',
        title: t('marketing:email.templates.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: EmailTemplateDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common.edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEditTemplate(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="200px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEditTemplate]
  );

  // Tạo filterOptions với các tùy chọn lọc cần thiết
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      // Status filters
      {
        id: 'active',
        label: t('marketing:email.templates.status.active', 'Hoạt động'),
        icon: 'check-circle',
        value: 'ACTIVE',
      },
      {
        id: 'draft',
        label: t('marketing:email.templates.status.draft', 'Bản nháp'),
        icon: 'edit',
        value: 'DRAFT',
      },
      {
        id: 'archived',
        label: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
        icon: 'archive',
        value: 'ARCHIVED',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): EmailTemplateQueryDto => {
      console.log('🔄 [EmailTemplatesPage] createQueryParams called with:', params);

      const queryParams: EmailTemplateQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      // Xử lý filter cho status và type
      if (params.filterValue && params.filterValue !== 'all') {
        const filterValue = params.filterValue as string;
        console.log('🎯 [EmailTemplatesPage] Filter value detected:', filterValue);

        // Kiểm tra xem filter value có phải là status không
        if (['ACTIVE', 'DRAFT', 'ARCHIVED'].includes(filterValue)) {
          console.log('📊 [EmailTemplatesPage] Setting status filter:', filterValue);
          queryParams.status = filterValue as EmailTemplateStatus;
          console.log('📊 [EmailTemplatesPage] Status set to:', queryParams.status);
        }
        // Kiểm tra xem filter value có phải là type không
        else if (['WELCOME', 'PROMOTIONAL', 'NEWSLETTER', 'TRANSACTIONAL'].includes(filterValue)) {
          console.log('🏷️ [EmailTemplatesPage] Setting type filter:', filterValue);
          queryParams.type = filterValue as EmailTemplateType;
          console.log('🏷️ [EmailTemplatesPage] Type set to:', queryParams.type);
        }
      }

      console.log('✅ [EmailTemplatesPage] Final query params:', queryParams);
      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<EmailTemplateDto, EmailTemplateQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // API calls
  const {
    data: templatesData,
    isLoading,
    refetch,
  } = useEmailTemplatesAdapter(dataTable.queryParams);

  // Handler cho reload data
  const handleReload = useCallback(async () => {
    try {
      await refetch();
      success({ message: t('common:reloadSuccess', 'Tải lại dữ liệu thành công') });
    } catch (error) {
      showError({ message: t('common:reloadError', 'Lỗi khi tải lại dữ liệu') });
    }
  }, [refetch, success, showError, t]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        // Status filters
        ACTIVE: t('marketing:email.templates.status.active', 'Hoạt động'),
        DRAFT: t('marketing:email.templates.status.draft', 'Bản nháp'),
        ARCHIVED: t('marketing:email.templates.status.archived', 'Đã lưu trữ'),
        // Type filters
        WELCOME: t('marketing:email.templates.type.welcome', 'Chào mừng'),
        PROMOTIONAL: t('marketing:email.templates.type.promotional', 'Khuyến mãi'),
        NEWSLETTER: t('marketing:email.templates.type.newsletter', 'Newsletter'),
        TRANSACTIONAL: t('marketing:email.templates.type.transactional', 'Giao dịch'),
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    setSearchParams({});
  };

  const handleEditSuccess = () => {
    hideEditForm();
    setSelectedTemplate(null);
  };

  const handleEditCancel = () => {
    hideEditForm();
    setSelectedTemplate(null);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        columnLabelMap={{
          template: t('marketing:email.templates.table.template', 'Template'),
          type: t('marketing:email.templates.table.type', 'Loại'),
          status: t('marketing:email.templates.table.status', 'Trạng thái'),
          tags: t('marketing:email.templates.table.tags', 'Tags'),
          variables: t('marketing:email.templates.table.variables', 'Biến'),
          actions: t('marketing:email.templates.table.actions', 'Thao tác'),
        }}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'refresh-cw',
            tooltip: t('common:reload', 'Tải lại dữ liệu'),
            variant: 'secondary',
            onClick: handleReload,
            className: 'text-blue-500',
          },
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho tạo template */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h3">
              {t('marketing:email.templates.create.title', 'Tạo Email Template')}
            </Typography>
          </div>
          <CreateEmailTemplateForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho chỉnh sửa template */}
      <SlideInForm isVisible={isEditVisible}>
        <div className="space-y-4">
          <div>
            <Typography variant="h3">
              {t('marketing:email.templates.edit.title', 'Chỉnh sửa Email Template')}
            </Typography>
          </div>
          {selectedTemplate && (
            <div className="email-template-edit-form">
              <EditEmailTemplateForm
                templateId={selectedTemplate.id}
                onSuccess={handleEditSuccess}
                onCancel={handleEditCancel}
              />
            </div>
          )}
        </div>
      </SlideInForm>

      {/* Templates Table */}
      <Card className="overflow-hidden">
        <Table<EmailTemplateDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={templatesData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: templatesData?.meta.currentPage || 1,
            pageSize: templatesData?.meta.itemsPerPage || 10,
            total: templatesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t(
          'marketing:email.templates.confirmBulkDeleteMessage',
          'Bạn có chắc chắn muốn xóa {{count}} template đã chọn?',
          { count: selectedRowKeys.length }
        )}
      />
    </div>
  );
}

export default EmailTemplatesPage;
