# Stage 1: Build ứng dụng
FROM node:22.14.0-alpine AS build

# Thiết lập thư mục làm việc
WORKDIR /app

# Thiết lập biến môi trường
ENV NODE_ENV=production
ENV VITE_NODE_ENV=production
ENV NODE_OPTIONS=--max-old-space-size=5120
ENV VITE_BASE_URL=/
ENV CI=true
ENV BROWSER=none

# Sao chép package.json và package-lock.json
COPY package*.json ./

# Cài đặt dependencies với các tùy chọn để tránh lỗi
RUN npm ci --legacy-peer-deps

# Sao chép toàn bộ mã nguồn
COPY . .

# Tạo file .env từ .env.production nếu tồn tại, nếu không sử dụng .env.development
RUN if [ -f .env.production ]; then \
      cp .env.production .env; \
    elif [ -f .env.development ]; then \
      cp .env.development .env; \
    fi

# Build ứng dụng
RUN npm run build

# Stage 2: Chạy ứng dụng với Nginx
FROM nginx:alpine

# Sao chép các file đã build từ stage trước vào thư mục web root của Nginx
COPY --from=build /app/dist /usr/share/nginx/html

# Cấu hình Nginx để xử lý SPA routing
RUN echo 'server { \
    listen 80; \
    server_name _; \
    root /usr/share/nginx/html; \
    index index.html; \
    # Cấu hình cho các tài nguyên tĩnh \
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ { \
        expires 1y; \
        add_header Cache-Control "public, max-age=********"; \
        try_files $uri $uri/ /index.html; \
        access_log off; \
    } \
    # Cấu hình cho các tài nguyên trong thư mục assets \
    location /assets/ { \
        alias /usr/share/nginx/html/assets/; \
        try_files $uri $uri/ /index.html; \
        expires 1y; \
        add_header Cache-Control "public, max-age=********"; \
        access_log off; \
    } \
    # Cấu hình cho các tài nguyên trong thư mục src/assets \
    location /src/assets/ { \
        alias /usr/share/nginx/html/assets/; \
        try_files $uri $uri/ /index.html; \
        expires 1y; \
        add_header Cache-Control "public, max-age=********"; \
        access_log off; \
    } \
    location / { \
        try_files $uri $uri/ /index.html; \
        add_header Cache-Control "no-store, no-cache, must-revalidate"; \
    } \
    # Cấu hình gzip \
    gzip on; \
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript; \
    gzip_comp_level 9; \
    gzip_min_length 1000; \
}' > /etc/nginx/conf.d/default.conf

# Mở cổng 80
EXPOSE 80

# Khởi động Nginx
CMD ["nginx", "-g", "daemon off;"]
