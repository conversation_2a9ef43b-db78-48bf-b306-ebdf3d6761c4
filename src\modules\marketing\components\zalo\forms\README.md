# Zalo Personal Campaign Forms

Th<PERSON> mục này chứa các form components cho việc tạo các loại chiến dịch Zalo cá nhân khác nhau.

## Components

### CrawFriendsListForm

Form để tạo chiến dịch "Craw danh sách bạn bè".

**Props:**
- `onSubmit: (data: CrawFriendsListFormData) => void` - Callback khi submit form
- `onCancel?: () => void` - Callback khi hủy form (optional)

**Form Fields:**
- `campaignName: string` - Tên chiến dịch (bắt buộc)
- `accountIds: string[]` - Danh sách ID tài khoản <PERSON>alo (bắt buộc, multiple selection)

**Features:**
- Validation với Zod schema
- Sử dụng `AsyncSelectWithPagination` cho việc chọn tài khoản
- <PERSON><PERSON><PERSON> hợ<PERSON> với API `/marketing/user/zalo-personal/integrations`
- Hỗ trợ đa ngôn ngữ (i18n)
- Responsive design

### CrawGroupMembersForm

Form để tạo chiến dịch "Craw nhóm".

**Props:**
- `onSubmit: (data: CrawGroupMembersFormData) => void` - Callback khi submit form
- `onCancel?: () => void` - Callback khi hủy form (optional)

**Form Fields:**
- `campaignName: string` - Tên chiến dịch (bắt buộc)
- `accountIds: string[]` - Danh sách ID tài khoản Zalo (bắt buộc, multiple selection)
- `groupIds: string[]` - Danh sách ID nhóm Zalo (bắt buộc, nhập thủ công)

**Features:**
- Validation với Zod schema
- Sử dụng `AsyncSelectWithPagination` cho việc chọn tài khoản
- Input tùy chỉnh cho việc nhập danh sách Group IDs (cách nhau bằng dấu phẩy)
- Tích hợp với API `/marketing/user/zalo-personal/integrations`
- Hỗ trợ đa ngôn ngữ (i18n)
- Responsive design

### FriendRequestToPhoneForm

Form để tạo chiến dịch "Inbox, kết bạn đến SĐT & bạn bè" - gộp tính năng gửi tin nhắn và kết bạn.

## Usage

```tsx
import CrawFriendsListForm from './forms/CrawFriendsListForm';

const handleSubmit = (data) => {
  console.log('Form data:', data);
  // Handle form submission
};

const handleCancel = () => {
  // Handle form cancellation
};

<CrawFriendsListForm
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

## Integration

Form được tích hợp vào `CreateZaloPersonalCampaignForm` và sẽ được hiển thị khi user chọn campaign type `CRAWL_FRIENDS_LIST`.

## Development

### Adding New Forms

1. Tạo component form mới trong thư mục này
2. Implement interface props và validation schema
3. Thêm translation keys vào các file locale
4. Import và sử dụng trong `CreateZaloPersonalCampaignForm`
5. Thêm export vào `src/modules/marketing/index.ts`

### Testing

Chạy tests:
```bash
npm test -- --testPathPattern=forms
```
