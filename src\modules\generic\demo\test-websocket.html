<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generic WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Generic WebSocket Test</h1>
    
    <div id="status" class="status disconnected">
        Disconnected
    </div>
    
    <div>
        <button id="connectBtn">Connect</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
        <button id="addWidgetBtn" disabled>Add Demo Widget</button>
        <button id="clearLogBtn">Clear Log</button>
    </div>
    
    <h3>WebSocket Log:</h3>
    <div id="log" class="log"></div>
    
    <script>
        let ws = null;
        let sessionId = 'test-session-' + Date.now();
        
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const addWidgetBtn = document.getElementById('addWidgetBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(connected) {
            if (connected) {
                statusEl.textContent = `Connected (Session: ${sessionId})`;
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                addWidgetBtn.disabled = false;
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                addWidgetBtn.disabled = true;
            }
        }
        
        function connect() {
            const wsUrl = `ws://localhost:8081/ws/generic?sessionId=${sessionId}`;
            log(`Connecting to: ${wsUrl}`);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                log('✅ WebSocket connected');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`📥 Received: ${JSON.stringify(data, null, 2)}`);
                } catch (error) {
                    log(`📥 Received (raw): ${event.data}`);
                }
            };
            
            ws.onclose = function() {
                log('🔌 WebSocket disconnected');
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                log(`❌ WebSocket error: ${error}`);
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function addDemoWidget() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket not connected');
                return;
            }
            
            const widget = {
                id: 'demo-widget-' + Date.now(),
                title: 'Demo Widget',
                type: 'data-count',
                x: 0,
                y: 0,
                w: 4,
                h: 3,
                props: { count: Math.floor(Math.random() * 1000), label: 'Random Count' }
            };
            
            const event = {
                type: 'add_widget',
                payload: { widget },
                sessionId: sessionId,
                timestamp: new Date().toISOString()
            };
            
            log(`📤 Sending: ${JSON.stringify(event, null, 2)}`);
            ws.send(JSON.stringify(event));
        }
        
        function clearLog() {
            logEl.innerHTML = '';
        }
        
        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        addWidgetBtn.addEventListener('click', addDemoWidget);
        clearLogBtn.addEventListener('click', clearLog);
        
        // Initial log
        log('WebSocket test page loaded');
        log(`Session ID: ${sessionId}`);
    </script>
</body>
</html>
