import React from 'react';
import { Plus } from 'lucide-react';
import { EmailElement as EmailElementType } from '../types';
import EmailElement from './EmailElement';

interface DropZoneProps {
  children?: EmailElementType[];
  containerType: string;
  containerStyle?: React.CSSProperties;
  className?: string;
  placeholder?: string;
  onAddElement?: (elementType: string) => void;
  onSelectElement?: (element: EmailElementType, index: number) => void;
  onDeleteElement?: (index: number) => void;
  onMoveElementUp?: (index: number) => void;
  onMoveElementDown?: (index: number) => void;
  onUpdateElement?: (index: number, property: string, value: unknown) => void;
  selectedElement?: EmailElementType | null;
  selectedIndex?: number | null;
}

const DropZone: React.FC<DropZoneProps> = ({
  children = [],
  containerType,
  containerStyle = {},
  className = '',
  placeholder = 'Thả elements vào đây',
  onAddElement,
  onSelectElement,
  onDeleteElement,
  onMoveElementUp,
  onMoveElementDown,
  onUpdateElement,
  selectedElement,
  selectedIndex,
}) => {
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const elementType = e.dataTransfer.getData('text/plain');
    if (elementType && onAddElement) {
      onAddElement(elementType);
    }
  };

  const baseClassName = `
    min-h-[80px] 
    border-2 border-dashed border-gray-300 dark:border-gray-600 
    rounded-lg 
    transition-colors 
    hover:border-blue-400 hover:bg-blue-50/10 dark:hover:bg-blue-900/10
    ${className}
  `.trim();

  return (
    <div
      className={baseClassName}
      style={containerStyle}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {children.length > 0 ? (
        <div className="space-y-2">
          {children.map((child, index) => (
            <EmailElement
              key={child.id}
              element={child}
              index={index}
              isSelected={selectedElement?.id === child.id}
              onSelect={(element, elementIndex) => {
                if (onSelectElement) {
                  onSelectElement(element, elementIndex);
                }
              }}
              onDelete={() => {
                if (onDeleteElement) {
                  onDeleteElement(index);
                }
              }}
              onMoveUp={() => {
                if (onMoveElementUp) {
                  onMoveElementUp(index);
                }
              }}
              onMoveDown={() => {
                if (onMoveElementDown) {
                  onMoveElementDown(index);
                }
              }}
              onUpdateElement={(property, value) => {
                if (onUpdateElement) {
                  onUpdateElement(index, property, value);
                }
              }}
              totalElements={children.length}
            />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-full py-8 text-gray-500 dark:text-gray-400">
          <div className="flex items-center justify-center w-12 h-12 mb-3 bg-gray-100 dark:bg-gray-700 rounded-full">
            <Plus size={20} />
          </div>
          <div className="text-sm font-medium">{placeholder}</div>
          <div className="text-xs mt-1 text-center">
            Kéo thả elements từ panel bên trái
          </div>
        </div>
      )}
    </div>
  );
};

export default DropZone;
