import React, { useRef } from 'react';
import { EmailElement as EmailElementType } from '../types';
import { Typography } from '@/shared/components/common';
import EmailElement from './EmailElement';

interface EmailCanvasProps {
  emailElements: EmailElementType[];
  selectedElement: EmailElementType | null;
  selectedIndex: number | null;
  viewMode: 'desktop' | 'tablet' | 'mobile';
  onSelectElement: (element: EmailElementType | null, index: number) => void;
  onDeleteElement: (index: number) => void;
  onMoveElementUp: () => void;
  onMoveElementDown: () => void;
  onUpdateElement: (property: string, value: unknown) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  onAddElementToContainer?: (
    containerIndex: number,
    elementType: string,
    columnPosition?: 'left' | 'right'
  ) => void;
  onUpdateNestedElement?: (
    containerIndex: number,
    childIndex: number,
    property: string,
    value: unknown
  ) => void;
  onDeleteNestedElement?: (containerIndex: number, childIndex: number) => void;
  onMoveNestedElementUp?: (containerIndex: number, childIndex: number) => void;
  onMoveNestedElementDown?: (containerIndex: number, childIndex: number) => void;
  showPreview: boolean;
}

const EmailCanvas: React.FC<EmailCanvasProps> = ({
  emailElements,
  selectedElement,
  selectedIndex,
  viewMode,
  onSelectElement,
  onDeleteElement,
  onMoveElementUp,
  onMoveElementDown,
  onUpdateElement,
  onDrop,
  onAddElementToContainer,
  onUpdateNestedElement,
  onDeleteNestedElement,
  onMoveNestedElementUp,
  onMoveNestedElementDown,
  showPreview,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);

  // Xử lý khi kéo phần tử vào canvas
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  // Lấy chiều rộng canvas dựa trên chế độ xem
  const getCanvasWidth = () => {
    switch (viewMode) {
      case 'mobile':
        return '375px';
      case 'tablet':
        return '768px';
      case 'desktop':
      default:
        return '100%';
    }
  };

  return (
    <div className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900 p-6">
      <div
        ref={canvasRef}
        className={`mx-auto bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${showPreview ? 'pointer-events-none' : ''}`}
        style={{
          width: getCanvasWidth(),
          maxWidth: '100%',
          minHeight: '600px',
          transition: 'all 0.3s ease-in-out',
        }}
        onDragOver={handleDragOver}
        onDrop={onDrop}
        onClick={() => {
          // Bỏ chọn phần tử khi click vào khu vực trống
          if (selectedElement) {
            onSelectElement(null, -1);
          }
        }}
      >
        {emailElements.length === 0 ? (
          <div className="flex items-center justify-center h-full min-h-[400px] text-gray-500 dark:text-gray-400 text-center p-8">
            <div className="max-w-md">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </div>
              <Typography variant="h6" className="mb-2 text-gray-700 dark:text-gray-300">
                Bắt đầu thiết kế email
              </Typography>
              <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                Kéo và thả các phần tử từ panel bên trái để tạo email của bạn
              </Typography>
            </div>
          </div>
        ) : (
          <div className="min-h-full pt-10">
            {emailElements.map((element, index) => (
              <EmailElement
                key={element.id}
                element={element}
                index={index}
                isSelected={selectedIndex === index}
                onSelect={onSelectElement}
                onDelete={() => onDeleteElement(index)}
                onMoveUp={onMoveElementUp}
                onMoveDown={onMoveElementDown}
                onUpdateElement={onUpdateElement}
                onAddElementToContainer={
                  onAddElementToContainer
                    ? (elementType, columnPosition) =>
                        onAddElementToContainer(index, elementType, columnPosition)
                    : undefined
                }
                onUpdateNestedElement={
                  onUpdateNestedElement
                    ? (childIndex, property, value) =>
                        onUpdateNestedElement(index, childIndex, property, value)
                    : undefined
                }
                onDeleteNestedElement={
                  onDeleteNestedElement
                    ? childIndex => onDeleteNestedElement(index, childIndex)
                    : undefined
                }
                onMoveNestedElementUp={
                  onMoveNestedElementUp
                    ? childIndex => onMoveNestedElementUp(index, childIndex)
                    : undefined
                }
                onMoveNestedElementDown={
                  onMoveNestedElementDown
                    ? childIndex => onMoveNestedElementDown(index, childIndex)
                    : undefined
                }
                totalElements={emailElements.length}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailCanvas;
