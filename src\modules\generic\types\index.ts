/**
 * Generic Page Types
 * Định nghĩa types cho trang generic với WebSocket integration
 */

import { DashboardWidget } from '@/modules/dashboard/types';

export interface GenericWidget extends Omit<DashboardWidget, 'id'> {
  id: string;
  sessionId?: string; // Session ID để track widget từ backend
  createdAt?: string;
  updatedAt?: string;
}

export interface GenericPageState {
  widgets: GenericWidget[];
  layout: GenericLayout[];
  isConnected: boolean;
  sessionId: string | null;
}

export interface GenericLayout {
  i: string; // widget id
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
}

// WebSocket Events
export interface GenericWebSocketEvent {
  type: 'add_widget' | 'remove_widget' | 'update_layout' | 'sync_state';
  payload: any;
  sessionId: string;
  timestamp: string;
}

export interface AddWidgetEvent extends GenericWebSocketEvent {
  type: 'add_widget';
  payload: {
    widget: GenericWidget;
    position?: { x: number; y: number };
  };
}

export interface RemoveWidgetEvent extends GenericWebSocketEvent {
  type: 'remove_widget';
  payload: {
    widgetId: string;
  };
}

export interface UpdateLayoutEvent extends GenericWebSocketEvent {
  type: 'update_layout';
  payload: {
    layout: GenericLayout[];
  };
}

export interface SyncStateEvent extends GenericWebSocketEvent {
  type: 'sync_state';
  payload: {
    widgets: GenericWidget[];
    layout: GenericLayout[];
  };
}

// API Types
export interface GenericPageResponse {
  widgets: GenericWidget[];
  layout: GenericLayout[];
  sessionId: string;
}

export interface CreateGenericWidgetRequest {
  widgetType: string;
  title: string;
  position?: { x: number; y: number };
  size?: { w: number; h: number };
  props?: Record<string, unknown>;
}

export interface UpdateGenericLayoutRequest {
  layout: GenericLayout[];
}
