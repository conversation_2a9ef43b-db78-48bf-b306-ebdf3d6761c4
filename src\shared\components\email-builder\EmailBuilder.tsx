import React, { useState, useEffect, useRef } from 'react';
import { EmailBuilderProps, EmailElement, HistoryState } from './types';
import { generateHTML } from './utils';
import { EmailCanvas } from './components';
import EmailBuilderHeader from './components/EmailBuilderHeader';

const EmailBuilder: React.FC<EmailBuilderProps> = ({
  initialValue,
  onContentChange,
  // compactMode = false
}) => {
  const [selectedElement, setSelectedElement] = useState<EmailElement | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [emailElements, setEmailElements] = useState<EmailElement[]>([]);

  // Email data for generating HTML
  const emailData = { subject: '', previewText: '' };

  // State variables for enhanced functionality
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Nạp dữ liệu từ initialValue nếu có
  useEffect(() => {
    if (initialValue && initialValue.trim() !== '' && emailElements.length === 0) {
      // Nếu có initialValue và không có phần tử nào, tạo một phần tử text mặc định
      const newElements = [
        {
          id: `content-${Date.now()}`,
          type: 'text',
          content: initialValue,
          style: {
            color: '#333333',
            textAlign: 'left',
            fontSize: 16,
            lineHeight: 1.5,
            padding: 16,
          },
        },
      ];

      setEmailElements(newElements);
      // Khởi tạo history
      setHistory([{ emailElements: newElements, selectedIndex: null }]);
      setHistoryIndex(0);
    }
  }, [initialValue, emailElements.length]);

  // Sử dụng useRef để lưu trữ onContentChange callback
  const onContentChangeRef = useRef(onContentChange);

  // Cập nhật ref khi onContentChange thay đổi
  useEffect(() => {
    onContentChangeRef.current = onContentChange;
  }, [onContentChange]);

  // Cập nhật nội dung HTML khi emailElements thay đổi
  useEffect(() => {
    if (onContentChangeRef.current && emailElements.length > 0) {
      // Tạo HTML từ các phần tử
      const html = generateHTML(emailElements, emailData);
      // Gọi callback
      onContentChangeRef.current(html);
    }
  }, [emailElements, emailData]);

  // Thêm vào history khi có thay đổi
  const addToHistory = (elements: EmailElement[], index: number | null) => {
    // Cắt bỏ history sau vị trí hiện tại nếu đang ở giữa
    const newHistory = history.slice(0, historyIndex + 1);
    // Thêm trạng thái mới
    newHistory.push({ emailElements: JSON.parse(JSON.stringify(elements)), selectedIndex: index });
    // Cập nhật history và index
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Tạo element mới với config mặc định
  const createNewElement = (
    type: string,
    additionalProps: Partial<EmailElement> = {}
  ): EmailElement => {
    const id = `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newElement: EmailElement = {
      id,
      type,
      draggable: true,
      removable: true,
      editable: true,
      selectable: true,
      hoverable: true,
      copyable: true,
      style: {
        padding: 8,
        margin: 4,
      },
      ...additionalProps,
    };

    // Cấu hình mặc định cho từng loại phần tử
    switch (type) {
      case 'text':
        newElement.content = 'Nhấp đôi để chỉnh sửa văn bản này';
        newElement.style = {
          ...newElement.style,
          color: '#333333',
          fontSize: 16,
          textAlign: 'left',
          paddingTop: 8,
          paddingBottom: 8,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        };
        break;

      case 'heading':
        newElement.content = 'Tiêu đề mẫu';
        newElement.headingType = 'h2';
        newElement.style = {
          ...newElement.style,
          color: '#111111',
          fontSize: 24,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 16,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          marginBottom: 16,
          fontFamily: 'Arial, sans-serif',
        };
        break;

      case 'image':
        newElement.src = 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu';
        newElement.alt = 'Hình ảnh mẫu';
        newElement.style = {
          ...newElement.style,
          width: '100%',
          paddingTop: 8,
          paddingBottom: 8,
          display: 'block',
        };
        break;

      case 'button':
        newElement.text = 'Nút nhấn';
        newElement.url = '#';
        newElement.style = {
          ...newElement.style,
          backgroundColor: '#0070f3',
          color: '#ffffff',
          padding: 12,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          width: '200px',
          margin: '16px auto',
        };
        break;

      // Thêm các loại phần tử khác nếu cần
    }

    return newElement;
  };

  // Thêm phần tử mới vào email (root level)
  const addNewElement = (type: string) => {
    const newElement = createNewElement(type);
    const newElements = [...emailElements, newElement];
    setEmailElements(newElements);

    // Chọn phần tử mới thêm
    setSelectedElement(newElement);
    setSelectedIndex(newElements.length - 1);

    // Thêm vào history
    addToHistory(newElements, newElements.length - 1);
  };

  // Thêm phần tử vào container (nested)
  const addElementToContainer = (
    containerIndex: number,
    elementType: string,
    columnPosition?: 'left' | 'right'
  ) => {
    const newElement = createNewElement(elementType, { columnPosition });

    const newElements = [...emailElements];
    const container = newElements[containerIndex];

    if (container) {
      if (!container.children) {
        container.children = [];
      }
      container.children.push(newElement);

      setEmailElements(newElements);
      addToHistory(newElements, containerIndex);
    }
  };

  // Update nested element trong container
  const updateNestedElement = (
    containerIndex: number,
    childIndex: number,
    property: string,
    value: unknown
  ) => {
    const newElements = [...emailElements];
    const container = newElements[containerIndex];

    if (container && container.children && container.children[childIndex]) {
      const child = container.children[childIndex];

      if (property === 'style' && typeof value === 'object' && value !== null) {
        child.style = { ...child.style, ...value };
      } else {
        (child as any)[property] = value;
      }

      setEmailElements(newElements);
      addToHistory(newElements, containerIndex);
    }
  };

  // Delete nested element từ container
  const deleteNestedElement = (containerIndex: number, childIndex: number) => {
    const newElements = [...emailElements];
    const container = newElements[containerIndex];

    if (container && container.children) {
      container.children.splice(childIndex, 1);
      setEmailElements(newElements);
      addToHistory(newElements, containerIndex);
    }
  };

  // Move nested element trong container
  const moveNestedElementUp = (containerIndex: number, childIndex: number) => {
    if (childIndex <= 0) return;

    const newElements = [...emailElements];
    const container = newElements[containerIndex];

    if (container && container.children) {
      const temp = container.children[childIndex];
      container.children[childIndex] = container.children[childIndex - 1];
      container.children[childIndex - 1] = temp;

      setEmailElements(newElements);
      addToHistory(newElements, containerIndex);
    }
  };

  const moveNestedElementDown = (containerIndex: number, childIndex: number) => {
    const newElements = [...emailElements];
    const container = newElements[containerIndex];

    if (container && container.children && childIndex < container.children.length - 1) {
      const temp = container.children[childIndex];
      container.children[childIndex] = container.children[childIndex + 1];
      container.children[childIndex + 1] = temp;

      setEmailElements(newElements);
      addToHistory(newElements, containerIndex);
    }
  };

  // Chọn phần tử để chỉnh sửa
  const handleSelectElement = (element: EmailElement, index: number) => {
    // Nếu đang chọn phần tử khác, lưu trạng thái hiện tại vào history
    if (selectedIndex !== index && selectedIndex !== null) {
      addToHistory(emailElements, selectedIndex);
    }

    setSelectedElement(element);
    setSelectedIndex(index);
  };

  // Xử lý khi thả phần tử vào canvas
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const elementType = e.dataTransfer.getData('text/plain');
    if (elementType) {
      addNewElement(elementType);
    }
  };

  // Cập nhật thuộc tính của phần tử được chọn
  const updateSelectedElement = (property: string, value: unknown) => {
    console.log('updateSelectedElement called:', {
      property,
      value,
      selectedIndex,
      elementType: selectedElement?.type,
    });

    if (selectedElement && selectedIndex !== null) {
      const updatedElement = { ...selectedElement };

      if (property.includes('.')) {
        const [parentProp, child] = property.split('.');
        if (parentProp) {
          if (!updatedElement[parentProp as keyof typeof updatedElement]) {
            updatedElement[parentProp as keyof typeof updatedElement] = {};
          } else {
            // Sử dụng type assertion để đảm bảo có thể spread object
            const currentValue = updatedElement[parentProp as keyof typeof updatedElement];
            updatedElement[parentProp as keyof typeof updatedElement] = {
              ...(currentValue as Record<string, unknown>),
            };
          }

          // Safely update the nested property
          const parentObj = updatedElement[parentProp as keyof typeof updatedElement] as Record<
            string,
            unknown
          >;
          if (parentObj && child) {
            parentObj[child] = value;
          }
        }
      } else {
        // Sử dụng type assertion với EmailElement có thuộc tính động
        updatedElement[property] = value;
      }

      // Cập nhật phần tử trong emailElements
      const newElements = [...emailElements];
      newElements[selectedIndex] = updatedElement;
      setEmailElements(newElements);
      setSelectedElement(updatedElement);

      console.log('Element updated:', {
        property,
        value,
        selectedIndex,
        updatedElement: updatedElement.style,
      });
    }
  };

  // Xóa phần tử theo index
  const deleteElementByIndex = (index: number) => {
    console.log('🗑️ deleteElementByIndex called with index:', index);
    console.log('Current emailElements length:', emailElements.length);
    console.log('Element to delete:', emailElements[index]);
    console.log('Is last element:', index === emailElements.length - 1);

    // Lưu trạng thái trước khi xóa
    addToHistory(emailElements, index);

    const newElements = [...emailElements];
    newElements.splice(index, 1);
    setEmailElements(newElements);

    console.log('✅ After delete, new length:', newElements.length);

    // Reset selection nếu element bị xóa đang được chọn
    if (selectedIndex === index) {
      setSelectedElement(null);
      setSelectedIndex(null);
      console.log('Reset selection because deleted element was selected');
    } else if (selectedIndex !== null && selectedIndex > index) {
      // Điều chỉnh selectedIndex nếu element trước đó bị xóa
      setSelectedIndex(selectedIndex - 1);
      console.log('Adjusted selectedIndex to:', selectedIndex - 1);
    }
  };

  // Di chuyển phần tử lên trên
  const moveElementUp = () => {
    if (selectedIndex !== null && selectedIndex > 0) {
      // Lưu trạng thái trước khi di chuyển
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      const temp = newElements[selectedIndex];
      newElements[selectedIndex] = newElements[selectedIndex - 1] as EmailElement;
      newElements[selectedIndex - 1] = temp as EmailElement;

      setEmailElements(newElements);
      setSelectedIndex(selectedIndex - 1);
      setSelectedElement(temp as EmailElement);
    }
  };

  // Di chuyển phần tử xuống dưới
  const moveElementDown = () => {
    if (selectedIndex !== null && selectedIndex < emailElements.length - 1) {
      // Lưu trạng thái trước khi di chuyển
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      const temp = newElements[selectedIndex];
      newElements[selectedIndex] = newElements[selectedIndex + 1] as EmailElement;
      newElements[selectedIndex + 1] = temp as EmailElement;

      setEmailElements(newElements);
      setSelectedIndex(selectedIndex + 1);
      setSelectedElement(temp as EmailElement);
    }
  };

  return (
    <div className="email-builder flex flex-col h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      <EmailBuilderHeader onAddElement={addNewElement} />

      <div className="flex-1 flex overflow-hidden">
        <EmailCanvas
          emailElements={emailElements}
          selectedElement={selectedElement}
          selectedIndex={selectedIndex}
          viewMode="desktop"
          onSelectElement={(element, index) => {
            if (element) {
              handleSelectElement(element, index);
            } else {
              // Deselect when clicking on empty area
              setSelectedElement(null);
              setSelectedIndex(null);
            }
          }}
          onDeleteElement={deleteElementByIndex}
          onMoveElementUp={moveElementUp}
          onMoveElementDown={moveElementDown}
          onUpdateElement={updateSelectedElement}
          onDrop={handleDrop}
          onAddElementToContainer={addElementToContainer}
          onUpdateNestedElement={updateNestedElement}
          onDeleteNestedElement={deleteNestedElement}
          onMoveNestedElementUp={moveNestedElementUp}
          onMoveNestedElementDown={moveNestedElementDown}
          showPreview={false}
        />
      </div>
    </div>
  );
};

export default EmailBuilder;
