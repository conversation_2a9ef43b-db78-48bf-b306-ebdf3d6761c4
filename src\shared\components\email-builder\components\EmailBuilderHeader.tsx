import React from 'react';
import {
  Type,
  Heading1,
  Image,
  Square,
  Minus,
  ArrowUpDown,
  List,
  Link,
  Share2,
  Video,
  Undo,
  Redo,
  Save,
  Eye,
  Code,
  Download,
  Monitor,
  Tablet,
  Smartphone,
  Maximize,
  Minimize,
} from 'lucide-react';

interface EmailBuilderHeaderProps {
  emailData: {
    subject: string;
    previewText: string;
  };
  setEmailData: (data: any) => void;
  showPreview: boolean;
  setShowPreview: (show: boolean) => void;
  viewMode: 'desktop' | 'tablet' | 'mobile';
  setViewMode: (mode: 'desktop' | 'tablet' | 'mobile') => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onSaveHTML: () => void;
  onSaveHTMLWithCSS: () => void;
  onSaveGrapesJS: () => void;
  onShowCode: () => void;
  isFullscreen: boolean;
  toggleFullscreen: () => void;
  onAddElement: (elementType: string) => void;
}

const EmailBuilderHeader: React.FC<EmailBuilderHeaderProps> = ({
  emailData,
  setEmailData,
  showPreview,
  setShowPreview,
  viewMode,
  setViewMode,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onSaveHTML,
  onSaveHTMLWithCSS,
  onSaveGrapesJS,
  onShowCode,
  isFullscreen,
  toggleFullscreen,
  onAddElement,
}) => {
  const elements = [
    { type: 'text', icon: Type, label: 'Văn bản' },
    { type: 'heading', icon: Heading1, label: 'Tiêu đề' },
    { type: 'image', icon: Image, label: 'Hình ảnh' },
    { type: 'button', icon: Square, label: 'Nút nhấn' },
    { type: 'divider', icon: Minus, label: 'Đường kẻ' },
    { type: 'spacer', icon: ArrowUpDown, label: 'Khoảng cách' },
    { type: 'list', icon: List, label: 'Danh sách' },
    { type: 'link', icon: Link, label: 'Liên kết' },
    { type: 'social', icon: Share2, label: 'Mạng xã hội' },
    { type: 'video', icon: Video, label: 'Video' },
  ];

  const containers = [
    { type: 'header', label: 'Header' },
    { type: 'footer', label: 'Footer' },
    { type: '1column', label: '1 Cột' },
    { type: '2columns', label: '2 Cột' },
  ];

  return (
    <div className="bg-gray-800 text-white px-4 py-3 flex items-center justify-between shadow-lg">
      {/* Left Section - Logo & Title */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
            <Type size={16} />
          </div>
          <span className="font-semibold text-lg">Email Builder</span>
        </div>
      </div>

      {/* Center Section - Elements */}
      <div className="flex items-center space-x-1">
        {/* Elements */}
        <div className="flex items-center space-x-1 px-3 py-1 bg-gray-700 rounded-lg">
          <span className="text-xs text-gray-300 mr-2">Elements:</span>
          {elements.map((element) => {
            const IconComponent = element.icon;
            return (
              <button
                key={element.type}
                onClick={() => onAddElement(element.type)}
                className="p-2 hover:bg-gray-600 rounded transition-colors group relative"
                title={element.label}
              >
                <IconComponent size={16} />
                <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  {element.label}
                </span>
              </button>
            );
          })}
        </div>

        {/* Containers */}
        <div className="flex items-center space-x-1 px-3 py-1 bg-gray-700 rounded-lg ml-2">
          <span className="text-xs text-gray-300 mr-2">Layout:</span>
          {containers.map((container) => (
            <button
              key={container.type}
              onClick={() => onAddElement(container.type)}
              className="px-3 py-1 hover:bg-gray-600 rounded transition-colors text-xs"
              title={container.label}
            >
              {container.label}
            </button>
          ))}
        </div>
      </div>

      {/* Right Section - Actions */}
      <div className="flex items-center space-x-2">
        {/* View Mode */}
        <div className="flex items-center space-x-1 bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setViewMode('desktop')}
            className={`p-1 rounded ${viewMode === 'desktop' ? 'bg-blue-600' : 'hover:bg-gray-600'}`}
            title="Desktop"
          >
            <Monitor size={16} />
          </button>
          <button
            onClick={() => setViewMode('tablet')}
            className={`p-1 rounded ${viewMode === 'tablet' ? 'bg-blue-600' : 'hover:bg-gray-600'}`}
            title="Tablet"
          >
            <Tablet size={16} />
          </button>
          <button
            onClick={() => setViewMode('mobile')}
            className={`p-1 rounded ${viewMode === 'mobile' ? 'bg-blue-600' : 'hover:bg-gray-600'}`}
            title="Mobile"
          >
            <Smartphone size={16} />
          </button>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1">
          <button
            onClick={onUndo}
            disabled={!canUndo}
            className="p-2 hover:bg-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo"
          >
            <Undo size={16} />
          </button>
          <button
            onClick={onRedo}
            disabled={!canRedo}
            className="p-2 hover:bg-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo"
          >
            <Redo size={16} />
          </button>
          <button
            onClick={() => setShowPreview(!showPreview)}
            className={`p-2 rounded ${showPreview ? 'bg-blue-600' : 'hover:bg-gray-700'}`}
            title="Preview"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={onShowCode}
            className="p-2 hover:bg-gray-700 rounded"
            title="View Code"
          >
            <Code size={16} />
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded font-medium"
            title="Save"
          >
            <Save size={16} className="mr-1" />
            Lưu
          </button>
          <button
            onClick={toggleFullscreen}
            className="p-2 hover:bg-gray-700 rounded"
            title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
          >
            {isFullscreen ? <Minimize size={16} /> : <Maximize size={16} />}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmailBuilderHeader;
