import React from 'react';
import {
  Type,
  Heading1,
  Image,
  Square,
  Minus,
  ArrowUpDown,
  List,
  Link,
  Share2,
  Video,
} from 'lucide-react';

interface EmailBuilderHeaderProps {
  onAddElement: (elementType: string) => void;
}

const EmailBuilderHeader: React.FC<EmailBuilderHeaderProps> = ({ onAddElement }) => {
  const elements = [
    { type: 'text', icon: Type, label: 'Văn bản' },
    { type: 'heading', icon: Heading1, label: 'Tiêu đề' },
    { type: 'image', icon: Image, label: 'Hình ảnh' },
    { type: 'button', icon: Square, label: 'Nút nhấn' },
    { type: 'divider', icon: Minus, label: 'Đường kẻ' },
    { type: 'spacer', icon: ArrowUpDown, label: 'Khoảng cách' },
    { type: 'list', icon: List, label: 'Danh sách' },
    { type: 'link', icon: Link, label: '<PERSON><PERSON><PERSON> kết' },
    { type: 'social', icon: Share2, label: 'Mạng xã hội' },
    { type: 'video', icon: Video, label: 'Video' },
  ];

  const containers = [
    { type: 'header', label: 'Header' },
    { type: 'footer', label: 'Footer' },
    { type: '1column', label: '1 Cột' },
    { type: '2columns', label: '2 Cột' },
  ];

  return (
    <div className="bg-gray-800 text-white px-4 py-3 flex items-center justify-center shadow-lg">
      {/* Center Section - Elements */}
      <div className="flex items-center space-x-1">
        {/* Elements */}
        <div className="flex items-center space-x-1 px-3 py-1 bg-gray-700 rounded-lg">
          <span className="text-xs text-gray-300 mr-2">Elements:</span>
          {elements.map(element => {
            const IconComponent = element.icon;
            return (
              <button
                key={element.type}
                onClick={() => onAddElement(element.type)}
                className="p-2 hover:bg-gray-600 rounded transition-colors group relative"
                title={element.label}
              >
                <IconComponent size={16} />
                <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  {element.label}
                </span>
              </button>
            );
          })}
        </div>

        {/* Containers */}
        <div className="flex items-center space-x-1 px-3 py-1 bg-gray-700 rounded-lg ml-2">
          <span className="text-xs text-gray-300 mr-2">Layout:</span>
          {containers.map(container => (
            <button
              key={container.type}
              onClick={() => onAddElement(container.type)}
              className="px-3 py-1 hover:bg-gray-600 rounded transition-colors text-xs"
              title={container.label}
            >
              {container.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmailBuilderHeader;
