import React from 'react';
import {
  Type,
  Heading1,
  Image,
  Square,
  Minus,
  ArrowUpDown,
  List,
  Link,
  Share2,
  Video,
} from 'lucide-react';

interface EmailBuilderHeaderProps {
  onAddElement: (elementType: string) => void;
}

const EmailBuilderHeader: React.FC<EmailBuilderHeaderProps> = ({ onAddElement }) => {
  const elements = [
    { type: 'text', icon: Type, label: 'Văn bản' },
    { type: 'heading', icon: Heading1, label: 'Tiêu đề' },
    { type: 'image', icon: Image, label: 'Hình ảnh' },
    { type: 'button', icon: Square, label: 'Nút nhấn' },
    { type: 'divider', icon: Minus, label: 'Đường kẻ' },
    { type: 'spacer', icon: ArrowUpDown, label: 'K<PERSON>ảng cách' },
    { type: 'list', icon: List, label: 'Danh sách' },
    { type: 'link', icon: Link, label: '<PERSON><PERSON><PERSON> kết' },
    { type: 'social', icon: Share2, label: 'Mạng xã hội' },
    { type: 'video', icon: Video, label: 'Video' },
  ];

  const containers = [
    { type: 'header', label: 'Header' },
    { type: 'footer', label: 'Footer' },
    { type: '1column', label: '1 Cột' },
    { type: '2columns', label: '2 Cột' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-4 py-3 flex items-center justify-center shadow-lg border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
      {/* Center Section - Elements */}
      <div className="flex items-center space-x-2 md:space-x-3 flex-wrap gap-2 min-w-0">
        {/* Elements */}
        <div className="flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 flex-shrink-0">
          <span className="text-xs text-gray-600 dark:text-gray-300 mr-1 md:mr-3 font-medium hidden sm:inline">
            Elements:
          </span>
          {elements.map(element => {
            const IconComponent = element.icon;
            return (
              <button
                key={element.type}
                onClick={() => onAddElement(element.type)}
                className="p-2 md:p-3 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors group relative text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                title={element.label}
              >
                <IconComponent size={16} className="md:w-[18px] md:h-[18px]" />
                <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap shadow-lg border border-gray-700 z-50">
                  {element.label}
                </span>
              </button>
            );
          })}
        </div>

        {/* Containers */}
        <div className="flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 flex-shrink-0">
          <span className="text-xs text-gray-600 dark:text-gray-300 mr-1 md:mr-3 font-medium hidden sm:inline">
            Layout:
          </span>
          {containers.map(container => (
            <button
              key={container.type}
              onClick={() => onAddElement(container.type)}
              className="px-2 md:px-4 py-1 md:py-2 text-xs bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white font-medium group relative whitespace-nowrap"
              title={container.label}
            >
              <span className="hidden sm:inline">{container.label}</span>
              <span className="sm:hidden">{container.label.charAt(0)}</span>
              <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap shadow-lg border border-gray-700 z-50">
                Thêm {container.label}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmailBuilderHeader;
