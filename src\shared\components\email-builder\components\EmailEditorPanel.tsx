import React, { useState } from 'react';
import { Input } from '@/shared/components/common';
import { EmailElement } from '../types';
import { Settings, Type, LayoutGrid } from 'lucide-react';
import RichTextEditor from './RichTextEditor';

interface EmailEditorPanelProps {
  selectedElement: EmailElement | null;
  updateSelectedElement: (property: string, value: unknown) => void;
  selectedIndex: number | null;
  emailElements: EmailElement[];
}

const EmailEditorPanel: React.FC<EmailEditorPanelProps> = ({
  selectedElement,
  updateSelectedElement,
}) => {
  const [activeTab, setActiveTab] = useState<'properties' | 'style' | 'layout'>('properties');

  if (!selectedElement) {
    return (
      <div className="p-6 text-center bg-white dark:bg-gray-800 h-full flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400">
          <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <Settings size={20} />
          </div>
          <p className="text-sm">Chọn một phần tử để chỉnh sửa</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto bg-white dark:bg-gray-800">
      {/* Header with element info */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 capitalize">
          {selectedElement.type}
        </h3>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Chỉnh sửa thuộc tính phần tử
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex">
          <button
            className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
              activeTab === 'properties'
                ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-b-2 border-blue-500'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            onClick={() => setActiveTab('properties')}
          >
            <Settings size={12} className="inline mr-1" />
            Thuộc tính
          </button>
          <button
            className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
              activeTab === 'style'
                ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-b-2 border-blue-500'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            onClick={() => setActiveTab('style')}
          >
            <Type size={12} className="inline mr-1" />
            Style
          </button>
          <button
            className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
              activeTab === 'layout'
                ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 border-b-2 border-blue-500'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            onClick={() => setActiveTab('layout')}
          >
            <LayoutGrid size={12} className="inline mr-1" />
            Layout
          </button>
        </div>
      </div>

      {activeTab === 'properties' && (
        <div className="p-4 space-y-4">
          {/* TEXT ELEMENT */}
          {selectedElement.type === 'text' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Nội dung văn bản
                </label>
                <RichTextEditor
                  value={selectedElement.content || ''}
                  onChange={value => updateSelectedElement('content', value)}
                  placeholder="Nhập nội dung văn bản"
                  className="w-full"
                />
              </div>
            </div>
          )}

          {/* HEADING ELEMENT */}
          {selectedElement.type === 'heading' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Nội dung tiêu đề
                </label>
                <RichTextEditor
                  value={selectedElement.content || ''}
                  onChange={value => updateSelectedElement('content', value)}
                  placeholder="Nhập tiêu đề"
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Loại tiêu đề
                </label>
                <select
                  value={selectedElement.headingType || 'h2'}
                  onChange={e => updateSelectedElement('headingType', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="h1">H1 - Tiêu đề chính</option>
                  <option value="h2">H2 - Tiêu đề phụ</option>
                  <option value="h3">H3 - Tiêu đề nhỏ</option>
                  <option value="h4">H4 - Tiêu đề rất nhỏ</option>
                </select>
              </div>
            </div>
          )}

          {/* BUTTON ELEMENT */}
          {selectedElement.type === 'button' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Văn bản nút
                </label>
                <Input
                  placeholder="Nhập văn bản nút"
                  value={selectedElement.text || ''}
                  onChange={e => updateSelectedElement('text', e.target.value)}
                  className="w-full text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Liên kết (URL)
                </label>
                <Input
                  placeholder="https://example.com"
                  value={selectedElement.url || ''}
                  onChange={e => updateSelectedElement('url', e.target.value)}
                  className="w-full text-sm"
                />
              </div>
            </div>
          )}

          {/* IMAGE ELEMENT */}
          {selectedElement.type === 'image' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  URL hình ảnh
                </label>
                <Input
                  placeholder="https://example.com/image.jpg"
                  value={selectedElement.src || ''}
                  onChange={e => updateSelectedElement('src', e.target.value)}
                  className="w-full text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Văn bản thay thế (Alt)
                </label>
                <Input
                  placeholder="Mô tả hình ảnh"
                  value={selectedElement.alt || ''}
                  onChange={e => updateSelectedElement('alt', e.target.value)}
                  className="w-full text-sm"
                />
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'style' && (
        <div className="p-4 space-y-4">
          {/* Font Size */}
          {(selectedElement.type === 'text' ||
            selectedElement.type === 'heading' ||
            selectedElement.type === 'button') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Kích thước chữ (px)
              </label>
              <Input
                type="number"
                placeholder="16"
                value={selectedElement.style?.fontSize || ''}
                onChange={e =>
                  updateSelectedElement(
                    'style.fontSize',
                    e.target.value ? parseInt(e.target.value) : ''
                  )
                }
                className="w-full text-sm"
              />
            </div>
          )}

          {/* Text Color */}
          {(selectedElement.type === 'text' ||
            selectedElement.type === 'heading' ||
            selectedElement.type === 'button') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Màu chữ
              </label>
              <div className="flex gap-2">
                <Input
                  type="color"
                  value={selectedElement.style?.color || '#000000'}
                  onChange={e => updateSelectedElement('style.color', e.target.value)}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  type="text"
                  placeholder="#000000"
                  value={selectedElement.style?.color || ''}
                  onChange={e => updateSelectedElement('style.color', e.target.value)}
                  className="flex-1 text-sm"
                />
              </div>
            </div>
          )}

          {/* Background Color for Button */}
          {selectedElement.type === 'button' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Màu nền
              </label>
              <div className="flex gap-2">
                <Input
                  type="color"
                  value={selectedElement.style?.backgroundColor || '#3b82f6'}
                  onChange={e => updateSelectedElement('style.backgroundColor', e.target.value)}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  type="text"
                  placeholder="#3b82f6"
                  value={selectedElement.style?.backgroundColor || ''}
                  onChange={e => updateSelectedElement('style.backgroundColor', e.target.value)}
                  className="flex-1 text-sm"
                />
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'layout' && (
        <div className="p-4 space-y-4">
          {/* Padding */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Padding (px)
            </label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Top"
                value={selectedElement.style?.paddingTop || ''}
                onChange={e =>
                  updateSelectedElement(
                    'style.paddingTop',
                    e.target.value ? parseInt(e.target.value) : ''
                  )
                }
                className="text-sm"
              />
              <Input
                type="number"
                placeholder="Right"
                value={selectedElement.style?.paddingRight || ''}
                onChange={e =>
                  updateSelectedElement(
                    'style.paddingRight',
                    e.target.value ? parseInt(e.target.value) : ''
                  )
                }
                className="text-sm"
              />
              <Input
                type="number"
                placeholder="Bottom"
                value={selectedElement.style?.paddingBottom || ''}
                onChange={e =>
                  updateSelectedElement(
                    'style.paddingBottom',
                    e.target.value ? parseInt(e.target.value) : ''
                  )
                }
                className="text-sm"
              />
              <Input
                type="number"
                placeholder="Left"
                value={selectedElement.style?.paddingLeft || ''}
                onChange={e =>
                  updateSelectedElement(
                    'style.paddingLeft',
                    e.target.value ? parseInt(e.target.value) : ''
                  )
                }
                className="text-sm"
              />
            </div>
          </div>

          {/* Border Radius */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bo góc (px)
            </label>
            <Input
              type="number"
              placeholder="4"
              value={selectedElement.style?.borderRadius || ''}
              onChange={e =>
                updateSelectedElement(
                  'style.borderRadius',
                  e.target.value ? parseInt(e.target.value) : ''
                )
              }
              className="w-full text-sm"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailEditorPanel;
