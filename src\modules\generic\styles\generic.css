/**
 * Generic Page Styles
 * Styles cho trang generic với ultra-minimal design
 */

/* Generic page container */
.generic-page {
  width: 100%;
  min-height: 400px;
  background: var(--background);
  color: var(--foreground);
}

/* Generic widget container - ultra minimal */
.generic-widget-container {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}

/* Override react-grid-layout styles for minimal look */
.generic-page .react-grid-layout {
  background: transparent;
}

.generic-page .react-grid-item {
  border: none;
  background: transparent;
  transition: none;
}

.generic-page .react-grid-item:hover {
  border: none;
  box-shadow: none;
}

.generic-page .react-grid-item.react-grid-placeholder {
  background: rgba(0, 0, 0, 0.1);
  border: 1px dashed rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Dark mode placeholder */
.dark .generic-page .react-grid-item.react-grid-placeholder {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Hide resize handles in view mode */
.generic-page .react-resizable-handle {
  display: none;
}

/* Connection status indicator */
.generic-connection-status {
  position: fixed;
  top: 8px;
  right: 8px;
  z-index: 1000;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: #ef4444;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.generic-connection-status.connected {
  background: #10b981;
}

/* Debug info */
.generic-debug-info {
  position: fixed;
  bottom: 8px;
  left: 8px;
  z-index: 1000;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-family: 'Courier New', monospace;
  color: white;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

/* Empty state */
.generic-empty-state {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--muted-foreground);
}

.generic-empty-state .icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.generic-empty-state h2 {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.generic-empty-state p {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.generic-empty-state .session-id {
  font-size: 0.75rem;
  font-family: 'Courier New', monospace;
  opacity: 0.7;
  margin-top: 0.5rem;
}

/* Loading state */
.generic-loading {
  width: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .generic-debug-info {
    font-size: 8px;
    padding: 2px 4px;
  }
  
  .generic-connection-status {
    font-size: 10px;
    padding: 2px 6px;
  }
  
  .generic-empty-state .icon {
    font-size: 3rem;
  }
  
  .generic-empty-state h2 {
    font-size: 1rem;
  }
  
  .generic-empty-state p {
    font-size: 0.75rem;
  }
}

/* Animation for smooth transitions */
.generic-widget-container {
  transition: opacity 0.2s ease-in-out;
}

.generic-widget-container.entering {
  opacity: 0;
}

.generic-widget-container.entered {
  opacity: 1;
}

.generic-widget-container.exiting {
  opacity: 0;
}

/* Ensure widgets fill container completely */
.generic-widget-container > * {
  width: 100% !important;
  height: 100% !important;
}
