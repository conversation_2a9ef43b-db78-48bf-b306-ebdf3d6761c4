import React, { useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Chip,
  Typography,
  ActionMenu,
  ConfirmDeleteModal,
  IconCard,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import {
  useSeedingGroups,
  useDeleteSeedingGroups,
  useStartSeedingGroup,
  usePauseSeedingGroup,
  useStopSeedingGroup,
  useCreateSeedingGroup,
} from '../../hooks/useSeedingGroups';
import SeedingGroupForm from '../../components/seedingGroup/SeedingGroupForm';
import {
  SeedingGroupDto,
  SeedingGroupStatus,
  SeedingGroupQueryDto,
  getSeedingGroupStatusLabel,
  CreateSeedingGroupDto,
} from '../../types/seedingGroup';

const SeedingGroupsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    ids: string[];
    names: string[];
  }>({
    isOpen: false,
    ids: [],
    names: [],
  });

  // Mutations
  const createSeedingGroup = useCreateSeedingGroup();
  const deleteSeedingGroups = useDeleteSeedingGroups();
  const startSeedingGroup = useStartSeedingGroup();
  const pauseSeedingGroup = usePauseSeedingGroup();
  const stopSeedingGroup = useStopSeedingGroup();

  // Event handlers
  const handleAdd = () => {
    setShowCreateForm(true);
  };

  const handleView = (id: string) => {
    // TODO: Implement view detail
    console.log('View seeding group:', id);
  };

  const handleEdit = (id: string) => {
    // TODO: Implement edit
    console.log('Edit seeding group:', id);
  };

  const handleStart = useCallback(
    async (id: string) => {
      try {
        await startSeedingGroup.mutateAsync(id);
      } catch {
        // Error handled by hook
      }
    },
    [startSeedingGroup]
  );

  const handlePause = useCallback(
    async (id: string) => {
      try {
        await pauseSeedingGroup.mutateAsync(id);
      } catch {
        // Error handled by hook
      }
    },
    [pauseSeedingGroup]
  );

  const handleStop = useCallback(
    async (id: string) => {
      try {
        await stopSeedingGroup.mutateAsync(id);
      } catch {
        // Error handled by hook
      }
    },
    [stopSeedingGroup]
  );

  const handleDelete = (ids: string[], names: string[]) => {
    setDeleteModal({
      isOpen: true,
      ids,
      names,
    });
  };

  // Table columns
  const columns = useMemo(
    () => [
      {
        title: t('marketing:seedingGroups.table.name', 'Tên'),
        dataIndex: 'name',
        key: 'name',
        sortable: true,
        render: (value: string, record: SeedingGroupDto) => (
          <div>
            <Typography variant="body1" className="font-medium">
              {value}
            </Typography>
            {record.description && (
              <Typography variant="body2" className="text-gray-500 mt-1">
                {record.description}
              </Typography>
            )}
          </div>
        ),
      },
      {
        title: t('marketing:seedingGroups.table.status', 'Trạng thái'),
        dataIndex: 'status',
        key: 'status',
        sortable: true,
        render: (status: SeedingGroupStatus) => <Chip>{getSeedingGroupStatusLabel(status)}</Chip>,
      },
      {
        title: t('marketing:seedingGroups.table.accounts', 'Tài khoản'),
        dataIndex: 'totalAccounts',
        key: 'totalAccounts',
        render: (totalAccounts: number, record: SeedingGroupDto) => (
          <div className="text-center">
            <Typography variant="body1" className="font-medium">
              {record.activeAccounts}/{totalAccounts}
            </Typography>
            <Typography variant="body2" className="text-gray-500">
              Hoạt động/Tổng
            </Typography>
          </div>
        ),
      },
      {
        title: t('marketing:seedingGroups.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        sortable: true,
        render: (value: string) => (
          <Typography variant="body2">
            {new Date(value).toLocaleDateString('vi-VN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Typography>
        ),
      },
      {
        title: t('common:actions', 'Hành động'),
        key: 'actions',
        width: 120,
        render: (_: any, record: SeedingGroupDto) => (
          <ActionMenu
            items={[
              {
                id: 'view',
                label: t('common:view', 'Xem'),
                icon: 'eye',
                onClick: () => handleView(record.id),
              },
              {
                id: 'edit',
                label: t('common:edit', 'Sửa'),
                icon: 'edit',
                onClick: () => handleEdit(record.id),
                disabled: record.status === SeedingGroupStatus.ACTIVE,
              },
              ...(record.status === SeedingGroupStatus.DRAFT ||
              record.status === SeedingGroupStatus.PAUSED
                ? [
                    {
                      id: 'start',
                      label: t('marketing:seedingGroups.actions.start', 'Bắt đầu'),
                      icon: 'play' as const,
                      onClick: () => handleStart(record.id),
                    },
                  ]
                : []),
              ...(record.status === SeedingGroupStatus.ACTIVE
                ? [
                    {
                      id: 'pause',
                      label: t('marketing:seedingGroups.actions.pause', 'Tạm dừng'),
                      icon: 'pause' as const,
                      onClick: () => handlePause(record.id),
                    },
                  ]
                : []),
              ...(record.status !== SeedingGroupStatus.STOPPED
                ? [
                    {
                      id: 'stop',
                      label: t('marketing:seedingGroups.actions.stop', 'Dừng'),
                      icon: 'stop-circle' as const,
                      onClick: () => handleStop(record.id),
                    },
                  ]
                : []),
              {
                id: 'delete',
                label: t('common:delete', 'Xóa'),
                icon: 'trash-2',
                onClick: () => handleDelete([record.id], [record.name]),
                disabled: record.status === SeedingGroupStatus.ACTIVE,
              },
            ]}
          />
        ),
      },
    ],
    [t, handlePause, handleStart, handleStop]
  );

  // Data table configuration
  const dataTable = useDataTable(
    useDataTableConfig({
      columns: columns as any,
      createQueryParams: (params: any) => {
        const queryParams: SeedingGroupQueryDto = {
          page: params.page,
          limit: params.pageSize,
        };

        if (params.searchTerm) {
          queryParams.search = params.searchTerm;
        }

        if (params.sortBy) {
          queryParams.sortBy = params.sortBy;
        }

        if (params.sortDirection) {
          queryParams.sortDirection = params.sortDirection === 'ASC' ? 'asc' : 'desc';
        }

        return queryParams;
      },
    })
  );

  // Data fetching
  const { data, isLoading, refetch } = useSeedingGroups(dataTable.queryParams as any);

  const handleConfirmDelete = async () => {
    try {
      await deleteSeedingGroups.mutateAsync(deleteModal.ids);
      setDeleteModal({ isOpen: false, ids: [], names: [] });
      refetch();
    } catch {
      // Error handled by hook
    }
  };

  const handleCreateSubmit = async (data: CreateSeedingGroupDto) => {
    try {
      await createSeedingGroup.mutateAsync(data);
      setShowCreateForm(false);
      refetch();
    } catch {
      // Error handled by hook
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar onSearch={() => {}} onAdd={handleAdd} />
      <Card>
        {/* Create Form */}
        <SlideInForm isVisible={showCreateForm}>
          <div>
            <div className="flex justify-between items-center mb-6">
              <Typography variant="h4">
                {t('marketing:seedingGroups.form.create.title', 'Cấu hình Seeding Group')}
              </Typography>
              <IconCard
                icon="x"
                variant="secondary"
                onClick={() => setShowCreateForm(false)}
                title="Đóng"
              />
            </div>
            <SeedingGroupForm
              onSubmit={handleCreateSubmit}
              onCancel={() => setShowCreateForm(false)}
            />
          </div>
        </SlideInForm>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, ids: [], names: [] })}
        onConfirm={handleConfirmDelete}
        title={t('marketing:seedingGroups.delete.title', 'Xác nhận xóa')}
        message={
          deleteModal.names.length === 1
            ? t(
                'marketing:seedingGroups.delete.single',
                'Bạn có chắc chắn muốn xóa Seeding Group "{{name}}"?',
                {
                  name: deleteModal.names[0],
                }
              )
            : t(
                'marketing:seedingGroups.delete.multiple',
                'Bạn có chắc chắn muốn xóa {{count}} Seeding Groups đã chọn?',
                {
                  count: deleteModal.names.length,
                }
              )
        }
        isSubmitting={deleteSeedingGroups.isPending}
      />
    </div>
  );
};

export default SeedingGroupsPage;
