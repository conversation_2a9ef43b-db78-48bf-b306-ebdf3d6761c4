import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getZaloPersonalCampaigns,
  getZaloPersonalCampaignDetail,
  createZaloPersonalCampaign,
  createCrawlFriendsCampaign,
  createCrawlGroupsCampaign,
  updateZaloPersonalCampaign,
  deleteZaloPersonalCampaigns,
  startZaloPersonalCampaign,
  pauseZaloPersonalCampaign,
  stopZaloPersonalCampaign,
} from '../../api/zalo/zaloPersonalCampaignApi';
import { ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS } from '../../constants/zaloPersonalCampaignQueryKeys';
import type {
  ZaloPersonalCampaignQueryDto,
  CreateZaloPersonalCampaignDto,
  UpdateZaloPersonalCampaignDto,
  CreateCrawlFriendsListCampaignDto,
  CreateCrawlGroupsCampaignDto,
} from '../../types/zaloPersonalCampaign';

/**
 * Hook để lấy danh sách chiến dịch Zalo Personal
 */
export const useZaloPersonalCampaigns = (params?: ZaloPersonalCampaignQueryDto) => {
  return useQuery({
    queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.LIST(params || {}),
    queryFn: () => getZaloPersonalCampaigns(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết chiến dịch Zalo Personal
 */
export const useZaloPersonalCampaign = (id: string) => {
  return useQuery({
    queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
    queryFn: () => getZaloPersonalCampaignDetail(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Note: Stats are now included in the main campaign object

/**
 * Hook để tạo chiến dịch Zalo Personal mới
 */
export const useCreateZaloPersonalCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateZaloPersonalCampaignDto) =>
      createZaloPersonalCampaign(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để tạo chiến dịch Crawl Friends List
 */
export const useCreateCrawlFriendsCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCrawlFriendsListCampaignDto) => createCrawlFriendsCampaign(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để tạo chiến dịch Crawl Groups
 */
export const useCreateCrawlGroupsCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCrawlGroupsCampaignDto) => createCrawlGroupsCampaign(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật chiến dịch Zalo Personal
 */
export const useUpdateZaloPersonalCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateZaloPersonalCampaignDto }) =>
      updateZaloPersonalCampaign(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để xóa chiến dịch Zalo Personal
 */
export const useDeleteZaloPersonalCampaigns = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => deleteZaloPersonalCampaigns(ids),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để bắt đầu chiến dịch
 */
export const useStartZaloPersonalCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => startZaloPersonalCampaign(id),
    onSuccess: (_, id) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để tạm dừng chiến dịch
 */
export const usePauseZaloPersonalCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => pauseZaloPersonalCampaign(id),
    onSuccess: (_, id) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để dừng chiến dịch
 */
export const useStopZaloPersonalCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => stopZaloPersonalCampaign(id),
    onSuccess: (_, id) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_CAMPAIGN_QUERY_KEYS.ALL,
      });
    },
  });
};
