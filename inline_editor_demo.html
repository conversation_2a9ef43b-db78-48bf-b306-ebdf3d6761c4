<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inline Editor Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #3b82f6;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .element-demo {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            min-height: 100px;
            background: #fafafa;
        }
        .element-demo.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .toolbar {
            position: absolute;
            top: -30px;
            left: 0;
            background: #3b82f6;
            color: white;
            padding: 5px 10px;
            border-radius: 4px 4px 0 0;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .toolbar button {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 2px;
            font-size: 10px;
        }
        .toolbar button:hover {
            background: rgba(255,255,255,0.2);
        }
        .inline-editor {
            position: absolute;
            inset: 0;
            background: white;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 50;
        }
        .editor-toolbar {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8f9fa;
            border-radius: 6px 6px 0 0;
        }
        .editor-toolbar button {
            padding: 4px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .editor-toolbar button:hover {
            background: #e5e7eb;
        }
        .editor-toolbar .divider {
            width: 1px;
            height: 16px;
            background: #d1d5db;
            margin: 0 4px;
        }
        .editor-content {
            padding: 12px;
            min-height: 80px;
            outline: none;
            overflow-y: auto;
            max-height: 200px;
        }
        .editor-footer {
            padding: 8px 12px;
            border-top: 1px solid #e5e7eb;
            background: #f8f9fa;
            font-size: 11px;
            color: #6b7280;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .editor-footer .buttons {
            display: flex;
            gap: 8px;
        }
        .editor-footer button {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
        }
        .save-btn {
            background: #10b981;
            color: white;
        }
        .save-btn:hover {
            background: #059669;
        }
        .cancel-btn {
            background: #6b7280;
            color: white;
        }
        .cancel-btn:hover {
            background: #4b5563;
        }
        .color-picker {
            position: absolute;
            top: 32px;
            left: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10;
        }
        .color-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 4px;
        }
        .color-btn {
            width: 24px;
            height: 24px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.1s;
        }
        .color-btn:hover {
            transform: scale(1.1);
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        .instructions ul {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h1>📝 Inline Editor Demo</h1>
            <p>Test the rich text editing functionality</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>🎯 Hướng dẫn sử dụng:</h3>
                <ul>
                    <li><strong>Double-click</strong> vào text để mở inline editor</li>
                    <li>Sử dụng toolbar để format text (Bold, Italic, Underline, etc.)</li>
                    <li><strong>Ctrl+Enter</strong> để lưu, <strong>Esc</strong> để hủy</li>
                    <li>Click nút "Edit" trong toolbar khi element được chọn</li>
                </ul>
            </div>

            <div class="element-demo" id="textElement">
                <div class="toolbar">
                    <span>Text</span>
                    <button onclick="startEditing('textElement')" title="Edit">✏️</button>
                    <button title="Move Up">↑</button>
                    <button title="Move Down">↓</button>
                    <button title="Delete">🗑️</button>
                </div>
                <div class="element-content" ondblclick="startEditing('textElement')">
                    Nhấp đôi để chỉnh sửa văn bản này. Bạn có thể thêm <strong>bold</strong>, <em>italic</em>, và nhiều định dạng khác.
                </div>
            </div>

            <div class="element-demo" id="headingElement">
                <div class="toolbar">
                    <span>Heading</span>
                    <button onclick="startEditing('headingElement')" title="Edit">✏️</button>
                    <button title="Move Up">↑</button>
                    <button title="Move Down">↓</button>
                    <button title="Delete">🗑️</button>
                </div>
                <div class="element-content" ondblclick="startEditing('headingElement')" style="font-size: 24px; font-weight: bold; text-align: center;">
                    Tiêu đề mẫu - Double click để chỉnh sửa
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentEditor = null;
        let showColorPicker = false;

        const colors = [
            '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff',
            '#ff0000', '#ff6600', '#ffcc00', '#33cc00', '#0066cc', '#6600cc',
            '#ff3366', '#ff9933', '#ffff33', '#66ff33', '#3366ff', '#9933ff'
        ];

        function startEditing(elementId) {
            if (currentEditor) return;
            
            const element = document.getElementById(elementId);
            const content = element.querySelector('.element-content');
            const currentContent = content.innerHTML;
            
            // Mark as selected
            element.classList.add('selected');
            
            // Create inline editor
            const editor = document.createElement('div');
            editor.className = 'inline-editor';
            editor.innerHTML = `
                <div class="editor-toolbar">
                    <button onclick="execCommand('bold')" title="Bold">𝐁</button>
                    <button onclick="execCommand('italic')" title="Italic">𝐼</button>
                    <button onclick="execCommand('underline')" title="Underline">𝐔</button>
                    <div class="divider"></div>
                    <button onclick="execCommand('justifyLeft')" title="Align Left">⬅️</button>
                    <button onclick="execCommand('justifyCenter')" title="Align Center">↔️</button>
                    <button onclick="execCommand('justifyRight')" title="Align Right">➡️</button>
                    <div class="divider"></div>
                    <button onclick="execCommand('insertUnorderedList')" title="Bullet List">• • •</button>
                    <div class="divider"></div>
                    <select onchange="execCommand('fontSize', this.value)">
                        <option value="1">8px</option>
                        <option value="2">10px</option>
                        <option value="3" selected>12px</option>
                        <option value="4">14px</option>
                        <option value="5">18px</option>
                        <option value="6">24px</option>
                        <option value="7">36px</option>
                    </select>
                    <div style="position: relative;">
                        <button onclick="toggleColorPicker()" title="Text Color">🎨</button>
                        <div id="colorPicker" class="color-picker" style="display: none;">
                            <div class="color-grid">
                                ${colors.map(color => `<div class="color-btn" style="background: ${color}" onclick="setColor('${color}')"></div>`).join('')}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="editor-content" contenteditable="true">${currentContent}</div>
                <div class="editor-footer">
                    <span>Ctrl+Enter để lưu, Esc để hủy</span>
                    <div class="buttons">
                        <button class="save-btn" onclick="saveEdit('${elementId}')">Lưu</button>
                        <button class="cancel-btn" onclick="cancelEdit('${elementId}')">Hủy</button>
                    </div>
                </div>
            `;
            
            element.appendChild(editor);
            currentEditor = elementId;
            
            // Focus editor
            const editorContent = editor.querySelector('.editor-content');
            editorContent.focus();
            
            // Set cursor to end
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(editorContent);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
            
            // Add keyboard shortcuts
            editorContent.addEventListener('keydown', handleKeyDown);
        }

        function execCommand(command, value = null) {
            document.execCommand(command, false, value);
        }

        function toggleColorPicker() {
            const picker = document.getElementById('colorPicker');
            showColorPicker = !showColorPicker;
            picker.style.display = showColorPicker ? 'block' : 'none';
        }

        function setColor(color) {
            execCommand('foreColor', color);
            toggleColorPicker();
        }

        function handleKeyDown(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                saveEdit(currentEditor);
            }
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit(currentEditor);
            }
        }

        function saveEdit(elementId) {
            const element = document.getElementById(elementId);
            const editor = element.querySelector('.inline-editor');
            const editorContent = editor.querySelector('.editor-content');
            const content = element.querySelector('.element-content');
            
            content.innerHTML = editorContent.innerHTML;
            
            element.removeChild(editor);
            element.classList.remove('selected');
            currentEditor = null;
            showColorPicker = false;
        }

        function cancelEdit(elementId) {
            const element = document.getElementById(elementId);
            const editor = element.querySelector('.inline-editor');
            
            element.removeChild(editor);
            element.classList.remove('selected');
            currentEditor = null;
            showColorPicker = false;
        }

        // Close color picker when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#colorPicker') && !e.target.closest('button[onclick="toggleColorPicker()"]')) {
                const picker = document.getElementById('colorPicker');
                if (picker) {
                    picker.style.display = 'none';
                    showColorPicker = false;
                }
            }
        });
    </script>
</body>
</html>
