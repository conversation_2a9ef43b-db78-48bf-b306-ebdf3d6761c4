/**
 * Tag hooks using TanStack Query
 */

import { useMutation, useQuery, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { TagService } from '../services/tag.service';
import {
  TagQueryParams,
  CreateTagRequest,
  UpdateTagRequest,
  AssignTagsToAudiencesRequest,
  Tag,
} from '../types/tag.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Query keys for tags
 */
export const TAG_QUERY_KEYS = {
  ALL: ['tags'] as const,
  all: ['marketing', 'tags'] as const, // Backward compatibility
  LIST: (params: TagQueryParams) => [...TAG_QUERY_KEYS.ALL, 'list', params] as const,
  list: (params?: TagQueryParams) => [...TAG_QUERY_KEYS.all, 'list', params] as const, // Backward compatibility
  DETAIL: (id: number) => [...TAG_QUERY_KEYS.ALL, 'detail', id] as const,
  detail: (id: number) => [...TAG_QUERY_KEYS.all, 'detail', id] as const, // Backward compatibility
};

/**
 * Hook to get tags list
 */
export const useTags = (params?: TagQueryParams) => {
  return useQuery({
    queryKey: TAG_QUERY_KEYS.LIST(params || {}),
    queryFn: () => TagService.getTags(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to get tag by ID
 */
export const useTag = (
  id: number,
  options?: Omit<UseQueryOptions<ApiResponseDto<Tag>, Error, Tag>, 'queryKey' | 'queryFn' | 'select'>
) => {
  return useQuery({
    queryKey: TAG_QUERY_KEYS.DETAIL(id),
    queryFn: () => TagService.getTagById(id),
    select: data => data.result,
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook to create tag
 */
export const useCreateTag = () => {
  const queryClient = useQueryClient();
  const smartNotification = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreateTagRequest) => TagService.createTag(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.ALL });
      smartNotification.success({
        message: 'Tạo tag thành công'
      });
    },
    onError: (error: any) => {
      smartNotification.error({
        message: error?.message || 'Có lỗi xảy ra khi tạo tag'
      });
    },
  });
};

/**
 * Hook to update tag with dynamic ID
 */
export const useUpdateTag = () => {
  const queryClient = useQueryClient();
  const smartNotification = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTagRequest }) =>
      TagService.updateTag(id, data),
    onSuccess: () => {
      // Chỉ invalidate danh sách tags, không invalidate detail để tránh gọi API không cần thiết
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.ALL });
      smartNotification.success({
        message: 'Cập nhật tag thành công'
      });
    },
    onError: (error: any) => {
      smartNotification.error({
        message: error?.message || 'Có lỗi xảy ra khi cập nhật tag'
      });
    },
  });
};

/**
 * Hook to update tag with static ID (backward compatibility)
 */
export const useUpdateTagDynamic = () => {
  return useUpdateTag();
};

/**
 * Hook to delete tag
 */
export const useDeleteTag = () => {
  const queryClient = useQueryClient();
  const smartNotification = useSmartNotification();

  return useMutation({
    mutationFn: (id: number) => TagService.deleteTag(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.ALL });
      smartNotification.success({
        message: 'Xóa tag thành công'
      });
    },
    onError: (error: any) => {
      smartNotification.error({
        message: error?.message || 'Có lỗi xảy ra khi xóa tag'
      });
    },
  });
};

/**
 * Hook to delete multiple tags
 */
export const useDeleteMultipleTags = () => {
  const queryClient = useQueryClient();
  const smartNotification = useSmartNotification();

  return useMutation({
    mutationFn: (ids: number[]) => TagService.deleteMultipleTags(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.ALL });
      smartNotification.success({
        message: 'Xóa tags thành công'
      });
    },
    onError: (error: any) => {
      smartNotification.error({
        message: error?.message || 'Có lỗi xảy ra khi xóa tags'
      });
    },
  });
};

/**
 * Hook to assign tags to audiences
 */
export const useAssignTagsToAudiences = () => {
  const queryClient = useQueryClient();
  const smartNotification = useSmartNotification();

  return useMutation({
    mutationFn: (data: AssignTagsToAudiencesRequest) => TagService.assignTagsToAudiences(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.ALL });
      // Also invalidate audience queries if they exist
      queryClient.invalidateQueries({ queryKey: ['audiences'] });
      smartNotification.success({
        message: `Đã gắn tag cho ${response.result.assignedCount} đối tượng thành công`
      });
    },
    onError: (error: any) => {
      smartNotification.error({
        message: error?.message || 'Có lỗi xảy ra khi gắn tag'
      });
    },
  });
};
