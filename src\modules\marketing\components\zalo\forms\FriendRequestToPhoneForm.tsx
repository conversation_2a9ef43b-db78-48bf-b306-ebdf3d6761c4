import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Form,
  FormItem,
  Input,
  CollapsibleCard,
  Button,
  RadioGroup,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useFormErrors } from '@/shared/hooks';
import ScheduleSettings, { ScheduleType } from '../shared/ScheduleSettings';

interface FriendRequestToPhoneFormData {
  // Cài đặt chung
  accountId: string;
  campaignName: string;
  schedule: ScheduleType;

  // Cài đặt giới hạn hành động
  delayBetweenSends: number; // giây
  dailyLimit: number;
  totalLimit: number;
  limitInMinutes: number;

  // Loại hành động
  actionType: 'sendMessage' | 'addFriend' | 'both';

  // Cài đặt khác
  skipDaysForPreviousContacts: number;

  // Nội dung tin nhắn
  messageContent: string;
}

interface FriendRequestToPhoneFormProps {
  onSubmit?: (data: FriendRequestToPhoneFormData) => void;
  onCancel?: () => void;
}

const FriendRequestToPhoneForm: React.FC<FriendRequestToPhoneFormProps> = ({
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['marketing']);
  const { formRef, setFormErrors } = useFormErrors();

  const [formData, setFormData] = useState<FriendRequestToPhoneFormData>({
    accountId: '',
    campaignName: '',
    schedule: {
      type: 'daily',
      startDate: null,
      endDate: null,
      pauseWhenNoData: true,
      refreshDataDaily: false,
    },
    delayBetweenSends: 30,
    dailyLimit: 100,
    totalLimit: 1000,
    limitInMinutes: 60,
    actionType: 'both',
    skipDaysForPreviousContacts: 7,
    messageContent: '',
  });

  const handleInputChange = (field: keyof FriendRequestToPhoneFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = () => {
    // Basic validation
    const errors: Record<string, string> = {};
    if (!formData.accountId) {
      errors.accountId = 'Vui lòng chọn tài khoản Zalo';
    }
    if (!formData.campaignName.trim()) {
      errors.campaignName = 'Vui lòng nhập tên chiến dịch';
    }
    // Chỉ validate nội dung tin nhắn nếu có gửi tin nhắn
    if ((formData.actionType === 'sendMessage' || formData.actionType === 'both') && !formData.messageContent.trim()) {
      errors.messageContent = 'Vui lòng nhập nội dung tin nhắn';
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    onSubmit?.(formData);
  };

  // API function to load Zalo accounts
  const loadZaloAccounts = async (params: { search?: string; page?: number; limit?: number }) => {
    // Mock API call - replace with actual API
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockAccounts = [
      { value: '1', label: 'Tài khoản Zalo 1', description: 'Tài khoản chính' },
      { value: '2', label: 'Tài khoản Zalo 2', description: 'Tài khoản phụ' },
      { value: '3', label: 'Tài khoản Zalo 3', description: 'Tài khoản backup' },
    ];

    const filteredAccounts = params.search
      ? mockAccounts.filter(acc => acc.label.toLowerCase().includes(params.search!.toLowerCase()))
      : mockAccounts;

    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    return {
      items: filteredAccounts.slice(startIndex, endIndex),
      totalItems: filteredAccounts.length,
      totalPages: Math.ceil(filteredAccounts.length / limit),
      currentPage: page,
      hasMore: endIndex < filteredAccounts.length,
    };
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
        {/* Cài đặt chung */}
        <CollapsibleCard
          title={t('marketing:friendRequestToPhone.generalSettings', 'Cài đặt chung')}
        >
          <div className="space-y-4">
            <FormItem label={t('marketing:friendRequestToPhone.account', 'Tài khoản')} name="accountId" required>
              <AsyncSelectWithPagination
                value={formData.accountId}
                onChange={(value) => handleInputChange('accountId', value)}
                loadOptions={loadZaloAccounts}
                placeholder={t('marketing:friendRequestToPhone.selectAccount', 'Chọn tài khoản Zalo')}
              />
            </FormItem>

            <FormItem label={t('marketing:friendRequestToPhone.campaignName', 'Tên chiến dịch')} name="campaignName" required>
              <Input
                value={formData.campaignName}
                onChange={(e) => handleInputChange('campaignName', e.target.value)}
                placeholder={t('marketing:friendRequestToPhone.campaignNamePlaceholder', 'Nhập tên chiến dịch')}
                fullWidth
              />
            </FormItem>

            <ScheduleSettings
              value={formData.schedule}
              onChange={(value) => handleInputChange('schedule', value)}
            />
          </div>
        </CollapsibleCard>

        {/* Cài đặt giới hạn hành động */}
        <CollapsibleCard
          title={t('marketing:friendRequestToPhone.actionLimits', 'Cài đặt giới hạn hành động')}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('marketing:friendRequestToPhone.delayBetweenSends', 'Thời gian nghỉ giữa 2 lần gửi (giây)')} name="delayBetweenSends">
              <Input
                type="number"
                value={formData.delayBetweenSends}
                onChange={(e) => handleInputChange('delayBetweenSends', parseInt(e.target.value) || 0)}
                min={1}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('marketing:friendRequestToPhone.dailyLimit', 'Giới hạn trong ngày')} name="dailyLimit">
              <Input
                type="number"
                value={formData.dailyLimit}
                onChange={(e) => handleInputChange('dailyLimit', parseInt(e.target.value) || 0)}
                min={1}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('marketing:friendRequestToPhone.totalLimit', 'Giới hạn số tin gửi')} name="totalLimit">
              <Input
                type="number"
                value={formData.totalLimit}
                onChange={(e) => handleInputChange('totalLimit', parseInt(e.target.value) || 0)}
                min={1}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('marketing:friendRequestToPhone.limitInMinutes', 'Trong số phút')} name="limitInMinutes">
              <Input
                type="number"
                value={formData.limitInMinutes}
                onChange={(e) => handleInputChange('limitInMinutes', parseInt(e.target.value) || 0)}
                min={1}
                fullWidth
              />
            </FormItem>
          </div>

          <Typography variant="body2" className="text-muted-foreground mt-2">
            {t('marketing:friendRequestToPhone.todaySent', 'Hôm nay đã gửi được')} <strong>0</strong> {t('marketing:friendRequestToPhone.messages', 'tin')}
          </Typography>
        </CollapsibleCard>

        {/* Loại hành động */}
        <CollapsibleCard
          title={t('marketing:friendRequestToPhone.actionType', 'Loại hành động')}
        >
          <FormItem label={t('marketing:friendRequestToPhone.selectActionType', 'Chọn hành động thực hiện')} name="actionType">
            <RadioGroup
              options={[
                { label: t('marketing:friendRequestToPhone.sendMessageOnly', 'Chỉ gửi tin nhắn'), value: 'sendMessage' },
                { label: t('marketing:friendRequestToPhone.addFriendOnly', 'Chỉ kết bạn'), value: 'addFriend' },
                { label: t('marketing:friendRequestToPhone.both', 'Cả hai (Gửi tin nhắn và kết bạn)'), value: 'both' },
              ]}
              value={formData.actionType}
              onChange={(value) => handleInputChange('actionType', value)}
              direction="vertical"
            />
          </FormItem>
        </CollapsibleCard>


        {/* Cài đặt khác */}
        <CollapsibleCard
          title={t('marketing:friendRequestToPhone.otherSettings', 'Cài đặt khác')}
        >
          <FormItem label={t('marketing:friendRequestToPhone.skipDays', 'Không gửi tin nhắn cho khách hàng đã từng gửi tin cách đó số ngày')} name="skipDaysForPreviousContacts">
            <Input
              type="number"
              value={formData.skipDaysForPreviousContacts}
              onChange={(e) => handleInputChange('skipDaysForPreviousContacts', parseInt(e.target.value) || 0)}
              min={0}
              fullWidth
            />
          </FormItem>
        </CollapsibleCard>



        {/* Nội dung tin nhắn - chỉ hiển thị khi cần gửi tin nhắn */}
        {(formData.actionType === 'sendMessage' || formData.actionType === 'both') && (
          <CollapsibleCard
            title={t('marketing:friendRequestToPhone.messageContent', 'Nội dung tin nhắn')}
          >
            <FormItem label={t('marketing:friendRequestToPhone.message', 'Tin nhắn')} name="messageContent" required>
              <textarea
                className="w-full p-3 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary bg-background text-foreground"
                rows={4}
                value={formData.messageContent}
                onChange={(e) => handleInputChange('messageContent', e.target.value)}
                placeholder={t('marketing:friendRequestToPhone.messagePlaceholder', 'Nhập nội dung tin nhắn...')}
              />
            </FormItem>
          </CollapsibleCard>
        )}

        {/* Chi tiết chiến dịch */}
        <CollapsibleCard
          title={t('marketing:friendRequestToPhone.campaignDetails', 'Chi tiết chiến dịch')}
        >
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:friendRequestToPhone.targetList', 'Danh sách các đối tượng sẽ gửi sẽ được hiển thị ở đây sau khi cấu hình xong.')}
          </Typography>
        </CollapsibleCard>

        {/* Action buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" variant="primary">
            {t('marketing:friendRequestToPhone.create', 'Tạo chiến dịch')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default FriendRequestToPhoneForm;
