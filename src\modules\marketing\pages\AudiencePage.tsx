import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Chip } from '@/shared/components/common';
import AsyncMultiSelectMenu, { AsyncMultiSelectOption, SelectedOption } from '@/shared/components/common/AsyncMultiSelectMenu/AsyncMultiSelectMenu';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  AudienceStatus,
  AudienceQueryParams,
  AudienceType,
  AudienceAttribute,
  ContactData,
  CreateAudienceRequest,
} from '../types/audience.types';
import AudienceForm from '../components/forms/AudienceForm';
import AudienceDetailView from '../components/forms/AudienceDetailView';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useAudiences, useCreateAudience, useDeleteMultipleAudiences, useAssignTagsToAudiences } from '../hooks';
import { TagService } from '../services/tag.service';
import { Tag } from '../types/tag.types';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import { formatTimestamp } from '@/shared/utils/date';
import { scrollToTop, scrollToSelector } from '@/shared/utils/scroll';

/**
 * Interface cho dữ liệu audience từ API
 */
interface AudienceData {
  id: string;
  name: string;
  email: string;
  channel: string; // Nền tảng
  description: string;
  type: AudienceType;
  status: AudienceStatus;
  totalContacts: number;
  attributes: AudienceAttribute[];
  tags: Array<{ id: string; name: string; color?: string; }>;
  createdAt: string;
  updatedAt: string;
}



/**
 * Trang quản lý đối tượng sử dụng các hooks tối ưu
 */
const AudiencePage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho tag selection
  const [showTagMenu, setShowTagMenu] = useState(false);
  const [selectedTags, setSelectedTags] = useState<SelectedOption[]>([]);
  const [tagIconElement, setTagIconElement] = useState<HTMLElement | null>(null);

  // Effect để lấy ref của tag icon từ MenuIconBar
  React.useEffect(() => {
    // Tìm element có data-icon-id 'tag-selector' trong DOM
    const tagIcon = document.querySelector('[data-icon-id="tag-selector"]') as HTMLElement;
    if (tagIcon) {
      setTagIconElement(tagIcon);
    } else {
      // Fallback: tìm theo id nếu không tìm thấy bằng data-icon-id
      const tagIconById = document.getElementById('tag-selector') as HTMLElement;
      if (tagIconById) {
        setTagIconElement(tagIconById);
      }
    }
  }, [selectedRowKeys.length, showTagMenu]); // Re-run khi selectedRowKeys thay đổi vì icon chỉ hiện khi có selection

  // Tạo ref object từ element
  const tagIconRef = React.useMemo(() => ({
    current: tagIconElement
  }), [tagIconElement]);

  // State cho detail view
  const [selectedAudienceId, setSelectedAudienceId] = useState<number | null>(null);
  const [showDetailView, setShowDetailView] = useState(false);

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: AudienceStatus.ACTIVE },
      { id: 'draft', label: t('common:draft'), icon: 'file', value: AudienceStatus.DRAFT },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: AudienceStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<AudienceData>[]>(
    () => [
      { key: 'id', title: t('common:id', 'ID'), dataIndex: 'id', width: '8%', sortable: true },
      {
        key: 'name',
        title: t('marketing:audience.name', 'Tên'),
        dataIndex: 'name',
        width: '18%',
        sortable: true,
      },
      {
        key: 'email',
        title: t('marketing:audience.email', 'Email'),
        dataIndex: 'email',
        width: '18%',
        sortable: true,
      },
      {
        key: 'channel',
        title: t('marketing:audience.channel', 'Nền tảng'),
        dataIndex: 'channel',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const channel = value as string;

          // Chuyển đổi tên channel để hiển thị
          const getChannelDisplayName = (resource: string) => {
            switch (resource?.toLowerCase()) {
              case 'zalo':
                return t('marketing:audience.platform.zalo', 'Zalo');
              case 'zalo_personal':
                return t('marketing:audience.platform.zaloPersonal', 'Zalo Cá nhân');
              case 'facebook':
                return t('marketing:audience.platform.facebook', 'Facebook');
              case 'email':
                return t('marketing:audience.platform.email', 'Email');
              case 'phone':
                return t('marketing:audience.platform.phone', 'Phone');
              case 'web':
                return t('marketing:audience.platform.web', 'Web');
              default:
                return resource || t('common:noData', '-');
            }
          };

          return channel ? (
            <span className="text-sm text-gray-900 dark:text-gray-100">
              {getChannelDisplayName(channel)}
            </span>
          ) : (
            <span className="text-gray-400">{t('common:noData', '-')}</span>
          );
        },
      },
      {
        key: 'tags',
        title: t('marketing:audience.tags', 'Tags'),
        dataIndex: 'tags',
        width: '20%',
        render: (value: unknown) => {
          const tags = value as Array<{ id: string; name: string; color?: string; }>;

          if (!tags || tags.length === 0) {
            return (
              <span className="text-xs text-muted-foreground">
                {t('common:noData', 'Không có')}
              </span>
            );
          }

          // Hiển thị tối đa 2 tags đầu tiên
          const visibleTags = tags.slice(0, 2);
          const remainingCount = tags.length - 2;

          return (
            <div className="flex flex-wrap gap-1">
              {visibleTags.map((tag) => (
                <Chip
                  key={tag.id}
                  size="sm"
                  variant="default"
                  className="text-xs"
                  style={{
                    backgroundColor: tag.color || '#e5e7eb',
                    color: tag.color ? '#ffffff' : '#374151',
                    border: `1px solid ${tag.color || '#d1d5db'}`,
                  }}
                >
                  {tag.name}
                </Chip>
              ))}
              {remainingCount > 0 && (
                <Chip size="sm" variant="secondary" className="text-xs">
                  +{remainingCount}
                </Chip>
              )}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '17%',
        sortable: true,
        render: (value: unknown) => {
          return formatTimestamp(value);
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '12%',
        render: (_: unknown, record: AudienceData) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => handleViewDetail(parseInt(record.id, 10)),
            }
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AudienceQueryParams => {
    const queryParams: AudienceQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AudienceStatus;
    }
    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AudienceData, AudienceQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Sử dụng hooks từ API với query params từ dataTable
  const { data: apiAudienceData, isLoading, refetch } = useAudiences(dataTable.queryParams);

  // Chuyển đổi từ API response sang AudienceData
  const audienceData = useMemo(() => {
    // Lấy dữ liệu từ API response
    const rawData = apiAudienceData?.items || [];

    // Debug: Log dữ liệu từ API để kiểm tra
    console.log('Raw API Data:', rawData);
    if (rawData.length > 0) {
      console.log('First item importResource:', rawData[0].importResource);
    }

    if (!rawData || rawData.length === 0) {
      return { items: [], meta: { currentPage: 1, totalItems: 0 } };
    }
    // Chuyển đổi mỗi item từ API response sang AudienceData
    // API trả về dữ liệu contact (email, phone, customFields, tags) thay vì audience data
    const items = rawData.map((contact: ContactData, index: number): AudienceData => {
      const mappedItem = {
        id: contact.id?.toString() || index.toString(),
        name: contact.name || `Contact ${contact.id || index + 1}`, // Lấy name từ API
        email: contact.email || '', // Lấy email từ API
        channel: contact.importResource || '', // Lấy importResource từ API
        description: contact.phone || contact.phoneNumber || '', // Sử dụng phone làm mô tả tạm thời
        type: AudienceType.CUSTOMER, // Mặc định là customer
        status: AudienceStatus.ACTIVE, // Mặc định là active
        totalContacts: 1,
        attributes: contact.customFields?.map((field, fieldIndex: number): AudienceAttribute => ({
          id: fieldIndex.toString(),
          name: field.name || `Field ${fieldIndex}`,
          value: field.value || '',
        })) || [],
        tags: contact.tags || [], // Lấy tags từ API
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
      };

      // Debug: Log mapped item để kiểm tra
      if (index === 0) {
        console.log('Mapped first item:', mappedItem);
        console.log('Channel value:', mappedItem.channel);
      }

      return mappedItem;
    });
    return {
      items,
      meta: apiAudienceData?.meta || { currentPage: 1, totalItems: items.length },
    };
  }, [apiAudienceData]);
  const createAudienceMutation = useCreateAudience();
  const { mutateAsync: deleteMultipleAudiences } = useDeleteMultipleAudiences();
  const { mutateAsync: assignTagsToAudiences } = useAssignTagsToAudiences();
  const smartNotification = useSmartNotification();

  // Load tags function cho AsyncMultiSelectMenu
  const loadTags = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await TagService.getTags({
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
      });

      const items: AsyncMultiSelectOption[] = (response.result?.items || []).map((tag: Tag) => ({
        id: tag.id,
        label: tag.name,
        value: tag.id,
        icon: 'tag',
      }));

      return {
        items,
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || 1,
      };
    } catch (error) {
      console.error('Error loading tags:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  }, []);

  // Handler cho tag menu
  const handleTagMenuToggle = useCallback(() => {
    setShowTagMenu(!showTagMenu);
  }, [showTagMenu]);

  const handleTagSelectionChange = useCallback((tags: SelectedOption[]) => {
    setSelectedTags(tags);
  }, []);

  const handleTagMenuClose = useCallback(() => {
    setShowTagMenu(false);
  }, []);

  // Handler cho việc gắn tag cho audiences
  const handleAssignTags = useCallback(async () => {
    if (selectedRowKeys.length === 0 || selectedTags.length === 0) {
      smartNotification.warning({
        message: t('marketing:audience.selectAudienceAndTag', 'Vui lòng chọn audience và tag')
      });
      return;
    }

    try {
      await assignTagsToAudiences({
        audienceIds: selectedRowKeys.map(key => Number(key)),
        tagIds: selectedTags.map(t => t.id as number),
      });

      smartNotification.success({
        message: t('marketing:audience.assignTagSuccess', 'Đã gắn tag cho audience thành công')
      });

      // Reset selections
      setSelectedRowKeys([]);
      setSelectedTags([]);
      setShowTagMenu(false);
    } catch (error) {
      smartNotification.error({
        message: t('marketing:audience.assignTagError', 'Lỗi khi gắn tag cho audience')
      });
    }
  }, [selectedRowKeys, selectedTags, assignTagsToAudiences, smartNotification, t]);

  // Handler cho reload data
  const handleReload = useCallback(async () => {
    try {
      await refetch();
      smartNotification.success({ message: t('common:reloadSuccess', 'Tải lại dữ liệu thành công') });
    } catch (error) {
      smartNotification.error({ message: t('common:reloadError', 'Lỗi khi tải lại dữ liệu') });
    }
  }, [refetch, smartNotification, t]);

  // State để lưu form errors
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();



  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('marketing:audience.selectToDelete', 'Vui lòng chọn audience để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Chuyển đổi selectedRowKeys thành number[]
      const ids = selectedRowKeys.map(key => {
        if (typeof key === 'string') {
          return parseInt(key, 10);
        }
        return key as number;
      }).filter(id => !isNaN(id));

      // Gọi API xóa nhiều audiences cùng lúc
      await deleteMultipleAudiences(ids);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:audience.bulkDeleteSuccess', 'Xóa {{count}} audience thành công', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('marketing:audience.bulkDeleteError', 'Xóa nhiều audience thất bại'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleAudiences, t]);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [AudienceStatus.ACTIVE]: t('common:active'),
      [AudienceStatus.DRAFT]: t('common:draft'),
      [AudienceStatus.INACTIVE]: t('common:inactive'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    setFormErrors({}); // Clear form errors when opening form
    showAddForm();
  };

  // Xử lý xem chi tiết
  const handleViewDetail = (audienceId: number) => {
    setSelectedAudienceId(audienceId);
    setShowDetailView(true);

    // Scroll đến form sau khi animation hoàn thành (350ms)
    setTimeout(() => {
      const formElement = document.querySelector('.audience-detail-form');
      console.log('🔍 Form element found:', formElement);
      console.log('🔍 Current scroll position:', window.pageYOffset);

      if (formElement) {
        // Sử dụng scrollToSelector từ scroll.ts để tránh border đỏ
        scrollToSelector('.audience-detail-form', {
          behavior: 'smooth',
          offset: 0,
        });
        console.log('✅ Scrolled to detail form using scrollToSelector for audience:', audienceId);
      } else {
        // Fallback: scroll lên đầu trang
        scrollToTop({
          behavior: 'smooth',
          offset: 0,
        });
        console.log('✅ Fallback: Scrolled to top for audience detail view:', audienceId);
      }

      setTimeout(() => {
        console.log('🔍 New scroll position:', window.pageYOffset);
      }, 500);
    }, 350);
  };

  // Xử lý đóng detail view
  const handleCloseDetailView = () => {
    setShowDetailView(false);
    setSelectedAudienceId(null);
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Reset form errors trước khi submit
    setFormErrors({});

    // Chuyển đổi values thành CreateAudienceRequest với countryCode, phoneNumber và address
    const audienceData: CreateAudienceRequest = {
      name: values['name'] as string,
      ...(values['email'] ? { email: values['email'] as string } : {}),
      ...(values['phoneNumber'] ? { phoneNumber: values['phoneNumber'] as string } : {}),
      ...(values['countryCode'] ? { countryCode: values['countryCode'] as number } : {}),
      ...(values['address'] ? { address: values['address'] as string } : {}), // Thêm address
    };

    if (values['tagIds']) {
      audienceData.tagIds = values['tagIds'] as number[];
    }

    if (values['attributes']) {
      audienceData.attributes = values['attributes'] as Omit<AudienceAttribute, 'id'>[];
    }

    createAudienceMutation.mutate(audienceData, {
      onSuccess: () => {
        smartNotification.success({
          title: t('marketing:audience.createSuccess', 'Tạo đối tượng thành công'),
          message: t('marketing:audience.createSuccessMessage', 'Đối tượng đã được tạo thành công'),
          duration: 3000,
        });
        hideAddForm();
        setFormErrors({}); // Clear form errors on success
      },
      onError: (error: any) => {
        // Xử lý lỗi API với code 13014 (email đã tồn tại)
        if (error?.response?.data?.code === 13014) {
          // Hiển thị lỗi vào trường email
          const errorMessage = error.response.data.message || t('marketing:audience.emailExists', 'Email đã được sử dụng bởi khách hàng khác');
          setFormErrors({
            email: String(errorMessage)
          });

          // Hiển thị thông báo bằng useSmartNotification
          smartNotification.error({
            title: t('marketing:audience.createError', 'Tạo đối tượng thất bại'),
            message: String(errorMessage),
            duration: 5000,
          });
        } else {
          // Xử lý các lỗi khác
          const errorMessage = error?.response?.data?.message || t('marketing:audience.createErrorMessage', 'Có lỗi xảy ra khi tạo đối tượng');
          smartNotification.error({
            title: t('marketing:audience.createError', 'Tạo đối tượng thất bại'),
            message: String(errorMessage),
            duration: 3000,
          });
        }
      },
    });
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setFormErrors({}); // Clear form errors when canceling
    hideAddForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'refresh-cw',
            tooltip: t('common:reload', 'Tải lại dữ liệu'),
            variant: 'secondary',
            onClick: handleReload,
            className: 'text-blue-500',
          },
          {
            icon: 'tag',
            tooltip: t('marketing:audience.assignTag', 'Gắn tag'),
            variant: 'primary',
            onClick: handleTagMenuToggle,
            className: 'text-green-500',
            condition: selectedRowKeys.length > 0,
            id: 'tag-selector',
          },
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AudienceForm onSubmit={handleSubmit} onCancel={handleCancel} formErrors={formErrors} />
      </SlideInForm>

      {/* Detail View */}
      <SlideInForm isVisible={showDetailView}>
        {selectedAudienceId && (
          <div className="audience-detail-form">
            <AudienceDetailView
              audienceId={selectedAudienceId}
              onClose={handleCloseDetailView}
            />
          </div>
        )}
      </SlideInForm>

      <Card className="overflow-hidden mt-4">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={audienceData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: audienceData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: audienceData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:audience.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} audience đã chọn?', { count: selectedRowKeys.length })}
      />

      {/* Tag Selection Menu */}
      <AsyncMultiSelectMenu
        isOpen={showTagMenu}
        onClose={handleTagMenuClose}
        selectedOptions={selectedTags}
        onSelectionChange={handleTagSelectionChange}
        loadOptions={loadTags}
        triggerRef={tagIconRef}
        title={t('marketing:tag.selectTags', 'Chọn tag')}
        searchPlaceholder={t('marketing:tag.searchPlaceholder', 'Tìm kiếm tag...')}
        itemsPerPage={20}
        noOptionsMessage={t('marketing:tag.noTags', 'Không có tag nào')}
        loadingMessage={t('common:loading', 'Đang tải...')}
        actionButtons={[
          {
            label: t('common:cancel', 'Hủy'),
            variant: 'outline',
            onClick: handleTagMenuClose,
          },
          {
            label: `${t('marketing:audience.assignTag', 'Gắn tag')} (${selectedRowKeys.length})`,
            variant: 'primary',
            onClick: handleAssignTags,
            disabled: selectedTags.length === 0,
          },
        ]}
      />
    </div>
  );
};

export default AudiencePage;
