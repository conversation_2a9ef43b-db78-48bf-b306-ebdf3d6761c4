import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Eye, FileText, Mail, Gift, UserPlus, ShoppingCart, MessageCircle } from 'lucide-react';
import { Card, Button, Typography, ResponsiveGrid, Modal, Alert } from '@/shared/components/common';
import { EmailTemplateType } from '../../types/email.types';

// Predefined template data
interface PredefinedTemplate {
  id: string;
  name: string;
  description: string;
  type: EmailTemplateType;
  icon: React.ComponentType<any>;
  color: string;
  subject: string;
  htmlContent: string;
  variables: Array<{
    name: string;
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue: string;
    required: boolean;
    description: string;
  }>;
  tags: string[];
  thumbnailUrl?: string;
}

interface TemplateSelectorProps {
  onSelectTemplate: (template: PredefinedTemplate) => void;
  onClose: () => void;
  isOpen: boolean;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onSelectTemplate,
  onClose,
  isOpen,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedTemplate, setSelectedTemplate] = useState<PredefinedTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Predefined templates
  const predefinedTemplates: PredefinedTemplate[] = [
    {
      id: 'newsletter-basic',
      name: 'Newsletter Cơ Bản',
      description: 'Template newsletter đơn giản với header, nội dung chính và footer',
      type: EmailTemplateType.NEWSLETTER,
      icon: FileText,
      color: 'blue',
      subject: 'Newsletter tháng {month} - {company_name}',
      htmlContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        .button { background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{company_name}</h1>
            <p>Newsletter tháng {month}</p>
        </div>
        <div class="content">
            <h2>Xin chào {customer_name}!</h2>
            <p>Chúng tôi rất vui được chia sẻ với bạn những tin tức mới nhất từ {company_name}.</p>
            <p>{main_content}</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{cta_link}" class="button">Xem thêm</a>
            </p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {company_name}. All rights reserved.</p>
            <p>Bạn nhận được email này vì đã đăng ký newsletter của chúng tôi.</p>
        </div>
    </div>
</body>
</html>`,
      variables: [
        {
          name: 'company_name',
          type: 'TEXT',
          defaultValue: 'Công ty ABC',
          required: true,
          description: 'Tên công ty',
        },
        {
          name: 'customer_name',
          type: 'TEXT',
          defaultValue: 'Khách hàng',
          required: true,
          description: 'Tên khách hàng',
        },
        {
          name: 'month',
          type: 'TEXT',
          defaultValue: 'Tháng 1',
          required: true,
          description: 'Tháng phát hành',
        },
        {
          name: 'main_content',
          type: 'TEXT',
          defaultValue: 'Nội dung chính của newsletter...',
          required: true,
          description: 'Nội dung chính',
        },
        {
          name: 'cta_link',
          type: 'URL',
          defaultValue: 'https://example.com',
          required: true,
          description: 'Link call-to-action',
        },
      ],
      tags: ['newsletter', 'basic', 'monthly'],
    },
    {
      id: 'promotional-sale',
      name: 'Khuyến Mãi Đặc Biệt',
      description: 'Template thông báo khuyến mãi với thiết kế bắt mắt',
      type: EmailTemplateType.PROMOTIONAL,
      icon: Gift,
      color: 'red',
      subject: '🎉 Khuyến mãi {discount_percent}% - {product_name}',
      htmlContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Khuyến Mãi Đặc Biệt</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #fff5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .discount-badge { background-color: #ef4444; color: white; padding: 15px 30px; border-radius: 50px; font-size: 24px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .product-image { width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 10px; margin: 20px 0; }
        .cta-button { background-color: #ef4444; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold; font-size: 18px; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 KHUYẾN MÃI ĐẶC BIỆT</h1>
            <div class="discount-badge">GIẢM {discount_percent}%</div>
        </div>
        <div class="content">
            <h2>Xin chào {customer_name}!</h2>
            <p>Chúng tôi có tin tuyệt vời dành cho bạn!</p>
            <div style="text-align: center;">
                <img src="{product_image}" alt="{product_name}" class="product-image" />
                <h3>{product_name}</h3>
                <p style="font-size: 18px; color: #666;">Giá gốc: <strike>{original_price}</strike></p>
                <p style="font-size: 24px; color: #ef4444; font-weight: bold;">Giá khuyến mãi: {sale_price}</p>
            </div>
            <p>{promotion_description}</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{shop_link}" class="cta-button">MUA NGAY</a>
            </p>
            <p style="color: #ef4444; font-weight: bold;">⏰ Ưu đãi có hạn đến {expiry_date}</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {company_name}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`,
      variables: [
        {
          name: 'customer_name',
          type: 'TEXT',
          defaultValue: 'Khách hàng',
          required: true,
          description: 'Tên khách hàng',
        },
        {
          name: 'company_name',
          type: 'TEXT',
          defaultValue: 'Cửa hàng ABC',
          required: true,
          description: 'Tên cửa hàng',
        },
        {
          name: 'product_name',
          type: 'TEXT',
          defaultValue: 'Sản phẩm XYZ',
          required: true,
          description: 'Tên sản phẩm',
        },
        {
          name: 'discount_percent',
          type: 'NUMBER',
          defaultValue: '50',
          required: true,
          description: 'Phần trăm giảm giá',
        },
        {
          name: 'original_price',
          type: 'TEXT',
          defaultValue: '1,000,000đ',
          required: true,
          description: 'Giá gốc',
        },
        {
          name: 'sale_price',
          type: 'TEXT',
          defaultValue: '500,000đ',
          required: true,
          description: 'Giá khuyến mãi',
        },
        {
          name: 'product_image',
          type: 'IMAGE',
          defaultValue: 'https://via.placeholder.com/300x200',
          required: false,
          description: 'Hình ảnh sản phẩm',
        },
        {
          name: 'promotion_description',
          type: 'TEXT',
          defaultValue: 'Khuyến mãi đặc biệt chỉ có trong tuần này!',
          required: true,
          description: 'Mô tả khuyến mãi',
        },
        {
          name: 'shop_link',
          type: 'URL',
          defaultValue: 'https://example.com/shop',
          required: true,
          description: 'Link đến cửa hàng',
        },
        {
          name: 'expiry_date',
          type: 'DATE',
          defaultValue: '31/12/2024',
          required: true,
          description: 'Ngày hết hạn',
        },
      ],
      tags: ['promotion', 'sale', 'discount', 'special-offer'],
    },
    {
      id: 'welcome-new-user',
      name: 'Chào Mừng Thành Viên Mới',
      description: 'Template chào mừng người dùng mới đăng ký',
      type: EmailTemplateType.WELCOME,
      icon: UserPlus,
      color: 'green',
      subject: 'Chào mừng {customer_name} đến với {company_name}! 🎉',
      htmlContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào Mừng</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f0fdf4; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #22c55e, #16a34a); color: white; padding: 40px; text-align: center; }
        .content { padding: 30px; }
        .welcome-icon { font-size: 60px; margin-bottom: 20px; }
        .cta-button { background-color: #22c55e; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold; }
        .features { background-color: #f8fafc; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .feature-item { margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #e2e8f0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="welcome-icon">🎉</div>
            <h1>Chào mừng đến với {company_name}!</h1>
            <p>Cảm ơn bạn đã tham gia cộng đồng của chúng tôi</p>
        </div>
        <div class="content">
            <h2>Xin chào {customer_name}!</h2>
            <p>Chúng tôi rất vui mừng chào đón bạn trở thành thành viên mới của {company_name}. Bạn đã thực hiện một lựa chọn tuyệt vời!</p>
            
            <div class="features">
                <h3>Những gì bạn có thể làm:</h3>
                <div class="feature-item">✅ Truy cập đầy đủ các tính năng premium</div>
                <div class="feature-item">✅ Nhận thông báo về các ưu đãi đặc biệt</div>
                <div class="feature-item">✅ Tham gia cộng đồng người dùng</div>
                <div class="feature-item">✅ Hỗ trợ khách hàng 24/7</div>
            </div>

            <p>Để bắt đầu, hãy khám phá các tính năng của chúng tôi:</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{getting_started_link}" class="cta-button">Bắt Đầu Ngay</a>
            </p>
            
            <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi tại <a href="mailto:{support_email}">{support_email}</a></p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {company_name}. All rights reserved.</p>
            <p>Bạn nhận được email này vì đã đăng ký tài khoản tại {company_name}</p>
        </div>
    </div>
</body>
</html>`,
      variables: [
        {
          name: 'customer_name',
          type: 'TEXT',
          defaultValue: 'Khách hàng',
          required: true,
          description: 'Tên khách hàng',
        },
        {
          name: 'company_name',
          type: 'TEXT',
          defaultValue: 'Công ty ABC',
          required: true,
          description: 'Tên công ty',
        },
        {
          name: 'getting_started_link',
          type: 'URL',
          defaultValue: 'https://example.com/getting-started',
          required: true,
          description: 'Link hướng dẫn bắt đầu',
        },
        {
          name: 'support_email',
          type: 'TEXT',
          defaultValue: '<EMAIL>',
          required: true,
          description: 'Email hỗ trợ',
        },
      ],
      tags: ['welcome', 'onboarding', 'new-user'],
    },
    {
      id: 'abandoned-cart',
      name: 'Giỏ Hàng Bỏ Quên',
      description: 'Template nhắc nhở khách hàng về giỏ hàng chưa thanh toán',
      type: EmailTemplateType.ABANDONED_CART,
      icon: ShoppingCart,
      color: 'yellow',
      subject: 'Bạn đã quên gì đó trong giỏ hàng! 🛒',
      htmlContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giỏ Hàng Bỏ Quên</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #fffbeb; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .cart-icon { font-size: 60px; margin-bottom: 20px; }
        .product-item { border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 15px 0; display: flex; align-items: center; }
        .product-image { width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-right: 15px; }
        .product-info { flex: 1; }
        .product-name { font-weight: bold; font-size: 16px; margin-bottom: 5px; }
        .product-price { color: #f59e0b; font-weight: bold; font-size: 18px; }
        .cta-button { background-color: #f59e0b; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold; font-size: 18px; }
        .urgency { background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="cart-icon">🛒</div>
            <h1>Bạn đã quên gì đó!</h1>
            <p>Những sản phẩm tuyệt vời đang chờ bạn</p>
        </div>
        <div class="content">
            <h2>Xin chào {customer_name}!</h2>
            <p>Chúng tôi nhận thấy bạn đã để lại một số sản phẩm trong giỏ hàng. Đừng để chúng chờ đợi quá lâu nhé!</p>

            <div class="product-item">
                <img src="{product_image}" alt="{product_name}" class="product-image" />
                <div class="product-info">
                    <div class="product-name">{product_name}</div>
                    <div class="product-price">{product_price}</div>
                </div>
            </div>

            <div class="urgency">
                <strong>⏰ Chỉ còn {stock_quantity} sản phẩm trong kho!</strong><br>
                Hoàn tất đơn hàng ngay để không bỏ lỡ cơ hội sở hữu sản phẩm này.
            </div>

            <p style="text-align: center; margin: 30px 0;">
                <a href="{checkout_link}" class="cta-button">HOÀN TẤT ĐƠN HÀNG</a>
            </p>

            <p>Tổng giá trị giỏ hàng: <strong style="color: #f59e0b; font-size: 20px;">{cart_total}</strong></p>
            <p>Nếu bạn có bất kỳ câu hỏi nào, liên hệ với chúng tôi tại <a href="mailto:{support_email}">{support_email}</a></p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {company_name}. All rights reserved.</p>
            <p>Email này được gửi vì bạn có sản phẩm trong giỏ hàng tại {company_name}</p>
        </div>
    </div>
</body>
</html>`,
      variables: [
        {
          name: 'customer_name',
          type: 'TEXT',
          defaultValue: 'Khách hàng',
          required: true,
          description: 'Tên khách hàng',
        },
        {
          name: 'company_name',
          type: 'TEXT',
          defaultValue: 'Cửa hàng ABC',
          required: true,
          description: 'Tên cửa hàng',
        },
        {
          name: 'product_name',
          type: 'TEXT',
          defaultValue: 'Áo thun premium',
          required: true,
          description: 'Tên sản phẩm',
        },
        {
          name: 'product_price',
          type: 'TEXT',
          defaultValue: '299,000đ',
          required: true,
          description: 'Giá sản phẩm',
        },
        {
          name: 'product_image',
          type: 'IMAGE',
          defaultValue: 'https://via.placeholder.com/80x80',
          required: false,
          description: 'Hình ảnh sản phẩm',
        },
        {
          name: 'stock_quantity',
          type: 'NUMBER',
          defaultValue: '3',
          required: true,
          description: 'Số lượng còn lại',
        },
        {
          name: 'cart_total',
          type: 'TEXT',
          defaultValue: '599,000đ',
          required: true,
          description: 'Tổng giá trị giỏ hàng',
        },
        {
          name: 'checkout_link',
          type: 'URL',
          defaultValue: 'https://example.com/checkout',
          required: true,
          description: 'Link thanh toán',
        },
        {
          name: 'support_email',
          type: 'TEXT',
          defaultValue: '<EMAIL>',
          required: true,
          description: 'Email hỗ trợ',
        },
      ],
      tags: ['abandoned-cart', 'reminder', 'ecommerce'],
    },
    {
      id: 'follow-up-survey',
      name: 'Khảo Sát Phản Hồi',
      description: 'Template yêu cầu khách hàng đánh giá và phản hồi',
      type: EmailTemplateType.FOLLOW_UP,
      icon: MessageCircle,
      color: 'indigo',
      subject: 'Chia sẻ trải nghiệm của bạn với {company_name} 📝',
      htmlContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Khảo Sát Phản Hồi</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #6366f1, #4f46e5); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .survey-icon { font-size: 60px; margin-bottom: 20px; }
        .rating-section { background-color: #f1f5f9; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .stars { font-size: 30px; margin: 15px 0; }
        .star { color: #fbbf24; margin: 0 5px; cursor: pointer; }
        .cta-button { background-color: #6366f1; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold; margin: 10px; }
        .incentive { background-color: #ecfdf5; border: 1px solid #10b981; border-radius: 8px; padding: 15px; margin: 20px 0; text-align: center; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="survey-icon">📝</div>
            <h1>Ý kiến của bạn rất quan trọng!</h1>
            <p>Giúp chúng tôi cải thiện dịch vụ</p>
        </div>
        <div class="content">
            <h2>Xin chào {customer_name}!</h2>
            <p>Cảm ơn bạn đã sử dụng dịch vụ của {company_name}. Chúng tôi rất mong nhận được phản hồi từ bạn để có thể cải thiện chất lượng dịch vụ.</p>

            <div class="rating-section">
                <h3>Bạn đánh giá trải nghiệm như thế nào?</h3>
                <div class="stars">
                    <span class="star">⭐</span>
                    <span class="star">⭐</span>
                    <span class="star">⭐</span>
                    <span class="star">⭐</span>
                    <span class="star">⭐</span>
                </div>
                <p>Nhấp vào số sao để đánh giá</p>
            </div>

            <p>Khảo sát chỉ mất 2-3 phút và sẽ giúp chúng tôi:</p>
            <ul>
                <li>✅ Cải thiện chất lượng sản phẩm/dịch vụ</li>
                <li>✅ Nâng cao trải nghiệm khách hàng</li>
                <li>✅ Phát triển tính năng mới phù hợp</li>
            </ul>

            <div class="incentive">
                <strong>🎁 Ưu đãi đặc biệt!</strong><br>
                Hoàn thành khảo sát để nhận mã giảm giá {discount_code} trị giá {discount_amount}
            </div>

            <p style="text-align: center; margin: 30px 0;">
                <a href="{survey_link}" class="cta-button">BẮT ĐẦU KHẢO SÁT</a>
            </p>

            <p style="font-size: 14px; color: #666;">Khảo sát sẽ hết hạn vào {expiry_date}. Đừng bỏ lỡ cơ hội nhận ưu đãi nhé!</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {company_name}. All rights reserved.</p>
            <p>Cảm ơn bạn đã dành thời gian cho chúng tôi</p>
        </div>
    </div>
</body>
</html>`,
      variables: [
        {
          name: 'customer_name',
          type: 'TEXT',
          defaultValue: 'Khách hàng',
          required: true,
          description: 'Tên khách hàng',
        },
        {
          name: 'company_name',
          type: 'TEXT',
          defaultValue: 'Công ty ABC',
          required: true,
          description: 'Tên công ty',
        },
        {
          name: 'survey_link',
          type: 'URL',
          defaultValue: 'https://example.com/survey',
          required: true,
          description: 'Link khảo sát',
        },
        {
          name: 'discount_code',
          type: 'TEXT',
          defaultValue: 'SURVEY20',
          required: true,
          description: 'Mã giảm giá',
        },
        {
          name: 'discount_amount',
          type: 'TEXT',
          defaultValue: '100,000đ',
          required: true,
          description: 'Số tiền giảm giá',
        },
        {
          name: 'expiry_date',
          type: 'DATE',
          defaultValue: '31/12/2024',
          required: true,
          description: 'Ngày hết hạn',
        },
      ],
      tags: ['survey', 'feedback', 'follow-up', 'customer-satisfaction'],
    },
  ];

  const handleTemplateClick = (template: PredefinedTemplate) => {
    setSelectedTemplate(template);
  };

  const handlePreview = (template: PredefinedTemplate) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };

  const handleUseTemplate = () => {
    if (selectedTemplate) {
      onSelectTemplate(selectedTemplate);
      onClose();
    }
  };

  const renderPreviewContent = () => {
    if (!selectedTemplate) return null;

    let previewHtml = selectedTemplate.htmlContent;
    selectedTemplate.variables.forEach(variable => {
      const placeholder = `{${variable.name}}`;
      previewHtml = previewHtml.replace(new RegExp(placeholder, 'g'), variable.defaultValue);
    });

    return (
      <div
        dangerouslySetInnerHTML={{ __html: previewHtml }}
        style={{
          maxHeight: '500px',
          overflow: 'auto',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
        }}
      />
    );
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={t('marketing:email.templates.selector.title', 'Chọn Template Có Sẵn')}
        size="xl"
      >
        <div className="space-y-6">
          <Alert
            type="info"
            message={t('marketing:email.templates.selector.info.title', 'Template có sẵn')}
            description={t(
              'marketing:email.templates.selector.info.description',
              'Chọn một template có sẵn để bắt đầu nhanh chóng. Bạn có thể tùy chỉnh nội dung sau khi chọn.'
            )}
          />

          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
            className="w-full"
          >
            {predefinedTemplates.map(template => {
              const IconComponent = template.icon;
              const isSelected = selectedTemplate?.id === template.id;

              return (
                <Card
                  key={template.id}
                  className={`
                    cursor-pointer transition-all duration-200 border h-full
                    ${
                      isSelected
                        ? `border-${template.color}-500 bg-${template.color}-50 dark:bg-${template.color}-900/20 shadow-md`
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }
                    hover:shadow-lg
                  `}
                  onClick={() => handleTemplateClick(template)}
                >
                  <div className="p-4 space-y-3">
                    {/* Icon and Type */}
                    <div className="flex items-center justify-between">
                      <div
                        className={`
                        w-12 h-12 rounded-lg flex items-center justify-center
                        ${
                          isSelected
                            ? `bg-${template.color}-500 text-white`
                            : `bg-${template.color}-100 dark:bg-${template.color}-900/30 text-${template.color}-600 dark:text-${template.color}-400`
                        }
                      `}
                      >
                        <IconComponent size={24} />
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={e => {
                          e.stopPropagation();
                          handlePreview(template);
                        }}
                        className="opacity-70 hover:opacity-100"
                      >
                        <Eye size={16} />
                      </Button>
                    </div>

                    {/* Content */}
                    <div className="space-y-2">
                      <Typography
                        variant="h6"
                        className={`font-semibold ${
                          isSelected
                            ? `text-${template.color}-900 dark:text-${template.color}-100`
                            : 'text-gray-900 dark:text-gray-100'
                        }`}
                      >
                        {template.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        className={`text-sm ${
                          isSelected
                            ? `text-${template.color}-700 dark:text-${template.color}-300`
                            : 'text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        {template.description}
                      </Typography>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-1 mt-2">
                        {template.tags.slice(0, 2).map((tag, index) => (
                          <span
                            key={index}
                            className={`
                              px-2 py-1 text-xs rounded-full
                              ${
                                isSelected
                                  ? `bg-${template.color}-200 text-${template.color}-800 dark:bg-${template.color}-800 dark:text-${template.color}-200`
                                  : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                              }
                            `}
                          >
                            {tag}
                          </span>
                        ))}
                        {template.tags.length > 2 && (
                          <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                            +{template.tags.length - 2}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })}
          </ResponsiveGrid>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button onClick={handleUseTemplate} disabled={!selectedTemplate}>
              {t('marketing:email.templates.selector.useTemplate', 'Sử dụng Template')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Preview Modal */}
      <Modal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        title={selectedTemplate ? `Preview: ${selectedTemplate.name}` : 'Preview'}
        size="xl"
      >
        <div className="space-y-4">
          {selectedTemplate && (
            <>
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                  Subject:
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {selectedTemplate.subject.replace(/{(\w+)}/g, (match, key) => {
                    const variable = selectedTemplate.variables.find(v => v.name === key);
                    return variable ? variable.defaultValue : match;
                  })}
                </Typography>
              </div>
              {renderPreviewContent()}
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default TemplateSelector;
