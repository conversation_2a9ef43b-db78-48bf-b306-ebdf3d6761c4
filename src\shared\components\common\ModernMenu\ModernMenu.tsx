import React, { useRef, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Icon } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

export interface ModernMenuItem {
  id: string;
  label?: React.ReactNode;
  icon?: IconName | React.ReactNode;
  onClick?: (() => void) | undefined;
  disabled?: boolean | undefined;
  divider?: boolean | undefined;
  /**
   * Không tự động đóng menu khi click vào item này
   */
  keepOpen?: boolean;
}

interface ModernMenuProps {
  items: ModernMenuItem[];
  isOpen: boolean;
  onClose: () => void;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  width?: string;
  /**
   * Vị trí menu theo trục X (tính từ bên trái)
   * Nếu không được cung cấp, menu sẽ được đặt theo placement
   */
  offsetX?: number;
  /**
   * Vị trí menu theo trục Y (tính từ bên trên)
   * Nếu không được cung cấp, menu sẽ được đặt theo placement
   */
  offsetY?: number;
  /**
   * Ưu tiên hiển thị menu bên phải nếu không đủ không gian bên trái
   */
  preferRight?: boolean;
  /**
   * Ưu tiên hiển thị menu bên trên nếu không đủ không gian bên dưới
   */
  preferTop?: boolean;
  /**
   * Ref của trigger element để tính toán vị trí
   */
  triggerRef?: React.RefObject<HTMLElement>;
}

/**
 * Component ModernMenu hiển thị menu với thiết kế hiện đại
 */
const ModernMenu: React.FC<ModernMenuProps> = ({
  items,
  isOpen,
  onClose,
  placement = 'bottom',
  width = '200px',
  offsetX,
  offsetY,
  preferRight = false,
  preferTop = false,
  triggerRef: externalTriggerRef,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const internalTriggerRef = useRef<HTMLElement | null>(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });

  // Sử dụng external triggerRef nếu có, nếu không thì tìm trigger element
  const triggerRef = externalTriggerRef || internalTriggerRef;

  // Tìm trigger element (parent của component này) nếu không có external ref
  useEffect(() => {
    if (isOpen && !externalTriggerRef) {
      // Tìm trigger element chính xác hơn
      let currentElement = menuRef.current?.parentElement;

      // Tìm element có class 'relative' hoặc element chứa IconCard
      while (currentElement && !currentElement.classList.contains('relative')) {
        currentElement = currentElement.parentElement;
      }

      if (currentElement) {
        internalTriggerRef.current = currentElement;
      }
    }
  }, [isOpen, externalTriggerRef]);

  // Cập nhật vị trí menu khi triggerRef thay đổi hoặc menu được mở
  useEffect(() => {
    if (isOpen) {
      const updatePosition = () => {
        const position = offsetX !== undefined && offsetY !== undefined
          ? { top: offsetY, left: offsetX }
          : getMenuPosition();
        setMenuPosition(position);
      };

      // Cập nhật ngay lập tức
      updatePosition();

      // Retry sau một khoảng thời gian ngắn nếu triggerRef chưa sẵn sàng
      const retryTimeout = setTimeout(() => {
        if (triggerRef.current) {
          updatePosition();
        }
      }, 10);

      // Lắng nghe sự kiện scroll và resize để cập nhật vị trí
      const handleScrollResize = () => {
        if (triggerRef.current) {
          updatePosition();
        }
      };

      // Xử lý click outside
      const handleDocumentClick = (e: MouseEvent) => {
        if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
          onClose();
        }
      };

      window.addEventListener('scroll', handleScrollResize, true);
      window.addEventListener('resize', handleScrollResize);
      document.addEventListener('mousedown', handleDocumentClick);

      return () => {
        clearTimeout(retryTimeout);
        window.removeEventListener('scroll', handleScrollResize, true);
        window.removeEventListener('resize', handleScrollResize);
        document.removeEventListener('mousedown', handleDocumentClick);
      };
    }

    // Return undefined when isOpen is false to satisfy TypeScript
    return undefined;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, triggerRef, offsetX, offsetY, placement, width, preferRight, preferTop, onClose]);

  // Tính toán vị trí menu với logic cải tiến
  const getMenuPosition = () => {
    if (!triggerRef.current) {
      console.warn('ModernMenu: triggerRef.current is null, using fallback position');
      // Fallback: center of viewport
      return {
        top: window.innerHeight / 2 - 150,
        left: window.innerWidth / 2 - 100
      };
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();

    // Validate triggerRect
    if (!triggerRect || triggerRect.width === 0 || triggerRect.height === 0) {
      console.warn('ModernMenu: Invalid triggerRect, using fallback position');
      return {
        top: window.innerHeight / 2 - 150,
        left: window.innerWidth / 2 - 100
      };
    }

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = parseInt(width.replace('px', '')) || 200;
    const menuHeight = Math.min(items.length * 40 + 16, 400); // Tính toán chính xác hơn chiều cao menu
    const margin = 8;

    // Tính toán vị trí ban đầu dựa trên placement
    let top: number;
    let left: number;

    switch (placement) {
      case 'top':
        top = triggerRect.top - menuHeight - margin;
        left = triggerRect.left + (triggerRect.width - menuWidth) / 2; // Căn giữa với trigger
        break;
      case 'right':
        top = triggerRect.top + (triggerRect.height - menuHeight) / 2; // Căn giữa theo chiều dọc
        left = triggerRect.right + margin;
        break;
      case 'left':
        top = triggerRect.top + (triggerRect.height - menuHeight) / 2; // Căn giữa theo chiều dọc
        left = triggerRect.left - menuWidth - margin;
        break;
      case 'bottom':
      default:
        top = triggerRect.bottom + margin;
        left = triggerRect.left + (triggerRect.width - menuWidth) / 2; // Căn giữa với trigger
        break;
    }

    // Logic thông minh để điều chỉnh vị trí khi bị tràn viewport

    // Điều chỉnh theo chiều ngang
    if (left + menuWidth > viewportWidth - margin) {
      // Menu bị tràn bên phải
      if (preferRight || placement === 'right') {
        left = triggerRect.right - menuWidth; // Căn phải với trigger
      } else {
        left = viewportWidth - menuWidth - margin; // Căn sát lề phải viewport
      }
    }

    if (left < margin) {
      // Menu bị tràn bên trái
      left = margin;
    }

    // Điều chỉnh theo chiều dọc
    if (top + menuHeight > viewportHeight - margin) {
      // Menu bị tràn phía dưới
      if (preferTop || placement === 'top') {
        top = triggerRect.top - menuHeight - margin; // Hiển thị phía trên trigger
      } else {
        top = viewportHeight - menuHeight - margin; // Căn sát lề dưới viewport
      }
    }

    if (top < margin) {
      // Menu bị tràn phía trên
      top = margin;
    }

    // Đảm bảo menu luôn hiển thị trong viewport
    top = Math.max(margin, Math.min(top, viewportHeight - menuHeight - margin));
    left = Math.max(margin, Math.min(left, viewportWidth - menuWidth - margin));

    return { top, left };
  };

  if (!isOpen) return null;

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Sử dụng vị trí đã được tính toán trong useEffect
  const position = menuPosition;

  // Render menu sử dụng portal để tránh bị cắt bởi overflow
  const menuContent = (
    <div
        ref={menuRef}
        className="fixed bg-white dark:bg-gray-800 rounded-lg shadow-xl z-[100000] overflow-hidden animate-fade-in modern-menu"
        data-portal-menu="true"
        style={{
          width,
          top: `${position.top}px`,
          left: `${position.left}px`,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        }}
        onClick={handleMenuClick}
      >
        <div className="p-1">
          {items.map(item =>
            item.divider ? (
              <div key={item.id} className="h-px bg-gray-200 dark:bg-gray-700 my-1" />
            ) : (
              <button
                key={item.id}
                className={`
                  flex items-center w-full px-3 py-2 text-left
                  ${
                    item.disabled
                      ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                      : 'text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer'
                  }
                  rounded-md transition-colors
                `}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();

                  if (!item.disabled && item.onClick) {
                    try {
                      item.onClick();
                    } catch (error) {
                      console.error('ModernMenu: Error executing onClick:', error);
                    }

                    // Chỉ đóng menu nếu item không có keepOpen flag
                    if (!item.keepOpen) {
                      onClose();
                    }
                  }
                }}
                disabled={item.disabled}
              >
                {/* Icon */}
                {item.icon && (
                  <div className="mr-2 flex-shrink-0 text-gray-600 dark:text-gray-400">
                    {typeof item.icon === 'string' ? (
                      <Icon name={item.icon as IconName} size="sm" />
                    ) : (
                      item.icon
                    )}
                  </div>
                )}

                {/* Label */}
                <span className="whitespace-nowrap">{item.label}</span>
              </button>
            )
          )}
        </div>
      </div>
  );

  // Sử dụng portal để render menu ở body level
  return createPortal(menuContent, document.body);
};

export default ModernMenu;
