import { createBrowser<PERSON>outer, RouterProvider } from 'react-router-dom';

// Import module routes
import formRoutes from './modules/formRoutes';
import componentRoutes from './modules/componentRoutes';
import aiRoutes from './modules/aiRoutes';
import commonRoutes from './modules/commonRoutes';
import blogRoutes from '@/modules/blog/routers/blogRoutes';
import { marketplaceRoutes } from '@/modules/marketplace';
import { businessRoutes } from '@/modules/business';
import dataRoutes from '@/modules/data/routers/dataRoutes';
import adminDataRoutes from '@/modules/admin/data/routers/adminDataRoutes';
import { authRoutes } from '@/modules/auth';
import { profileRoutes } from '@/modules/profile';
import { marketingRoutes } from '@/modules/marketing';
import { rpointRoutes } from '@/modules/rpoint';
import {
  rpointAdminRoutes as newRPointAdminRoutes,
  rpointAdminRoutes,
} from '@/modules/admin/r-point/routers/rpointAdminRoutes';
import { subscriptionRoutes } from '@/modules/subscription';
import { integrationRoutes } from '@/modules/integration';
import { employeeRoutes } from '@/modules/admin/employee/routers/employeeRoutes';
import authAdminRouters from '@/modules/admin/auth/routers/authAdminRouters';
import { userRoutes } from '@/modules/admin/user/routers';
import { affiliateRoutes } from '@/modules/admin/affiliate';
import { blogAdminRoutes } from '@/modules/admin/blog/routers';
import { adminToolRoutes } from '@/modules/admin/tool/routers';
import { toolRoutes } from '@/modules/tools/routers';
import dashboardAdminRoutes from '@/modules/admin/dashboard/routers/dashboardRouters';
import { marketingAdminRoutes } from '@/modules/admin/marketing/routers/marketingRoutes';

import { businessAdminRoutes } from '@/modules/admin/business';
import { settingsRoutes } from '@/modules/settings';
import agentRoutes from '@/modules/ai-agents/routers/agentRouter';
import marketplaceAdminRoutes from '@/modules/admin/marketplace/routers/marketplaceRoutes';
import adminIntegrationRoutes from '@/modules/admin/integration/routers/adminIntegrationRoutes';
import { contractRoutes } from '@/modules/contract';
import { contractAffiliateRoutes } from '@/modules/contract-affiliate';
import { userAffiliateRoutes } from '@/modules/user/affiliate/routers/userAffiliateRoutes';
import calendarRoutes from '@/modules/calendar/calendarRoutes';
import subscriptionAdminRoutes from '@/modules/admin/subscription/routers/subscriptionRoutes';
import { externalAgentRoutes } from '@/modules/external-agents/routers';
import userDatasetRoutes from '@/modules/user-dataset/routers/userDatasetRoutes';

import { adminAgentRoutes } from '@/modules/admin/agent/routers/adminAgentRoutes';
import { threadsRoutes } from '@/modules/threads';
import adminDatasetRoutes from '@/modules/admin/dataset/routers/adminDatasetRoutes';
import { profileRoutes as adminProfileRoutes } from '@/modules/admin/profile/routers';
import adminUser from '@/modules/admin/user/routers/userRoutes';
import { dashboardRoutes } from '@/modules/dashboard';
import configRoutes from '@/modules/admin/config/routers/configRouter';
import { helpRoutes } from '@/modules/help';
import { helpAdminRoutes } from '@/modules/admin/help';
import { usageRoutes } from '@/modules/usage';
import testRoutes from '@/modules/test/routers/testRoutes';
import genericRoutes from '@/modules/generic/routers/genericRoutes';
import { adminSettingsRoutes } from '@/modules/admin/settings';
import { workflowRoutes } from '@/modules/workflow';
import servicePackagesRoutes from '@/modules/service-packages/routers/servicePackagesRoutes';

// Import route protection utilities
import { wrapRoutesWithAuth, categorizeRoutes, wrapRoutesWithSubscriptionProtection } from './utils/routeUtils';
import { AuthType } from '@/shared/hooks/useAuthCommon';
import contractAdminRoutes from '@/modules/admin/contract/routers/contractRoutes';

/**
 * Tập hợp tất cả routes
 */
const allRoutes = [
  // USER
  ...authRoutes,
  ...blogRoutes,
  ...profileRoutes,
  ...marketplaceRoutes,
  ...businessRoutes,
  ...dataRoutes,
  ...integrationRoutes,
  ...rpointRoutes,
  ...subscriptionRoutes,
  ...marketingRoutes,
  ...dashboardRoutes,
  ...helpRoutes,
  ...usageRoutes,
  ...testRoutes,
  ...workflowRoutes,
  ...servicePackagesRoutes,

  // ADMIN
  ...authAdminRouters,
  ...newRPointAdminRoutes,
  ...userRoutes,
  ...affiliateRoutes,
  ...employeeRoutes,
  ...blogAdminRoutes,
  ...marketingAdminRoutes,
  ...marketplaceAdminRoutes,
  ...adminDataRoutes,
  ...dashboardAdminRoutes,
  ...componentRoutes,
  ...formRoutes,
  ...aiRoutes,
  ...rpointAdminRoutes,
  ...adminToolRoutes,
  ...toolRoutes,
  ...businessAdminRoutes,
  ...settingsRoutes,
  ...agentRoutes,
  ...adminIntegrationRoutes,
  ...contractRoutes,
  ...contractAffiliateRoutes,
  ...userAffiliateRoutes,
  ...calendarRoutes,
  ...subscriptionAdminRoutes,
  ...externalAgentRoutes,
  ...adminAgentRoutes,
  ...userDatasetRoutes,

  ...adminDatasetRoutes,
  ...threadsRoutes,
  ...adminProfileRoutes,
  ...adminUser,
  ...configRoutes,
  ...contractAdminRoutes,
  ...helpAdminRoutes,
  ...adminSettingsRoutes,
  ...genericRoutes,
  ...commonRoutes,
];

/**
 * Phân loại routes và áp dụng protection
 */
const {
  userRoutes: protectedUserRoutes,
  adminRoutes: protectedAdminRoutes,
  publicRoutes,
} = categorizeRoutes(allRoutes);

/**
 * Create router with protected routes
 */
const router = createBrowserRouter([
  // Public routes (auth routes) - không cần protection
  ...publicRoutes,

  // User routes - yêu cầu USER token và subscription protection
  ...wrapRoutesWithAuth(
    wrapRoutesWithSubscriptionProtection(protectedUserRoutes),
    AuthType.USER,
    '/auth'
  ),

  // Admin routes - yêu cầu ADMIN token (không có subscription protection)
  ...wrapRoutesWithAuth(protectedAdminRoutes, AuthType.ADMIN, '/admin/auth'),
]);

const AppRouter = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;
