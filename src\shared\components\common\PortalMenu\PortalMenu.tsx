import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';

/**
 * Interface cho props của PortalMenu component
 */
export interface PortalMenuProps {
  /**
   * Ref cho menu element để xử lý click outside
   */
  menuRef: React.RefObject<HTMLDivElement>;
  
  /**
   * Vị trí hiển thị menu
   */
  position: { top: number; left: number };
  
  /**
   * Nội dung hiển thị trong menu
   */
  children: React.ReactNode;
  
  /**
   * Callback khi đóng menu
   */
  onClose: () => void;
  
  /**
   * Chiều rộng của menu (mặc định: 220px)
   */
  width?: string | number;
  
  /**
   * Z-index của menu (mặc định: 100000)
   */
  zIndex?: number;
  
  /**
   * Class CSS bổ sung
   */
  className?: string;
}

/**
 * Component Portal Menu cho dropdown menu với khả năng tùy chỉnh
 * 
 * Component này sử dụng React Portal để render menu bên ngoài DOM tree hiện tại,
 * gi<PERSON><PERSON> tr<PERSON>h các vấn đề về z-index và overflow. Hỗ trợ click outside để đóng menu.
 * 
 * @example
 * ```tsx
 * const [showMenu, setShowMenu] = useState(false);
 * const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
 * const menuRef = useRef<HTMLDivElement>(null);
 * 
 * return (
 *   <>
 *     <button onClick={handleToggleMenu}>Toggle Menu</button>
 *     {showMenu && (
 *       <PortalMenu
 *         menuRef={menuRef}
 *         position={menuPosition}
 *         onClose={() => setShowMenu(false)}
 *       >
 *         <div>Menu Content</div>
 *       </PortalMenu>
 *     )}
 *   </>
 * );
 * ```
 */
const PortalMenu: React.FC<PortalMenuProps> = ({
  menuRef,
  position,
  children,
  onClose,
  width = '220px',
  zIndex = 100000,
  className = '',
}) => {
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuRef, onClose]);

  return ReactDOM.createPortal(
    <div
      ref={menuRef}
      className={`fixed animate-fade-in ${className}`}
      data-portal-menu="true"
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        top: position.top,
        left: position.left,
        zIndex,
      }}
    >
      {children}
    </div>,
    document.body
  );
};

export default PortalMenu;
