import React, { useRef, useEffect, useCallback, useState } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
} from 'lucide-react';

interface InlineEditorProps {
  content: string;
  onSave: (content: string) => void;
  onCancel: () => void;
  className?: string;
}

const InlineEditor: React.FC<InlineEditorProps> = ({
  content,
  onSave,
  onCancel,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);

  const handleSave = useCallback(() => {
    if (editorRef.current) {
      onSave(editorRef.current.innerHTML);
    }
  }, [onSave]);

  useEffect(() => {
    if (editorRef.current) {
      // Set initial content
      editorRef.current.innerHTML = content;

      // Ensure contentEditable is set
      editorRef.current.contentEditable = 'true';

      // Focus and position cursor at the end
      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.focus();

          // Position cursor at the end
          const range = document.createRange();
          const selection = window.getSelection();
          range.selectNodeContents(editorRef.current);
          range.collapse(false);
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }, 100);
    }

    // Handle click outside to close editor
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        // Don't close if clicking on toolbar buttons
        const isToolbarClick =
          target instanceof Element &&
          (target.closest('.inline-editor') ||
            target.closest('button') ||
            target.closest('select'));

        if (!isToolbarClick) {
          handleSave();
        }
      }
    };

    // Handle Escape key to cancel
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCancel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [content, onSave, onCancel, handleSave]);

  const handleCommand = (command: string, value?: string) => {
    if (!editorRef.current) return;

    console.log('Executing command:', command, 'with value:', value);

    // Focus editor first
    editorRef.current.focus();

    // Ensure we have a selection or create one
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      const range = document.createRange();
      range.selectNodeContents(editorRef.current);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    try {
      // Special handling for different commands
      let success = false;

      switch (command) {
        case 'fontSize': {
          // Convert font size number to actual size
          const sizeMap: { [key: string]: string } = {
            '1': '10px',
            '2': '13px',
            '3': '16px',
            '4': '18px',
            '5': '24px',
            '6': '32px',
            '7': '48px',
          };
          const actualSize = sizeMap[value || '3'] || '16px';
          success = document.execCommand('fontSize', false, '7'); // Use size 7 first
          if (success && selection && selection.rangeCount > 0) {
            // Then change to actual pixel size
            const range = selection.getRangeAt(0);
            const span = document.createElement('span');
            span.style.fontSize = actualSize;
            try {
              range.surroundContents(span);
            } catch (e) {
              // If surroundContents fails, try different approach
              span.innerHTML = range.toString();
              range.deleteContents();
              range.insertNode(span);
            }
          }
          break;
        }
        default:
          success = document.execCommand(command, false, value);
      }

      console.log(`Command ${command} executed:`, success);

      if (!success) {
        console.warn(`Command ${command} failed`);
      }

      // Keep focus on editor
      editorRef.current.focus();
    } catch (error) {
      console.error('execCommand error:', error);
    }
  };

  return (
    <div
      className={`inline-editor bg-white dark:bg-gray-800 border border-blue-500 rounded-lg shadow-lg ${className}`}
    >
      {/* Compact Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('bold');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Bold"
        >
          <Bold size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('italic');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Italic"
        >
          <Italic size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('underline');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Underline"
        >
          <Underline size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyLeft');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Left"
        >
          <AlignLeft size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyCenter');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Center"
        >
          <AlignCenter size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyRight');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Right"
        >
          <AlignRight size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('insertUnorderedList');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Bullet List"
        >
          <List size={14} />
        </button>

        <select
          className="text-xs border-0 bg-transparent text-gray-700 dark:text-gray-300 ml-2"
          onChange={e => {
            e.stopPropagation();
            handleCommand('fontSize', e.target.value);
          }}
          onMouseDown={e => e.stopPropagation()}
          onClick={e => e.stopPropagation()}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[60px] outline-none text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
        contentEditable={true}
        suppressContentEditableWarning={true}
        spellCheck={false}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
        }}
        onInput={() => {
          // Optional: real-time content sync
        }}
      />
    </div>
  );
};

export default InlineEditor;
