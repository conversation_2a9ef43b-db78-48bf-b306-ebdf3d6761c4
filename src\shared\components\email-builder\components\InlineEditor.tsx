import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
} from 'lucide-react';

interface InlineEditorProps {
  content: string;
  onContentChange: (content: string) => void;
  onClose: () => void;
  elementType: string;
}

const InlineEditor: React.FC<InlineEditorProps> = ({
  content,
  onContentChange,
  onClose,
  elementType,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const cursorPositionRef = useRef<{ start: number; end: number } | null>(null);

  // Save cursor position
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0 && editorRef.current) {
      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(editorRef.current);
      preCaretRange.setEnd(range.startContainer, range.startOffset);
      const start = preCaretRange.toString().length;

      preCaretRange.setEnd(range.endContainer, range.endOffset);
      const end = preCaretRange.toString().length;

      cursorPositionRef.current = { start, end };
    }
  }, []);

  useEffect(() => {
    if (editorRef.current) {
      // Set initial content
      editorRef.current.innerHTML = content;

      // Enable design mode for better editing
      try {
        editorRef.current.contentEditable = 'true';
        editorRef.current.focus();

        // Set cursor to end initially
        setTimeout(() => {
          if (editorRef.current) {
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(editorRef.current);
            range.collapse(false);
            selection?.removeAllRanges();
            selection?.addRange(range);
          }
        }, 10);
      } catch (error) {
        console.warn('Editor setup error:', error);
      }
    }
  }, [content]);

  const handleCommand = (command: string, value?: string) => {
    if (!editorRef.current) return;

    console.log('Executing command:', command, 'with value:', value);

    // Focus editor first and ensure it's focused
    editorRef.current.focus();

    // Ensure we have a selection
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      // Create a selection at the end if none exists
      const range = document.createRange();
      range.selectNodeContents(editorRef.current);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    // Execute command with proper error handling
    try {
      let success = false;

      // Note: execCommand is deprecated but still widely supported
      // and there's no direct replacement for rich text editing yet
      // @ts-ignore - execCommand deprecation warning
      switch (command) {
        case 'bold':
          success = document.execCommand('bold', false);
          break;
        case 'italic':
          success = document.execCommand('italic', false);
          break;
        case 'underline':
          success = document.execCommand('underline', false);
          break;
        case 'justifyLeft':
          success = document.execCommand('justifyLeft', false);
          break;
        case 'justifyCenter':
          success = document.execCommand('justifyCenter', false);
          break;
        case 'justifyRight':
          success = document.execCommand('justifyRight', false);
          break;
        case 'insertUnorderedList':
          success = document.execCommand('insertUnorderedList', false);
          break;
        case 'fontSize':
          success = document.execCommand('fontSize', false, value);
          break;
        case 'foreColor':
          success = document.execCommand('foreColor', false, value);
          break;
        default:
          success = document.execCommand(command, false, value);
      }

      console.log(`Command ${command} executed:`, success);

      if (!success) {
        console.warn(`Command ${command} failed`);
      }

      // Keep focus on editor
      editorRef.current.focus();
    } catch (error) {
      console.error('execCommand error:', error);
    }
  };

  const handleSave = () => {
    if (editorRef.current) {
      onContentChange(editorRef.current.innerHTML);
    }
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSave();
    }
    if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const colors = [
    '#000000',
    '#333333',
    '#666666',
    '#999999',
    '#cccccc',
    '#ffffff',
    '#ff0000',
    '#ff6600',
    '#ffcc00',
    '#33cc00',
    '#0066cc',
    '#6600cc',
    '#ff3366',
    '#ff9933',
    '#ffff33',
    '#66ff33',
    '#3366ff',
    '#9933ff',
  ];

  return (
    <div className="absolute inset-0 z-50 bg-white border-2 border-blue-500 rounded-lg shadow-lg">
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b bg-gray-50 rounded-t-lg">
        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault(); // Prevent losing focus
            handleCommand('bold');
          }}
          title="Bold (Ctrl+B)"
        >
          <Bold size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('italic');
          }}
          title="Italic (Ctrl+I)"
        >
          <Italic size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('underline');
          }}
          title="Underline (Ctrl+U)"
        >
          <Underline size={16} />
        </button>

        <div className="w-px h-4 bg-gray-300 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('justifyLeft');
          }}
          title="Align Left"
        >
          <AlignLeft size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('justifyCenter');
          }}
          title="Align Center"
        >
          <AlignCenter size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('justifyRight');
          }}
          title="Align Right"
        >
          <AlignRight size={16} />
        </button>

        <div className="w-px h-4 bg-gray-300 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('insertUnorderedList');
          }}
          title="Bullet List"
        >
          <List size={16} />
        </button>

        <div className="w-px h-4 bg-gray-300 mx-1"></div>

        <select
          className="text-xs border rounded px-1 py-0.5"
          onChange={e => {
            handleCommand('fontSize', e.target.value);
          }}
          onMouseDown={e => e.stopPropagation()}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>

        <div className="relative">
          <button
            className="p-1 hover:bg-gray-200 rounded"
            onMouseDown={e => {
              e.preventDefault();
              setShowColorPicker(!showColorPicker);
            }}
            title="Text Color"
          >
            <Palette size={16} />
          </button>

          {showColorPicker && (
            <div className="absolute top-8 left-0 bg-white border rounded-lg shadow-lg p-2 z-10">
              <div className="grid grid-cols-6 gap-1">
                {colors.map(color => (
                  <button
                    key={color}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onMouseDown={e => {
                      e.preventDefault();
                      handleCommand('foreColor', color);
                      setShowColorPicker(false);
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex-1"></div>

        <button
          className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
          onClick={handleSave}
          title="Save (Ctrl+Enter)"
        >
          Lưu
        </button>

        <button
          className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
          onClick={onClose}
          title="Cancel (Esc)"
        >
          Hủy
        </button>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[100px] outline-none overflow-auto"
        contentEditable
        onInput={() => {
          saveCursorPosition();
        }}
        onKeyDown={handleKeyDown}
        onBlur={saveCursorPosition}
        onMouseUp={saveCursorPosition}
        onKeyUp={saveCursorPosition}
        suppressContentEditableWarning={true}
        style={{
          maxHeight: '200px',
          fontSize: elementType === 'heading' ? '24px' : '16px',
          fontWeight: elementType === 'heading' ? 'bold' : 'normal',
        }}
      />

      {/* Help text */}
      <div className="px-3 py-1 text-xs text-gray-500 border-t bg-gray-50">
        Ctrl+Enter để lưu, Esc để hủy
      </div>
    </div>
  );
};

export default InlineEditor;
