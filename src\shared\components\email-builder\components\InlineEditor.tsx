import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Type,
  Palette,
} from 'lucide-react';

interface InlineEditorProps {
  content: string;
  onContentChange: (content: string) => void;
  onClose: () => void;
  elementType: string;
}

const InlineEditor: React.FC<InlineEditorProps> = ({
  content,
  onContentChange,
  onClose,
  elementType,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const cursorPositionRef = useRef<{ start: number; end: number } | null>(null);

  // Save cursor position
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0 && editorRef.current) {
      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(editorRef.current);
      preCaretRange.setEnd(range.startContainer, range.startOffset);
      const start = preCaretRange.toString().length;

      preCaretRange.setEnd(range.endContainer, range.endOffset);
      const end = preCaretRange.toString().length;

      cursorPositionRef.current = { start, end };
    }
  }, []);

  // Restore cursor position
  const restoreCursorPosition = useCallback(() => {
    if (cursorPositionRef.current && editorRef.current) {
      const { start, end } = cursorPositionRef.current;
      const selection = window.getSelection();
      const range = document.createRange();

      let charIndex = 0;
      let startNode: Node | null = null;
      let endNode: Node | null = null;
      let startOffset = 0;
      let endOffset = 0;

      const walker = document.createTreeWalker(editorRef.current, NodeFilter.SHOW_TEXT, null);

      let node;
      while ((node = walker.nextNode())) {
        const textLength = node.textContent?.length || 0;

        if (!startNode && charIndex + textLength >= start) {
          startNode = node;
          startOffset = start - charIndex;
        }

        if (!endNode && charIndex + textLength >= end) {
          endNode = node;
          endOffset = end - charIndex;
          break;
        }

        charIndex += textLength;
      }

      if (startNode && endNode) {
        range.setStart(startNode, startOffset);
        range.setEnd(endNode, endOffset);
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    }
  }, []);

  useEffect(() => {
    if (editorRef.current) {
      // Set initial content
      editorRef.current.innerHTML = content;
      editorRef.current.focus();

      // Set cursor to end initially
      const range = document.createRange();
      const selection = window.getSelection();
      range.selectNodeContents(editorRef.current);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }
  }, [content]);

  const handleCommand = (command: string, value?: string) => {
    saveCursorPosition();
    document.execCommand(command, false, value);
    // Small delay to allow command to execute
    setTimeout(() => {
      restoreCursorPosition();
    }, 10);
  };

  const handleSave = () => {
    if (editorRef.current) {
      onContentChange(editorRef.current.innerHTML);
    }
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSave();
    }
    if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const colors = [
    '#000000',
    '#333333',
    '#666666',
    '#999999',
    '#cccccc',
    '#ffffff',
    '#ff0000',
    '#ff6600',
    '#ffcc00',
    '#33cc00',
    '#0066cc',
    '#6600cc',
    '#ff3366',
    '#ff9933',
    '#ffff33',
    '#66ff33',
    '#3366ff',
    '#9933ff',
  ];

  return (
    <div className="absolute inset-0 z-50 bg-white border-2 border-blue-500 rounded-lg shadow-lg">
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b bg-gray-50 rounded-t-lg">
        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('bold')}
          title="Bold (Ctrl+B)"
        >
          <Bold size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('italic')}
          title="Italic (Ctrl+I)"
        >
          <Italic size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('underline')}
          title="Underline (Ctrl+U)"
        >
          <Underline size={16} />
        </button>

        <div className="w-px h-4 bg-gray-300 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('justifyLeft')}
          title="Align Left"
        >
          <AlignLeft size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('justifyCenter')}
          title="Align Center"
        >
          <AlignCenter size={16} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('justifyRight')}
          title="Align Right"
        >
          <AlignRight size={16} />
        </button>

        <div className="w-px h-4 bg-gray-300 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 rounded"
          onClick={() => handleCommand('insertUnorderedList')}
          title="Bullet List"
        >
          <List size={16} />
        </button>

        <div className="w-px h-4 bg-gray-300 mx-1"></div>

        <select
          className="text-xs border rounded px-1 py-0.5"
          onChange={e => handleCommand('fontSize', e.target.value)}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>

        <div className="relative">
          <button
            className="p-1 hover:bg-gray-200 rounded"
            onClick={() => setShowColorPicker(!showColorPicker)}
            title="Text Color"
          >
            <Palette size={16} />
          </button>

          {showColorPicker && (
            <div className="absolute top-8 left-0 bg-white border rounded-lg shadow-lg p-2 z-10">
              <div className="grid grid-cols-6 gap-1">
                {colors.map(color => (
                  <button
                    key={color}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => {
                      handleCommand('foreColor', color);
                      setShowColorPicker(false);
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex-1"></div>

        <button
          className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
          onClick={handleSave}
          title="Save (Ctrl+Enter)"
        >
          Lưu
        </button>

        <button
          className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
          onClick={onClose}
          title="Cancel (Esc)"
        >
          Hủy
        </button>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[100px] outline-none overflow-auto"
        contentEditable
        onInput={e => {
          saveCursorPosition();
        }}
        onKeyDown={handleKeyDown}
        onBlur={saveCursorPosition}
        onMouseUp={saveCursorPosition}
        onKeyUp={saveCursorPosition}
        suppressContentEditableWarning={true}
        style={{
          maxHeight: '200px',
          fontSize: elementType === 'heading' ? '24px' : '16px',
          fontWeight: elementType === 'heading' ? 'bold' : 'normal',
        }}
      />

      {/* Help text */}
      <div className="px-3 py-1 text-xs text-gray-500 border-t bg-gray-50">
        Ctrl+Enter để lưu, Esc để hủy
      </div>
    </div>
  );
};

export default InlineEditor;
