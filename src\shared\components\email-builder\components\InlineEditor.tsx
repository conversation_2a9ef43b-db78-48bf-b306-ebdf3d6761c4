import React, { useRef, useEffect } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
} from 'lucide-react';

interface InlineEditorProps {
  content: string;
  onSave: (content: string) => void;
  onCancel: () => void;
  className?: string;
}

const InlineEditor: React.FC<InlineEditorProps> = ({
  content,
  onSave,
  onCancel,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (editorRef.current) {
      // Set initial content
      editorRef.current.innerHTML = content;
      
      // Focus and position cursor at the end
      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.focus();
          
          // Position cursor at the end
          const range = document.createRange();
          const selection = window.getSelection();
          range.selectNodeContents(editorRef.current);
          range.collapse(false);
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }, 100);
    }

    // <PERSON>le click outside to close editor
    const handleClickOutside = (event: MouseEvent) => {
      if (editorRef.current && !editorRef.current.contains(event.target as Node)) {
        handleSave();
      }
    };

    // Handle Escape key to cancel
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCancel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [content]);

  const handleSave = () => {
    if (editorRef.current) {
      onSave(editorRef.current.innerHTML);
    }
  };

  const handleCommand = (command: string, value?: string) => {
    if (!editorRef.current) return;

    editorRef.current.focus();

    // Ensure we have a selection
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      const range = document.createRange();
      range.selectNodeContents(editorRef.current);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    try {
      document.execCommand(command, false, value);
      editorRef.current.focus();
    } catch (error) {
      console.warn('Command failed:', command, error);
    }
  };

  return (
    <div className={`inline-editor bg-white dark:bg-gray-800 border border-blue-500 rounded-lg shadow-lg ${className}`}>
      {/* Compact Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('bold');
          }}
          title="Bold"
        >
          <Bold size={14} />
        </button>
        
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('italic');
          }}
          title="Italic"
        >
          <Italic size={14} />
        </button>
        
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('underline');
          }}
          title="Underline"
        >
          <Underline size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('justifyLeft');
          }}
          title="Align Left"
        >
          <AlignLeft size={14} />
        </button>
        
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('justifyCenter');
          }}
          title="Align Center"
        >
          <AlignCenter size={14} />
        </button>
        
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('justifyRight');
          }}
          title="Align Right"
        >
          <AlignRight size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={(e) => {
            e.preventDefault();
            handleCommand('insertUnorderedList');
          }}
          title="Bullet List"
        >
          <List size={14} />
        </button>

        <select
          className="text-xs border-0 bg-transparent text-gray-700 dark:text-gray-300 ml-2"
          onChange={(e) => handleCommand('fontSize', e.target.value)}
          onMouseDown={(e) => e.stopPropagation()}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[60px] outline-none text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
        contentEditable
        suppressContentEditableWarning={true}
        style={{ 
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
      />
    </div>
  );
};

export default InlineEditor;
