import React, { useRef, useEffect, useCallback } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
} from 'lucide-react';

interface InlineEditorProps {
  content: string;
  onSave: (content: string) => void;
  onCancel: () => void;
  className?: string;
}

const InlineEditor: React.FC<InlineEditorProps> = ({
  content,
  onSave,
  onCancel,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);

  const handleSave = useCallback(() => {
    if (editorRef.current) {
      onSave(editorRef.current.innerHTML);
    }
  }, [onSave]);

  useEffect(() => {
    if (editorRef.current) {
      // Set initial content
      editorRef.current.innerHTML = content;

      // Ensure contentEditable is set
      editorRef.current.contentEditable = 'true';

      // Enable design mode for better execCommand support
      try {
        if (editorRef.current.ownerDocument) {
          editorRef.current.ownerDocument.designMode = 'on';
          setTimeout(() => {
            if (editorRef.current?.ownerDocument) {
              editorRef.current.ownerDocument.designMode = 'off';
            }
          }, 100);
        }
      } catch (e) {
        console.warn('Could not set design mode:', e);
      }

      // Focus and position cursor at the end
      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.focus();

          // Position cursor at the end
          const range = document.createRange();
          const selection = window.getSelection();
          range.selectNodeContents(editorRef.current);
          range.collapse(false);
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }, 150);
    }

    // Handle click outside to close editor
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        // Don't close if clicking on toolbar or related elements
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.inline-editor') ||
            element.closest('button') ||
            element.closest('select') ||
            element.closest('option') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'SELECT');

        if (!isToolbarClick) {
          handleSave();
        }
      }
    };

    // Handle keyboard shortcuts
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCancel();
        return;
      }

      // Handle common formatting shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'b':
            event.preventDefault();
            handleCommand('bold');
            break;
          case 'i':
            event.preventDefault();
            handleCommand('italic');
            break;
          case 'u':
            event.preventDefault();
            handleCommand('underline');
            break;
          case 's':
            event.preventDefault();
            handleSave();
            break;
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [content, onSave, onCancel, handleSave]);

  const handleCommand = (command: string, value?: string) => {
    if (!editorRef.current) return;

    console.log('Executing command:', command, 'with value:', value);

    // Focus editor and ensure it's ready
    editorRef.current.focus();

    // Wait a bit for focus to be set properly
    setTimeout(() => {
      if (!editorRef.current) return;

      try {
        let success = false;

        // Simple, direct approach for most commands
        switch (command) {
          case 'bold':
            success = document.execCommand('bold', false);
            break;
          case 'italic':
            success = document.execCommand('italic', false);
            break;
          case 'underline':
            success = document.execCommand('underline', false);
            break;
          case 'justifyLeft':
            success = document.execCommand('justifyLeft', false);
            break;
          case 'justifyCenter':
            success = document.execCommand('justifyCenter', false);
            break;
          case 'justifyRight':
            success = document.execCommand('justifyRight', false);
            break;
          case 'insertUnorderedList':
            success = document.execCommand('insertUnorderedList', false);
            break;
          case 'fontSize':
            // Use simple fontSize command
            success = document.execCommand('fontSize', false, value || '3');
            break;
          case 'foreColor':
            success = document.execCommand('foreColor', false, value);
            break;
          default:
            success = document.execCommand(command, false, value);
        }

        console.log(`Command ${command} executed:`, success);

        if (!success) {
          console.warn(`Command ${command} failed`);
        }

        // Keep focus on editor
        editorRef.current.focus();
      } catch (error) {
        console.error('execCommand error:', error);
      }
    }, 50); // Small delay to ensure focus is set
  };

  return (
    <div
      className={`inline-editor bg-white dark:bg-gray-800 border border-blue-500 rounded-lg shadow-lg ${className}`}
    >
      {/* Compact Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('bold');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Bold (Ctrl+B)"
        >
          <Bold size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('italic');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Italic (Ctrl+I)"
        >
          <Italic size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('underline');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Underline (Ctrl+U)"
        >
          <Underline size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyLeft');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Left"
        >
          <AlignLeft size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyCenter');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Center"
        >
          <AlignCenter size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyRight');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Right"
        >
          <AlignRight size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('insertUnorderedList');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Bullet List"
        >
          <List size={14} />
        </button>

        <select
          className="text-xs border-0 bg-transparent text-gray-700 dark:text-gray-300 ml-2"
          onChange={e => {
            e.stopPropagation();
            handleCommand('fontSize', e.target.value);
          }}
          onMouseDown={e => e.stopPropagation()}
          onClick={e => e.stopPropagation()}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[60px] outline-none text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
        contentEditable={true}
        suppressContentEditableWarning={true}
        spellCheck={false}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
        }}
        onInput={() => {
          // Optional: real-time content sync
        }}
        onKeyDown={e => {
          // Handle keyboard shortcuts in editor
          if (e.key === 'Escape') {
            onCancel();
            return;
          }

          if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
              case 'b':
                e.preventDefault();
                handleCommand('bold');
                break;
              case 'i':
                e.preventDefault();
                handleCommand('italic');
                break;
              case 'u':
                e.preventDefault();
                handleCommand('underline');
                break;
              case 's':
                e.preventDefault();
                handleSave();
                break;
            }
          }
        }}
      />
    </div>
  );
};

export default InlineEditor;
