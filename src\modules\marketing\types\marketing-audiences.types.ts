import { QueryDto } from '@/shared/dto/request/query.dto';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Enum cho platform
 */
export enum MarketingPlatform {
  ZALO = 'ZALO',
  ZALO_PERSONAL = 'ZALO_PERSONAL',
  FACEBOOK = 'FACEBOOK',
  WEB = 'WEB',
}

/**
 * Interface cho custom field
 */
export interface AudienceCustomField {
  key: string;
  value: unknown;
}

/**
 * Interface cho tag
 */
export interface AudienceTag {
  id: string;
  name: string;
  color?: string;
}

/**
 * Interface cho marketing audience
 */
export interface MarketingAudience {
  id: string;
  name: string;
  email: string | null;
  countryCode: string | null;
  phoneNumber: string | null;
  avatar: string | null;
  customFields: AudienceCustomField[];
  tags: AudienceTag[];
  createdAt: string;
  updatedAt: string;
}

// DTO Interfaces

/**
 * DTO cho lấy danh sách marketing audiences
 */
export interface GetMarketingAudiencesDto extends QueryDto {
  platform?: MarketingPlatform;
  integrationId?: string;
}

/**
 * Custom response structure for marketing audiences API
 */
export interface MarketingAudiencesApiResponse {
  data: MarketingAudience[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
  };
}

/**
 * Response type cho lấy danh sách marketing audiences
 */
export type GetMarketingAudiencesResponse = ApiResponseDto<MarketingAudiencesApiResponse>;
