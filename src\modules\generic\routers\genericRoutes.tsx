/**
 * Generic Module Routes
 * Routes cho trang generic với WebSocket integration
 */

import React, { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

// Lazy load components
const GenericPage = React.lazy(() => import('../pages/GenericPage'));

/**
 * Generic module routes
 */
const genericRoutes: RouteObject[] = [
  {
    path: '/generic',
    element: (
      <MainLayout title="Generic Page">
        <Suspense fallback={<Loading />}>
          <GenericPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default genericRoutes;
