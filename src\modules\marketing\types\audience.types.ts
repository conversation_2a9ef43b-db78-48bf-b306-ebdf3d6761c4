/**
 * Types for audience API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Audience status enum
 */
export enum AudienceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Audience type enum
 */
export enum AudienceType {
  CUSTOMER = 'customer',
  LEAD = 'lead',
  SUBSCRIBER = 'subscriber',
  CUSTOM = 'custom',
}

/**
 * Audience attribute
 */
export interface AudienceAttribute {
  id: string;
  name: string;
  value: string;
}

/**
 * Custom field từ API response
 */
export interface AudienceCustomField {
  id: string;
  audienceId: number;
  fieldId: string;
  fieldName: string;
  fieldType: string;
  fieldValue: unknown;
  configJson: unknown;
  createdAt: string;
  updatedAt: string;
}

/**
 * Audience entity
 */
export interface Audience {
  id: number;
  userId?: number;
  name: string;
  email?: string;
  phoneNumber?: string;
  countryCode?: number;
  address?: string; // Thêm trường address
  avatar?: string;
  zaloSocialId?: string;
  integrationId?: string;
  avatarsExternal?: string[];
  importResource?: string;
  platform?: string; // Nền tảng (ZALO, ZALO_PERSONAL, FACEBOOK, WEB, etc.)
  zaloOfficialAccountId?: string;
  zaloUserIsFollower?: boolean;
  userLastInteractionDate?: string;
  tagIds?: number[];
  tags?: Array<{ id: string; name: string; color?: string; }>;
  type?: AudienceType;
  status?: AudienceStatus;
  totalContacts?: number;
  attributes?: AudienceAttribute[];
  customFields?: AudienceCustomField[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Create audience request
 */
export interface CreateAudienceRequest {
  name: string;
  email?: string;
  phoneNumber?: string;
  countryCode?: number;
  address?: string; // Thêm trường address
  tagIds?: number[];
  attributes?: Omit<AudienceAttribute, 'id'>[];
}



/**
 * Audience response
 */
export type AudienceResponse = Audience;

/**
 * Contact data structure from API (actual response)
 */
export interface ContactData {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  phoneNumber?: string;
  countryCode?: number;
  avatar?: string;
  zaloSocialId?: string;
  integrationId?: string | null;
  avatarsExternal?: string[];
  importResource?: string;
  zaloOfficialAccountId?: string | null;
  zaloUserIsFollower?: boolean;
  userLastInteractionDate?: string;
  customFields?: Array<{
    id?: string;
    audienceId?: number;
    fieldId?: string;
    fieldValue?: string | number | boolean;
    configJson?: any;
    createdAt?: string;
    updatedAt?: string;
    name?: string;
    value?: string;
  }>;
  tags?: Array<{
    id: string;
    name: string;
    color?: string;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string;
    updatedAt?: string;
  }>;
  attributes?: Array<{
    id: string;
    name: string;
    value: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Actual API response structure for audience list (returns contact data)
 */
export interface ActualAudienceListResult {
  data: ContactData[];
  meta?: {
    totalItems?: number;
    itemCount?: number;
    itemsPerPage?: number;
    totalPages?: number;
    currentPage?: number;
  };
}

/**
 * Audience list response (actual API structure)
 */
export type AudienceListResponse = ApiResponseDto<ActualAudienceListResult>;

/**
 * Audience detail response
 */
export type AudienceDetailResponse = ApiResponseDto<AudienceResponse>;

/**
 * DTO cho việc tạo trường tùy chỉnh
 * Chỉ chứa ID tham chiếu đến định nghĩa trường và giá trị thực tế
 */
export interface CreateCustomFieldDto {
  /**
   * ID tham chiếu đến định nghĩa trường tùy chỉnh
   */
  fieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   */
  fieldValue: unknown;
}

/**
 * DTO cho việc cập nhật hàng loạt các trường tùy chỉnh của audience
 * Sẽ thay thế hoàn toàn các giá trị hiện có
 */
export interface BulkUpdateCustomFieldsDto {
  /**
   * Danh sách các trường tùy chỉnh mới
   */
  fields: CreateCustomFieldDto[];
}

/**
 * Enum cho các loại operation trong batch update
 */
export enum CustomFieldOperation {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * Interface cho operation trong batch update custom fields
 */
export interface CustomFieldBatchOperation {
  /**
   * Loại operation: add, update, delete
   */
  operation: CustomFieldOperation;

  /**
   * ID của field definition (dùng cho operation add)
   */
  fieldId?: number;

  /**
   * ID của custom field instance hiện có (dùng cho operation update/delete)
   */
  customFieldId?: number;

  /**
   * Giá trị mới của field (dùng cho operation add/update)
   */
  fieldValue?: unknown;
}

/**
 * DTO cho batch update custom fields với operations
 */
export interface BatchUpdateCustomFieldsDto {
  /**
   * Danh sách các operations cần thực hiện
   */
  operations: CustomFieldBatchOperation[];
}

/**
 * Audience query params
 */
export interface AudienceQueryParams {
  search?: string | undefined;
  status?: AudienceStatus;
  type?: AudienceType;
  page?: number;
  limit?: number;
  sortBy?: string | undefined;
  sortDirection?: string | undefined;
  hasPhoneNumber?: boolean;
  hasEmail?: boolean;
  segmentIds?: number[];
}
