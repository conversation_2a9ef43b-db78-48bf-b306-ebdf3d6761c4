import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import TagForm from '../components/forms/TagForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { Tag, CreateTagRequest, UpdateTagRequest, TagQueryParams } from '../types/tag.types';
import { useTags, useCreateTag, useUpdateTagDynamic, useTag, useDeleteMultipleTags } from '../hooks';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Tag Management Page using optimized hooks
 * Trang quản lý tag sử dụng các hooks tối ưu
 */
const TagManagementPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success, error: showError } = useSmartNotification();

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho form sửa tag
  const [editingTagId, setEditingTagId] = useState<number | null>(null);

  // Sử dụng hooks từ API (không phụ thuộc vào dataTable)
  const createTagMutation = useCreateTag();
  const updateTagMutation = useUpdateTagDynamic();
  const { mutateAsync: deleteMultipleTags } = useDeleteMultipleTags();

  // Hooks cho form sửa tag - sử dụng conditional logic
  const { data: editingTag } = useTag(editingTagId || 0, {
    enabled: !!editingTagId && editingTagId > 0,
  });

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: number | string) => {
    const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
    if (!isNaN(numericId)) {
      setEditingTagId(numericId);
      showEditForm();
    }
  }, [showEditForm]);



  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('marketing:tags.selectToDelete', 'Vui lòng chọn tag để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều tags cùng lúc
      await deleteMultipleTags(selectedRowKeys as number[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:tags.bulkDeleteSuccess', 'Xóa {{count}} tag thành công', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('marketing:tags.bulkDeleteError', 'Xóa nhiều tag thất bại'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleTags, t]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<Tag>[]>(
    () => [
      { key: 'id', title: t('common:id', 'ID'), dataIndex: 'id', width: '10%', sortable: true },
      { key: 'name', title: t('marketing:tag.name', 'Tên tag'), dataIndex: 'name', width: '30%', sortable: true },
      {
        key: 'color',
        title: t('marketing:tag.color', 'Màu sắc'),
        dataIndex: 'color',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const color = value as string;
          return color ? (
            <div className="flex items-center space-x-2">
              <div
                className="w-6 h-6 rounded"
                style={{ backgroundColor: color }}
              />
            </div>
          ) : (
            <span className="text-gray-400">{t('common:noData', '-')}</span>
          );
        },
      },
      {
        key: 'audienceCount',
        title: t('marketing:tag.objectCount', 'Số lượng đối tượng'),
        dataIndex: 'audienceCount',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const count = value as number;
          return (
            <div className="flex items-center text-center">
              <span className="text-sm text-center font-medium text-gray-900 dark:text-gray-100">
                {typeof count === 'number' ? count.toLocaleString() : t('common:zero', '0')}
              </span>
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: Tag) => {
          const actionItems = [
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
              tooltip: t('common:edit')
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
            />
          );
        },
      },
    ],
    [t, handleEdit]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): TagQueryParams => {
    const queryParams: TagQueryParams = {
      page: params.page,
      limit: params.pageSize,
      ...(params.searchTerm && { search: params.searchTerm }),
      ...(params.sortBy && { sortBy: params.sortBy }),
      ...(params.sortDirection && { sortDirection: params.sortDirection }),
    };

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<Tag, TagQueryParams>({
      columns,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách tags với queryParams từ dataTable
  const { data: tagData, isLoading, refetch } = useTags(dataTable.queryParams);

  // Handler cho reload data
  const handleReload = useCallback(async () => {
    try {
      await refetch();
      success({ message: t('common:reloadSuccess', 'Tải lại dữ liệu thành công') });
    } catch (error) {
      showError({ message: t('common:reloadError', 'Lỗi khi tải lại dữ liệu') });
    }
  }, [refetch, success, showError, t]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateTagRequest
    const tagData: CreateTagRequest = {
      name: values['name'] as string,
    };

    if (values['color']) {
      tagData.color = values['color'] as string;
    }

    createTagMutation.mutate(tagData);
    hideAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  // Xử lý submit form sửa
  const handleEditSubmit = async (values: Record<string, unknown>) => {
    if (!editingTagId) return;

    try {
      // Chuyển đổi values thành UpdateTagRequest
      const tagData: UpdateTagRequest = {
        name: values['name'] as string,
      };

      if (values['color']) {
        tagData.color = values['color'] as string;
      }

      await updateTagMutation.mutateAsync({ id: editingTagId, data: tagData });

      NotificationUtil.success({
        message: t('marketing:tags.updateSuccess', 'Cập nhật tag thành công'),
        duration: 3000,
      });

      hideEditForm();
      setEditingTagId(null);
    } catch {
      NotificationUtil.error({
        message: t('marketing:tags.updateError', 'Cập nhật tag thất bại'),
        duration: 3000,
      });
    }
  };

  // Xử lý hủy form sửa
  const handleEditCancel = () => {
    hideEditForm();
    setEditingTagId(null);
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'refresh-cw',
            tooltip: t('common:reload', 'Tải lại dữ liệu'),
            variant: 'secondary',
            onClick: handleReload,
            className: 'text-blue-500',
          },
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      {dataTable.filter.selectedValue !== undefined && (
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          dateRange={dataTable.dateRange.dateRange}
          onClearDateRange={handleClearDateRange}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />
      )}

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <TagForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      {/* Form sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {editingTag && (
          <TagForm
            mode="edit"
            initialData={editingTag}
            onSubmit={handleEditSubmit}
            onCancel={handleEditCancel}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={tagData?.result?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: tagData?.result?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: tagData?.result?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:tags.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} tag đã chọn?', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default TagManagementPage;
