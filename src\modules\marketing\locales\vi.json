{"marketing": {"title": "Marketing", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch marketing và đối tượng khách hàng", "menu": {"audience": "<PERSON><PERSON><PERSON>", "segment": "<PERSON><PERSON> đ<PERSON>n", "campaign": "<PERSON><PERSON><PERSON>", "tags": "Thẻ", "customFields": "Trường tùy chỉnh", "reports": "Báo cáo", "templateEmail": "Mẫu email", "articles": "<PERSON><PERSON><PERSON> vi<PERSON>", "videos": "Video"}, "systemEmailTemplates": {"table": {"template": "Mẫu", "type": "<PERSON><PERSON><PERSON>", "tags": "Thẻ", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}}, "featureInDevelopment": {"title": "<PERSON><PERSON><PERSON> năng đang đư<PERSON> phát triển", "description": "Chúng tôi đang nỗ lực phát triển tính năng này để mang đến trải nghiệm tốt nhất cho bạn. Vui lòng quay lại sau!", "comingSoon": "S<PERSON><PERSON> ra mắt:", "feature1": "<PERSON><PERSON><PERSON>n thân thiện và dễ sử dụng", "feature2": "<PERSON><PERSON><PERSON> hợp đ<PERSON>y đủ với hệ thống <PERSON>", "feature3": "<PERSON><PERSON><PERSON> su<PERSON>t cao và <PERSON>n định", "contact": "<PERSON>ó thắc mắc? <PERSON><PERSON>n hệ với chúng tôi qua"}, "dashboard": {"title": "Bảng điều k<PERSON>n Marketing", "description": "Tổng quan về các hoạt động marketing"}, "segment": {"title": "<PERSON><PERSON> đ<PERSON>n", "description": "<PERSON><PERSON> đoạn khách hàng theo các tiêu chí khác nhau", "totalSegments": "Tổng số phân đoạn", "manage": "<PERSON><PERSON><PERSON><PERSON> lý phân đoạn", "name": "<PERSON><PERSON><PERSON> phân đoạn", "audience": "<PERSON><PERSON><PERSON>", "audienceCount": "<PERSON><PERSON> đối t<PERSON>", "totalContacts": "<PERSON><PERSON> liên hệ", "addNew": "<PERSON>hê<PERSON> phân đoạn mới", "deleteSuccess": "Đã xóa phân đoạn thành công", "deleteError": "Có lỗi xảy ra khi xóa phân đoạn", "createSuccess": "<PERSON><PERSON> tạo phân đoạn thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo phân đoạn. <PERSON><PERSON> lòng thử lại sau.", "updateSuccess": "<PERSON><PERSON> cập nhật phân đoạn thành công", "updateError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật phân đoạn. <PERSON><PERSON> lòng thử lại sau.", "edit": "Chỉnh sửa phân đoạn", "selectSegmentsToDelete": "<PERSON><PERSON> lòng chọn ít nhất một phân đoạn để xóa", "bulkDeleteSuccess": "Đã xóa phân đoạn thành công", "bulkDeleteError": "Có lỗi xảy ra khi xóa phân đoạn", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa phân đoạn này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa phân đoạn đã chọn?", "noSegmentsSelected": "<PERSON><PERSON><PERSON><PERSON> có phân đoạn nào đ<PERSON><PERSON><PERSON> chọn", "customFields": "Trường tùy chỉnh", "selectCustomField": "<PERSON><PERSON><PERSON> trường...", "form": {"title": "Thông tin phân đoạn", "name": "<PERSON><PERSON><PERSON> phân đoạn", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên phân đoạn...", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả phân đoạn...", "conditions": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "conditionsDescription": "<PERSON><PERSON><PERSON><PERSON> lập các điều kiện để phân đoạn khách hàng", "addGroup": "<PERSON><PERSON><PERSON><PERSON> nhóm điều kiện", "addCondition": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u kiện", "removeGroup": "Xóa nhóm", "removeCondition": "<PERSON><PERSON><PERSON> đi<PERSON>u kiện", "field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator": "<PERSON><PERSON> tử", "value": "<PERSON><PERSON><PERSON> trị", "valuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị...", "and": "VÀ", "or": "HOẶC", "operators": {"equals": "Bằng", "not_equals": "Không bằng", "contains": "<PERSON><PERSON><PERSON>", "not_contains": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON>", "starts_with": "<PERSON><PERSON><PERSON> đầu bằng", "ends_with": "<PERSON><PERSON><PERSON> th<PERSON>c bằng", "greater_than": "<PERSON><PERSON><PERSON>", "less_than": "Nhỏ hơn", "greater_than_or_equal": "Lớn hơn hoặc bằng", "less_than_or_equal": "Nhỏ hơn hoặc bằng", "is_empty": "<PERSON><PERSON><PERSON><PERSON>", "is_not_empty": "<PERSON><PERSON><PERSON><PERSON> trống"}, "validation": {"nameRequired": "<PERSON>ên phân đoạn là bắt buộc", "nameMinLength": "Tên phân đoạn phải có ít nhất 2 ký tự", "nameMaxLength": "Tên phân đoạn không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "conditionsRequired": "<PERSON><PERSON><PERSON> có ít nhất một điều kiện", "fieldRequired": "<PERSON>rư<PERSON><PERSON> là b<PERSON> buộc", "operatorRequired": "<PERSON><PERSON> tử là b<PERSON> buộc", "valueRequired": "<PERSON><PERSON><PERSON> trị là b<PERSON>t buộc"}, "buttons": {"save": "<PERSON><PERSON><PERSON>", "cancel": "Đ<PERSON><PERSON>", "saveTooltip": "<PERSON><PERSON><PERSON> phân đo<PERSON>n", "cancelTooltip": "Hủy và đóng form"}, "systemField": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON> thống"}}, "tags": {"title": "<PERSON><PERSON><PERSON><PERSON> lý thẻ", "description": "Tạo và quản lý các thẻ cho khách hàng", "totalTags": "Tổng số thẻ", "manage": "<PERSON><PERSON><PERSON><PERSON> lý thẻ", "addNew": "Thêm tag mới", "edit": "<PERSON><PERSON><PERSON> tag", "form": {"name": "Tên tag", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tag", "color": "Mã màu", "colorPlaceholder": "<PERSON><PERSON><PERSON> màu cho tag", "randomColor": "<PERSON><PERSON><PERSON> ngẫu nhiên"}, "validation": {"nameRequired": "Tên tag là bắt buộc", "colorInvalid": "<PERSON>ã màu phải có định dạng HEX (ví dụ: #FF0000)"}, "deleteSuccess": "<PERSON>ó<PERSON> tag thành công", "deleteError": "<PERSON><PERSON><PERSON> tag thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t tag thành công", "updateError": "<PERSON><PERSON><PERSON> nhật tag thất bại", "selectToDelete": "<PERSON><PERSON> lòng chọn tag để xóa", "bulkDeleteSuccess": "Xóa {{count}} tag thành công", "bulkDeleteError": "<PERSON><PERSON><PERSON> nhi<PERSON>u tag thất bại", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} tag đã chọn?"}, "reports": {"title": "Báo cáo", "description": "<PERSON>em báo cáo và thống kê marketing"}, "templateEmail": {"title": "<PERSON><PERSON><PERSON><PERSON> lý mẫu email", "description": "Tạo và quản lý các mẫu email marketing"}, "articles": {"title": "<PERSON><PERSON><PERSON><PERSON> lý bài viết", "description": "Tạo và quản lý bài viết marketing"}, "videos": {"title": "<PERSON><PERSON><PERSON><PERSON> video", "description": "Tạo và quản lý video marketing"}, "email": {"title": "Email", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch email marketing"}, "sms": {"title": "SMS", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch SMS marketing"}, "googleAds": {"title": "Google Ads", "description": "<PERSON><PERSON><PERSON><PERSON> lý quảng c<PERSON>o Google", "detail": {"title": "<PERSON> tiết Google Ads"}, "accounts": {"title": "Tài khoản Google Ads", "description": "Quản lý tài khoản Google Ads"}, "campaigns": {"title": "Chiến dịch Google Ads", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch Google Ads"}, "keywords": {"title": "Từ khóa Google Ads", "description": "<PERSON><PERSON><PERSON><PERSON> lý từ khóa Google Ads"}, "ads": {"title": "Quảng cáo Google Ads", "description": "<PERSON><PERSON><PERSON>n lý quảng cáo Google Ads"}, "reports": {"title": "Báo cáo Google Ads", "description": "Báo cáo Google Ads"}, "settings": {"title": "Cài đặt Google Ads", "description": "Cài đặt Google Ads"}}, "facebookAds": {"analytics": {"title": "Phân tích Facebook Ads", "description": "<PERSON> dõi hiệu suất và tối ưu hóa chiến dịch quảng cáo", "overview": "<PERSON><PERSON><PERSON> quan hiệu su<PERSON>t", "topCampaigns": "<PERSON><PERSON><PERSON> dịch hi<PERSON>u su<PERSON>t cao", "export": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "chartPlaceholder": "<PERSON><PERSON><PERSON><PERSON> đồ hiệu su<PERSON>t", "chartDescription": "<PERSON>i<PERSON><PERSON> đồ chi tiết sẽ được hiển thị ở đây khi tích hợp với thư viện chart", "totalSpend": "Tổng chi phí", "totalSpendDesc": "So với kỳ trước", "impressions": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n thị", "impressionsDesc": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> hiển thị quảng cáo", "clicks": "<PERSON><PERSON><PERSON><PERSON>", "clicksDesc": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> nh<PERSON>p vào quảng cáo", "ctr": "Tỷ l<PERSON> nhấp (CTR)", "ctrDesc": "Clicks / Impressions", "cpc": "Chi phí/nhấp (CPC)", "cpcDesc": "Spend / Clicks", "conversions": "<PERSON><PERSON><PERSON><PERSON> đổi", "conversionsDesc": "Tổng số chuyển đổi", "roas": "ROAS", "roasDesc": "Return on Ad Spend", "reach": "<PERSON><PERSON><PERSON><PERSON> cận", "reachDesc": "<PERSON><PERSON> ng<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> ti<PERSON> cận", "periods": {"1d": "<PERSON><PERSON><PERSON> nay", "7d": "7 ngày qua", "30d": "30 ngày qua", "90d": "90 ngày qua", "custom": "<PERSON><PERSON><PERSON> chỉnh"}}, "metrics": {"spend": "Chi phí", "clicks": "<PERSON><PERSON><PERSON><PERSON>"}}, "zaloAds": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý quảng c<PERSON><PERSON>", "overview": {"title": "<PERSON><PERSON><PERSON> quan Zalo Ads"}, "accounts": {"title": "<PERSON><PERSON><PERSON><PERSON> lý tài k<PERSON><PERSON>n <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý tài k<PERSON><PERSON>n <PERSON>"}, "campaigns": {"title": "<PERSON><PERSON><PERSON><PERSON> lý chiến d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến d<PERSON><PERSON>"}, "reports": {"title": "Báo cáo Zalo Ads", "description": "Báo cáo Zalo Ads"}}, "tiktokAds": {"title": "TikTok Ads", "description": "<PERSON><PERSON><PERSON><PERSON> lý quảng c<PERSON>o TikTok", "overview": {"title": "<PERSON><PERSON>ng quan TikTok Ads"}, "accounts": {"title": "<PERSON><PERSON><PERSON> k<PERSON>n TikTok Ads", "description": "<PERSON><PERSON><PERSON>n lý tài k<PERSON>ản TikTok Ads"}, "campaigns": {"title": "<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch <PERSON>ik<PERSON>ok Ads"}, "creatives": {"title": "TikTok Ads Creatives", "description": "<PERSON><PERSON><PERSON><PERSON> lý creative TikTok Ads"}, "audiences": {"title": "TikTok Ads Audiences", "description": "Quản lý audience TikTok Ads"}, "reports": {"title": "Báo cáo TikTok Ads", "description": "Báo cáo TikTok Ads"}, "settings": {"title": "Cài đặt TikTok Ads", "description": "Cài đặt TikTok Ads"}}, "resources": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> viện mẫu và công cụ hỗ trợ"}, "zns": {"campaign": {"list": {"title": "<PERSON><PERSON> s<PERSON>ch ch<PERSON>n d<PERSON><PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON>ch <PERSON> mới", "success": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch thành công", "error": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch thất bại"}, "form": {"name": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chiến dịch", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả chiến dịch (tù<PERSON> chọn)", "integration": "Official Account", "integrationPlaceholder": "Chọn Official Account...", "template": "Template ZNS", "templatePlaceholder": "Chọn template ZNS...", "customerListType": "<PERSON><PERSON><PERSON> thức cài đặt", "customerListTypePlaceholder": "<PERSON><PERSON><PERSON> c<PERSON>ch thức...", "audience": "Chọn audience", "audiencePlaceholder": "Chọn audience...", "phoneNumbers": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "uploadFile": "Tải file Excel danh sách số điện thoại", "uploadFileDescription": "File Excel phải có cột đầu tiên chứa số điện thoại (bỏ qua dòng tiêu đề)", "uploadFileButton": "Nhấn để chọn file Excel", "uploadFileSupport": "Hỗ trợ .xlsx, .xls", "uploadSuccess": "<PERSON><PERSON> tải lên {{count}} số điện thoại", "uploadError": "Lỗi khi đọc file Excel. <PERSON><PERSON> lòng kiểm tra định dạng file.", "downloadTemplate": "Tải file mẫu", "addPhoneButton": "+ <PERSON>h<PERSON><PERSON> số", "addPhoneInstruction": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON>hê<PERSON> số\" để thêm số điện thoại", "phonePlaceholder": "0912345678", "templateDataField": "<PERSON><PERSON><PERSON> tr<PERSON>", "templateDataValue": "<PERSON><PERSON><PERSON><PERSON> giá trị...", "templateDataValuePlaceholder": "<giá_trị>", "addTemplateField": "+ Thêm trường dữ liệu", "sendingMode": "<PERSON><PERSON> độ gửi"}, "sendingMode": {"normal": "<PERSON><PERSON><PERSON> thường - <PERSON> Z<PERSON> đư<PERSON>c gửi theo cơ chế thông thường", "overLimit": "<PERSON><PERSON><PERSON> vư<PERSON><PERSON> hạn mức - <PERSON><PERSON> chế cho phép OA gửi tin ZNS tag 3 v<PERSON><PERSON><PERSON> hạn mức"}, "customerList": {"audience": "<PERSON><PERSON><PERSON> từ danh sách audience", "upload": "Tải file Excel", "manual": "<PERSON><PERSON><PERSON><PERSON> thủ công"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch là b<PERSON> bu<PERSON>c", "nameMaxLength": "<PERSON><PERSON><PERSON> chiến dịch không đ<PERSON><PERSON><PERSON> quá 255 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đư<PERSON><PERSON> quá 1000 ký tự", "integrationRequired": "<PERSON><PERSON> lòng chọn tài k<PERSON>n Zalo OA", "templateRequired": "<PERSON>ui lòng chọn template ZNS", "customerListTypeRequired": "<PERSON><PERSON> lòng chọn cách cài đặt danh sách khách hàng", "phoneInvalid": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "sendingModeRequired": "<PERSON><PERSON> lòng chọn chế độ gửi", "scheduleTypeRequired": "<PERSON><PERSON> lòng chọn lo<PERSON>i lập lịch", "scheduledTimeInvalid": "<PERSON>hời gian lập lịch phải lớn hơn thời gian hiện tại"}, "templateData": {"fallback": {"customerName": "<PERSON><PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o từ hệ thống"}}, "table": {"name": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "template": "Template", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "audience": "<PERSON><PERSON><PERSON>", "metrics": "<PERSON><PERSON><PERSON><PERSON> kê", "createdAt": "<PERSON><PERSON><PERSON>", "performance": "<PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON> gian lên l<PERSON>ch"}, "actions": {"start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "pause": "<PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON><PERSON><PERSON>", "stop": "Dừng", "duplicate": "<PERSON><PERSON><PERSON> b<PERSON>n"}, "status": {"draft": "Nháp", "scheduled": "<PERSON><PERSON> lên l<PERSON>ch", "running": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "delete": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>", "message": "<PERSON>ạn có chắc chắn muốn xóa chiến dịch \"{{name}}\"?"}}}, "customFields": {"title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa để tìm trường tùy chỉnh...", "noFields": "<PERSON><PERSON><PERSON> có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường."}, "common": {"moduleTitle": "Marketing", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "draft": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> mới", "edit": "Chỉnh sửa", "delete": "Xóa", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "type": "<PERSON><PERSON><PERSON>", "id": "ID", "followers": "Followers", "manage": "<PERSON><PERSON><PERSON><PERSON> lý"}, "audience": {"title": "<PERSON><PERSON><PERSON><PERSON> lý đối tư<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách khách hàng tiềm năng và khách hàng hiện tại", "totalContacts": "<PERSON><PERSON><PERSON> số liên hệ", "manage": "<PERSON><PERSON><PERSON><PERSON> lý đối tư<PERSON>", "name": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON>", "email": "Email", "channel": "<PERSON><PERSON><PERSON> t<PERSON>", "platform": {"zalo": "<PERSON><PERSON>", "zaloPersonal": "Zalo <PERSON> nhân", "facebook": "Facebook", "email": "Email", "phone": "Phone", "web": "Web"}, "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "type": "<PERSON><PERSON><PERSON> đối t<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "tags": "Thẻ", "customFields": "Trường tùy chỉnh", "generalInfo": "Th<PERSON>ng tin chung", "detailForm": "<PERSON> tiết đối tượng", "loadError": "Lỗi khi tải thông tin đối tượng", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đối tượng", "notFoundDescription": "<PERSON><PERSON><PERSON> tượng này có thể đã bị xóa hoặc không tồn tại", "createSuccess": "<PERSON><PERSON><PERSON> đối tượng thành công", "createError": "<PERSON><PERSON><PERSON> đối tượng thất bại", "form": {"addTitle": "<PERSON><PERSON><PERSON><PERSON> đối tư<PERSON> mới", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đối tư<PERSON>", "emailPlaceholder": "Nhập email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "tagsPlaceholder": "<PERSON><PERSON><PERSON> tags..."}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tượ<PERSON> là bắt buộc", "emailInvalid": "<PERSON><PERSON> h<PERSON> l<PERSON>"}, "edit": {"title": "Chỉnh sửa", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON> thành công", "successMessage": "Thông tin đối tượng đã đư<PERSON><PERSON> cập nhật", "error": "Lỗi lưu dữ liệu", "errorMessage": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin đối tượng"}, "avatar": {"clickToChange": "<PERSON><PERSON><PERSON><PERSON> để thay đổi avatar", "invalidFileType": "<PERSON>ui lòng chọn file hình <PERSON>nh", "fileTooLarge": "File quá lớn. <PERSON><PERSON> lòng chọn file nhỏ hơn 5MB", "uploadSuccess": "Avatar đã đ<PERSON><PERSON><PERSON> tải lên thành công", "uploadError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar"}, "interactionHistory": {"title": "<PERSON><PERSON><PERSON> sử tương tác & Tr<PERSON><PERSON> thái", "email": "Email", "zalo": "<PERSON><PERSON>", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "lastSent": "<PERSON><PERSON><PERSON> cu<PERSON>i", "lastMessage": "<PERSON> n<PERSON>n cuối", "lastCall": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> cuối", "openRate": "Tỷ lệ mở", "clickRate": "Tỷ lệ click", "responseRate": "Tỷ lệ phản hồi", "deliveryRate": "Tỷ lệ g<PERSON>i thành công", "callDuration": "<PERSON>hờ<PERSON> l<PERSON>i", "smsCount": "Số SMS đã gửi", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON> k<PERSON>", "verified": "<PERSON><PERSON> xác thực"}}, "socialInfo": {"title": "Thông tin Social", "profileUrl": "Profile URL", "lastActivity": "<PERSON><PERSON><PERSON> động cu<PERSON>i", "username": "Username", "followers": "Followers", "connections": "Connections", "handle": "<PERSON><PERSON>", "status": {"connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>"}}}, "zaloArticle": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "image": "<PERSON><PERSON><PERSON>", "createDate": "<PERSON><PERSON><PERSON>", "updateDate": "<PERSON><PERSON><PERSON> c<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusShow": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "statusHide": "Ẩn", "statusFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "selectOA": "Chọn Official Account để tạo bài viết", "selectOAPlaceholder": "Chọn Official Account...", "selectOARequired": "<PERSON><PERSON> lòng ch<PERSON>n Official Account", "stats": "<PERSON><PERSON><PERSON><PERSON> kê", "views": "<PERSON><PERSON><PERSON><PERSON> xem", "likes": "<PERSON><PERSON><PERSON><PERSON>", "shares": "chia sẻ", "view": "Xem", "selectOfficialAccount": "Chọn Official Account", "selectOfficialAccountPlaceholder": "Chọn Official Account...", "filterByType": "<PERSON><PERSON><PERSON> the<PERSON>", "selectTypePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON>i bài viết...", "deleteConfirm": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON>a bài viết", "deleteDescription": "Bạn có chắc chắn muốn xóa bài viết này? Hành động này không thể hoàn tác.", "type": {"normal": "<PERSON><PERSON><PERSON> vi<PERSON>", "video": "Video"}, "form": {"editTitle": "Chỉnh sửa bài viết", "createTitle": "Soạn bài viết mới", "officialAccount": "Official Account", "selectOAError": "<PERSON><PERSON> lòng ch<PERSON>n Official Account", "coverImage": "Ảnh đại diện", "uploadCoverImage": "<PERSON><PERSON><PERSON><PERSON> để thêm ảnh đại diện", "editCoverImage": "Chỉnh sửa ảnh", "articleTitle": "Ti<PERSON><PERSON> đề bài viết", "titlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề bài viết", "author": "Tác g<PERSON>", "authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tác g<PERSON>", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả bài viết", "content": "<PERSON><PERSON><PERSON> dung bài viết", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nội dung bài viết...", "relatedArticles": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> liên quan", "addRelated": "<PERSON><PERSON><PERSON><PERSON>", "hideRelated": "Ẩn", "editRelated": "<PERSON><PERSON><PERSON>", "selectRelatedDescription": "<PERSON><PERSON><PERSON> bài viết liên quan (tối đa 5 bài):", "addRelatedPlaceholder": "<PERSON><PERSON><PERSON>n để thêm bài viết liên quan", "saveDraft": "<PERSON><PERSON><PERSON>", "schedule": "Đặt lịch", "publish": "<PERSON><PERSON><PERSON>", "uploading": "<PERSON><PERSON> tải lên...", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "preview": {"selectOAPlaceholder": "Chọn Official Account", "addTitle": "<PERSON><PERSON><PERSON>n để thêm tiêu đề", "addAuthor": "<PERSON><PERSON><PERSON><PERSON> để thêm tác giả", "addDescription": "<PERSON><PERSON><PERSON>n để thêm mô tả", "addContent": "<PERSON><PERSON><PERSON>n để thêm nội dung bài viết...", "viewsCount": "<PERSON><PERSON><PERSON><PERSON> xem", "moreArticles": "b<PERSON><PERSON> vi<PERSON><PERSON>"}, "scheduler": {"title": "Đặt lịch xu<PERSON> bản", "description": "<PERSON><PERSON><PERSON> thời gian để tự động xuất bản bài viết", "selectDateTime": "<PERSON><PERSON><PERSON> ngày và giờ", "selectScheduleTime": "<PERSON><PERSON> lòng chọn thời gian đặt lịch", "confirmSchedule": "<PERSON><PERSON><PERSON> n<PERSON>n đặt lịch"}, "notifications": {"imageUploadSuccess": "<PERSON><PERSON><PERSON>nh lên thành công", "imageUploadError": "<PERSON><PERSON> lỗi xảy ra khi tải ảnh lên", "createSuccess": "<PERSON><PERSON><PERSON> bài viết thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo bài viết", "updateSuccess": "<PERSON><PERSON><PERSON> nhật bài viết thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật bài viết"}}, "zaloVideo": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "published": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "draft": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON> lại", "retrySuccess": "<PERSON><PERSON><PERSON> lại video thành công", "retryError": "<PERSON><PERSON> lỗi xảy ra khi đăng lại video"}, "type": {"customer": "<PERSON><PERSON><PERSON><PERSON>", "lead": "<PERSON><PERSON><PERSON><PERSON> hàng tiềm năng", "subscriber": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>ng ký", "custom": "<PERSON><PERSON><PERSON> chỉnh"}, "detail": {"overview": "<PERSON><PERSON><PERSON> quan", "totalContacts": "<PERSON><PERSON><PERSON> số liên hệ", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "overview": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "tags": "Thẻ", "timeInfo": "<PERSON>h<PERSON>ng tin thời gian", "customFields": "Trường tùy chỉnh"}, "mediaResources": {"title": "T<PERSON>i <PERSON>n <PERSON>", "description": "Quản lý file media cho Zalo OA", "table": {"preview": "<PERSON><PERSON>", "filename": "Tên file", "type": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "mimeType": "MIME Type", "description": "<PERSON><PERSON>", "uploadedAt": "Ngày upload"}, "type": {"image": "Ảnh", "gif": "GIF", "file": "File"}, "filter": {"type": "<PERSON><PERSON><PERSON> the<PERSON>"}, "form": {"title": "<PERSON><PERSON>ê<PERSON> tài nguyên <PERSON>", "officialAccount": {"label": "Official Account", "placeholder": "Chọn Official Account"}, "file": {"label": "File Upload", "placeholder": "<PERSON>éo thả hoặc click để tải lên file", "supportedTypes": "Loại file hỗ trợ:", "imageTypes": "Ảnh: JPG, PNG (tối đa 1MB)", "documentTypes": "Tài liệu: PDF, DOC, DOCX, CSV (tối đa 10MB)", "gifTypes": "GIF: GIF (tối đa 5MB)"}, "description": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho file (tù<PERSON> chọn)"}, "submit": "Upload File"}, "validation": {"oaRequired": "<PERSON><PERSON> lòng ch<PERSON>n Official Account", "fileRequired": "<PERSON><PERSON> lòng chọn file để upload", "imageSizeExceeded": "Ảnh không đư<PERSON><PERSON> vư<PERSON><PERSON> quá 1MB", "documentSizeExceeded": "<PERSON><PERSON><PERSON> liệu không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 10MB", "gifSizeExceeded": "GIF không đư<PERSON><PERSON> vư<PERSON>t quá 5MB", "unsupportedFileType": "Loại file không được hỗ trợ"}, "success": {"uploadSuccess": "Upload file thành công!"}, "error": {"uploadFailed": "Upload file thất bại. <PERSON>ui lòng thử lại."}}, "customField": {"configId": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của hệ thống", "add": "Thêm trường tùy chỉnh", "edit": "Chỉnh sửa trường tùy chỉnh", "dataType": "<PERSON><PERSON><PERSON> dữ liệu", "dataTypes": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Đúng/Sai", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>"}, "createSuccess": "Tạo trường tùy chỉnh thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công", "deleteSuccess": "<PERSON>óa trường tùy chỉnh thành công", "deleteMultipleSuccess": "<PERSON><PERSON> xóa {{count}} trường tùy chỉnh", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa trường tùy chỉnh này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?", "errors": {"fetchError": "Lỗi khi tải danh sách trường tùy chỉnh", "fetchDetailError": "Lỗi khi tải chi tiết trường tùy chỉnh", "createError": "Lỗi khi tạo trường tùy chỉnh", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "deleteMultipleError": "Lỗi khi xóa nhiều trường tùy chỉnh", "fetchConfigExamplesError": "Lỗi khi tải ví dụ cấu hình"}, "addForm": "Thêm trường tùy chỉnh mới", "editForm": "Chỉnh sửa trường tùy chỉnh", "component": "<PERSON><PERSON><PERSON> thành phần", "components": {"input": "Ô nhập liệu", "textarea": "Ô văn bản", "select": "<PERSON><PERSON>n", "checkbox": "<PERSON><PERSON><PERSON>", "radio": "Nút radio", "date": "<PERSON><PERSON><PERSON>", "number": "Số", "file": "<PERSON><PERSON>p tin", "multiSelect": "<PERSON><PERSON><PERSON>"}, "type": "<PERSON><PERSON><PERSON> dữ liệu", "type.string": "<PERSON><PERSON><PERSON>", "type.number": "Số", "type.boolean": "Có/<PERSON>hông", "type.date": "<PERSON><PERSON><PERSON>", "type.object": "<PERSON><PERSON><PERSON>", "type.array": "<PERSON><PERSON><PERSON>", "types": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Có/<PERSON>hông", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON><PERSON>", "string": "<PERSON><PERSON><PERSON>"}, "name": "<PERSON><PERSON><PERSON> tr<PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "placeholder": "Placeholder", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "options": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "validation": {"minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa"}, "form": {"fieldKeyLabel": "<PERSON>r<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "componentRequired": "<PERSON><PERSON> lòng chọn loại thành phần", "labelRequired": "<PERSON><PERSON> lòng nh<PERSON><PERSON> nh<PERSON>n", "typeRequired": "<PERSON><PERSON> lòng chọn kiểu dữ liệu", "idRequired": "<PERSON><PERSON> lòng nhập tên trường định danh", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n hiển thị", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho trường này", "placeholderPlaceholder": "<PERSON>h<PERSON>p placeholder", "defaultValuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị mặc định", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> các tù<PERSON>, ph<PERSON> cách bằng dấu phẩy hoặc định dạng JSON", "selectOptionsPlaceholder": "Nhập giá trị theo cấu trúc: Tên hiển thị:<PERSON><PERSON><PERSON> trị, Mỗi cặp giá trị trên 1 dòng. VD:\nNam:male\n<PERSON><PERSON>:female\n<PERSON>hác:other", "booleanDefaultPlaceholder": "<PERSON><PERSON><PERSON> giá trị mặc định", "dateDefaultPlaceholder": "<PERSON><PERSON><PERSON> ngày mặc định", "description": "<PERSON><PERSON>", "labelTagRequired": "<PERSON><PERSON> lòng thêm ít nhất một nhãn", "fieldKey": "<PERSON><PERSON>ó<PERSON> trường", "fieldKeyPlaceholder": "full_name", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNamePlaceholder": "Họ và tên", "tags": "Thẻ", "tagsPlaceholder": "personal, required, contact", "fieldIdLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> hiển thị", "displayNameRequired": "<PERSON><PERSON> lòng nhập tên trường hiển thị", "labelInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter", "tagsCount": "nh<PERSON>n đã thêm", "patternSuggestions": "Gợi ý pattern phổ biến:", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "minValue": "<PERSON><PERSON><PERSON> trị tối thiểu", "maxValue": "<PERSON><PERSON><PERSON> trị tối đa", "pattern": "Mẫu kiểm tra", "options": "<PERSON><PERSON><PERSON>", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa", "placeholder": "Placeholder", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "labels": "<PERSON><PERSON>ã<PERSON>", "showAdvancedSettings": "<PERSON><PERSON>n thị cài đặt nâng cao", "validation": {"fieldKeyRequired": "Khóa trường là bắt buộc", "fieldKeyPattern": "<PERSON><PERSON>óa trường chỉ được chứa chữ cái, số, gạch dưới và gạch ngang", "displayNameRequired": "<PERSON><PERSON><PERSON> hiển thị là bắt buộc", "dataTypeRequired": "Loại dữ liệu là bắt buộc"}}, "createError": "Lỗi khi tạo trường tùy chỉnh", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "loadError": "Lỗi khi tải trường tùy chỉnh", "booleanValues": {"true": "<PERSON><PERSON>", "false": "K<PERSON>ô<PERSON>"}, "patterns": {"email": "Email", "phoneVN": "Số điện thoại VN", "phoneIntl": "<PERSON><PERSON> điện thoại quốc tế", "postalCodeVN": "<PERSON><PERSON> b<PERSON><PERSON> ch<PERSON> VN", "lettersOnly": "Chỉ chữ cái", "numbersOnly": "Chỉ số", "alphanumeric": "Chữ và số", "noSpecialChars": "<PERSON><PERSON><PERSON><PERSON> có ký tự đặc biệt", "url": "URL", "ipv4": "IPv4", "strongPassword": "<PERSON><PERSON><PERSON><PERSON> mạnh", "vietnameseName": "<PERSON><PERSON><PERSON> (c<PERSON>)", "studentId": "<PERSON><PERSON> sinh viên", "nationalId": "CMND/CCCD", "taxCode": "<PERSON><PERSON> số thuế", "dateFormat": "<PERSON>ày (dd/mm/yyyy)", "timeFormat": "Giờ (hh:mm)", "hexColor": "Hex color", "base64": "Base64", "uuid": "UUID", "filename": "Tên file", "urlSlug": "Slug URL", "variableName": "<PERSON><PERSON><PERSON>", "creditCard": "Số thẻ tín dụng", "qrCode": "Mã QR", "gpsCoordinate": "Tọa độ GPS", "rgbColor": "Mã màu RGB", "domain": "<PERSON><PERSON><PERSON>", "decimal": "<PERSON><PERSON> thập phân", "barcode": "Mã vạch"}, "bulkDeleteSuccess": "<PERSON><PERSON> xóa thành công {{count}} trường tùy chỉnh", "bulkDeleteError": "C<PERSON> lỗi xảy ra khi xóa trường tùy chỉnh", "selectedItems": "<PERSON><PERSON> chọn {{count}} mục", "totalFields": "Tổng số trường tùy chỉnh", "manage": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả"}, "interactionHistory": {"title": "<PERSON><PERSON><PERSON> sử tương tác & Tr<PERSON><PERSON> thái", "channelStatus": "<PERSON>r<PERSON><PERSON> thái kênh liên lạc", "recentActivities": "<PERSON><PERSON><PERSON> động gần đây", "statistics": "<PERSON><PERSON><PERSON><PERSON> kê tương tác", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "<PERSON><PERSON> kết nối"}, "noEmail": "Chưa có email", "noZalo": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>", "noPhone": "<PERSON><PERSON><PERSON> có số điện thoại", "lastSent": "<PERSON><PERSON><PERSON> cu<PERSON>i", "lastMessage": "<PERSON> n<PERSON>n cuối", "lastCall": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> cuối", "openRate": "Tỷ lệ mở", "responseRate": "Tỷ lệ phản hồi", "smsDelivered": "SMS đã gửi", "emailOpened": "Đã mở email", "phoneCall": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i tư vấn", "smsReceived": "Nhận SMS", "noZaloActivity": "<PERSON><PERSON><PERSON> có hoạt động <PERSON>", "connectZalo": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> để theo dõi tương tác", "totalEmails": "<PERSON><PERSON> g<PERSON>", "totalCalls": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "totalSms": "SMS đã gửi", "totalZalo": "<PERSON>"}, "zalo": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> Official Account, g<PERSON><PERSON> tin <PERSON> chăm sóc kh<PERSON>ch hàng", "totalAccounts": "<PERSON><PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON>", "comingSoon": "<PERSON><PERSON><PERSON> năng đang đượ<PERSON> phát triển. Vui lòng quay lại sau!", "messages": "<PERSON>", "articles": {"title": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý bài viết tr<PERSON><PERSON> Official Account", "table": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "author": "Tác g<PERSON>", "stats": "<PERSON><PERSON><PERSON><PERSON> kê", "publishTime": "<PERSON><PERSON><PERSON><PERSON> gian xu<PERSON> b<PERSON>n"}, "type": {"title": "<PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> th<PERSON>", "text": "<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "video": "Video", "link": "<PERSON><PERSON><PERSON>"}, "status": {"draft": "Nháp", "published": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "archived": "<PERSON><PERSON><PERSON> tr<PERSON>", "deleted": "Đã xóa", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "actions": {"show": "<PERSON><PERSON><PERSON> thị", "hide": "Ẩn", "retry": "<PERSON><PERSON><PERSON> lại"}, "showSuccess": "<PERSON><PERSON><PERSON> thị bài viết thành công", "hideSuccess": "Ẩn bài viết thành công", "showError": "<PERSON><PERSON> lỗi xảy ra khi hiển thị bài viết", "hideError": "<PERSON><PERSON> lỗi xảy ra khi <PERSON>n bài viết", "retrySuccess": "<PERSON><PERSON><PERSON> lại bài viết thành công", "retryError": "<PERSON><PERSON> lỗi xảy ra khi đăng lại bài viết", "deleteError": "Có lỗi xảy ra khi xóa bài viết", "syncSuccess": "<PERSON><PERSON>ng bộ bài viết từ tất cả OA thành công", "syncError": "<PERSON><PERSON> lỗi xảy ra khi đồng bộ bài viết. <PERSON><PERSON> lòng thử lại."}, "automation": {"title": "Automation", "description": "<PERSON><PERSON><PERSON><PERSON> lập tự động hóa tin nhắn"}, "analytics": {"title": "Analytics", "description": "Báo cáo và phân tích hiệu quả"}, "znsCampaigns": {"title": "<PERSON><PERSON><PERSON>", "description": "Q<PERSON>ản lý chiến dịch Zalo Notification Service"}, "mediaResources": {"title": "T<PERSON>i <PERSON>n <PERSON>", "description": "Quản lý file media cho Zalo OA"}, "chat": {"title": "Cha<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý cuộc trò chuy<PERSON>n <PERSON>", "selectAccount": "<PERSON><PERSON><PERSON> tà<PERSON>", "chooseAccount": "<PERSON><PERSON><PERSON> tà<PERSON>", "noActiveAccounts": "<PERSON><PERSON><PERSON><PERSON> có tài khoản hoạt động", "selectAccountFirst": "<PERSON><PERSON> lòng chọn tài kho<PERSON>n <PERSON> trước", "selectAccountDescription": "<PERSON><PERSON>n tài khoản Zalo để bắt đầu chat với khách hàng", "searchContacts": "<PERSON><PERSON><PERSON> ki<PERSON>m liên hệ", "newChat": "<PERSON><PERSON> mới", "selectContactToStart": "<PERSON><PERSON><PERSON> liên hệ để bắt đầu trò chuyện", "selectContactDescription": "<PERSON><PERSON><PERSON> một liên hệ từ danh sách bên trái để bắt đầu cuộc trò chuyện", "newConversation": "<PERSON><PERSON><PERSON><PERSON> trò chuyện mới", "noContacts": "<PERSON><PERSON><PERSON> có liên hệ nào", "noContactsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy liên hệ nào", "noConversations": "Ch<PERSON>a có cuộc trò chuyện nào", "noConversationsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cuộc trò chuyện nào", "tryDifferentSearch": "Thử từ khóa tìm kiếm khác", "searchResults": "{{count}} kế<PERSON> qu<PERSON> tìm kiếm", "totalContacts": "{{count}} li<PERSON>n hệ", "totalConversations": "{{count}} cu<PERSON><PERSON> tr<PERSON> ch<PERSON>", "online": "Đang online", "offline": "Offline", "lastSeen": "<PERSON><PERSON><PERSON> động cuối {{time}}", "justNow": "<PERSON><PERSON><PERSON> xong", "minutesAgo": "{{count}} ph<PERSON><PERSON> tr<PERSON><PERSON>c", "hoursAgo": "{{count}} g<PERSON><PERSON> trước", "you": "Bạn", "image": "<PERSON><PERSON><PERSON>", "file": "File", "message": "<PERSON>", "noMessages": "<PERSON><PERSON><PERSON> có tin nhắn", "startConversation": "<PERSON><PERSON><PERSON> đầu cuộc trò chuyện với {{name}}", "selectContact": "<PERSON><PERSON><PERSON> liên hệ để xem tin nhắn", "typeMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn...", "typeMessageTo": "<PERSON><PERSON><PERSON><PERSON> tin nhắn cho {{name}}...", "sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "newLine": "<PERSON><PERSON><PERSON> dòng", "attachFile": "<PERSON><PERSON><PERSON> k<PERSON> file", "emoji": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> cảm x<PERSON>c", "addTag": "Th<PERSON><PERSON> tag", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "tags": "Tags", "notes": "<PERSON><PERSON><PERSON>", "attachedFiles": "<PERSON> đ<PERSON> k<PERSON>m", "replyTo": "<PERSON><PERSON><PERSON> lờ<PERSON>", "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> xuống cuối", "connected": "<PERSON><PERSON> kết nối", "showContacts": "<PERSON><PERSON><PERSON> thị danh sách liên hệ", "backToList": "Quay lại danh s<PERSON>ch", "expandSidebar": "Mở rộng sidebar", "collapseSidebar": "Thu nhỏ sidebar", "keyboardShortcuts": "<PERSON><PERSON><PERSON>", "unknownSender": "<PERSON><PERSON><PERSON><PERSON> gửi không x<PERSON>c đ<PERSON>nh", "errors": {"loadAccountsFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách tài k<PERSON>n", "loadContactsFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách liên hệ", "loadConversationsFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách cuộc trò chuyện", "loadMessagesFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải tin nhắn", "sendMessageFailed": "<PERSON><PERSON><PERSON> tin nhắn thất bại", "createContactFailed": "<PERSON><PERSON><PERSON> liên hệ thất bại", "uploadFailed": "Tải file lên thất bại", "updateSettingsFailed": "<PERSON><PERSON><PERSON> nhật cài đặt thất bại", "tooManyFiles": "Chỉ đư<PERSON><PERSON> đ<PERSON>h kèm tối đa {{max}} file", "fileTooLarge": "File {{name}} quá lớn. <PERSON><PERSON><PERSON> thư<PERSON>c tối đa là {{max}}MB", "invalidFileType": "Loại file {{name}} không được hỗ trợ"}, "success": {"messageSent": "<PERSON> nhắn đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "fileUploaded": "File đã đ<PERSON><PERSON><PERSON> tải lên", "contactCreated": "<PERSON><PERSON><PERSON> hệ đã đ<PERSON><PERSON><PERSON> t<PERSON>o", "settingsUpdated": "<PERSON>ài đặt đã được cập nhật"}}, "oaMessages": {"title": "Tin nhắn OA", "description": "<PERSON><PERSON><PERSON><PERSON> lý tin nh<PERSON>n Official Account", "table": {"status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "status": {"pending": "<PERSON>ờ gửi", "sent": "Đ<PERSON> gửi", "delivered": "Đã nhận", "read": "<PERSON><PERSON> đ<PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}}, "oa": {"management": {"title": "Quản lý Zalo OA"}}, "personalAccount": {"integration": {"title": "<PERSON><PERSON><PERSON> c<PERSON>hân"}}, "integration": {"select": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>:", "placeholder": "<PERSON><PERSON><PERSON>"}, "modules": {"znsTemplates": {"title": "Template ZNS", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> thông báo Z<PERSON> cho khách hàng", "sync": "Đồng bộ templates t<PERSON>", "table": {"templateName": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "templateType": "Lo<PERSON>i Template", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "filter": {"status": "<PERSON><PERSON><PERSON> theo trạng thái", "type": "<PERSON><PERSON><PERSON> the<PERSON>"}, "tag": {"transaction": "<PERSON><PERSON><PERSON>", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "otp": "OTP"}}, "oaTemplates": {"title": "Template Tin nhắn OA", "description": "Tạo và quản lý <PERSON> tin nhắn Official Account"}, "officialAccount": {"title": "Tài khoản OA", "description": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Official Account và kết n<PERSON>i"}, "znsCampaigns": {"description": "Q<PERSON>ản lý chiến dịch Zalo Notification Service"}, "groups": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý và tổ chức nhóm người dùng"}, "articles": {"description": "<PERSON><PERSON><PERSON><PERSON> lý bài vi<PERSON>t Zalo OA"}, "content": {"title": "<PERSON><PERSON><PERSON><PERSON> lý nội dung", "description": "<PERSON><PERSON><PERSON>n lý bài viết và video marketing"}, "mediaResources": {"description": "Quản lý file media cho Zalo OA"}, "personalAccount": {"title": "<PERSON><PERSON><PERSON>n cá nhân", "description": "<PERSON><PERSON><PERSON>n lý tài khoản <PERSON> cá nhân và tích hợp", "table": {"stt": "STT", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "actions": {"addIntegration": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch hợp mới", "relogin": "<PERSON><PERSON><PERSON> lại", "checkStatus": "<PERSON><PERSON><PERSON> tra trạng thái đăng nhập"}, "messages": {"reloginSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>p lại thành công", "reloginError": "Lỗi khi đăng nhập lại", "statusChecked": "<PERSON><PERSON> kiểm tra trạng thái", "statusCheckError": "Lỗi khi kiểm tra trạng thái", "deleteSuccess": "<PERSON><PERSON><PERSON> tích hợp thành công", "deleteError": "Lỗi khi xóa tích hợp"}, "deleteModal": {"title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "description": "Bạn có chắc chắn muốn xóa {{count}} tích hợp đã chọn?"}, "integration": {"title": "<PERSON><PERSON><PERSON> c<PERSON>hân", "description": "<PERSON>ết nối tài k<PERSON>n <PERSON> cá nhân để sử dụng các tính năng marketing", "oauth": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> qua <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> thức k<PERSON> nghị - <PERSON><PERSON><PERSON> nhập an toàn qua <PERSON>alo", "instructions": "<PERSON><PERSON><PERSON> nút bên dưới để đăng nhập vào <PERSON> và cấp quyền cho ứng dụng", "loginButton": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>"}, "manual": {"title": "<PERSON><PERSON><PERSON> hợp thủ công", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin xác thực thủ công"}, "form": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "integrationNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cho tích hợp nà<PERSON>...", "authCode": "<PERSON><PERSON> xác thực", "authCodeDescription": "<PERSON><PERSON> xác thực từ Zalo Developer Con<PERSON>e", "authCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã xác thực...", "redirectUri": "Redirect URI", "redirectUriDescription": "URL callback sau khi xác thực thành công", "redirectUriPlaceholder": "<PERSON><PERSON><PERSON><PERSON> redirect URI...", "createButton": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>p"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> tích hợp thành công", "createError": "Lỗi khi tạo tích hợp", "oauthComingSoon": "<PERSON><PERSON><PERSON> n<PERSON>ng <PERSON>uth đang đư<PERSON><PERSON> phát triển"}, "errors": {"nameRequired": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON> là bắ<PERSON> bu<PERSON>c", "authCodeRequired": "<PERSON><PERSON> x<PERSON>c thực là bắt bu<PERSON>c", "redirectUriRequired": "Redirect URI là bắt buộc"}}}, "oaMessages": {"title": "Tin nhắn OA", "description": "<PERSON><PERSON><PERSON> và quản lý tin nhắn qua Official Account"}, "followers": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> Followers", "description": "<PERSON> và quản lý người theo dõi OA"}, "analytics": {"title": "Analytics", "description": "Báo cáo và phân tích hiệu quả chiến dịch"}, "automation": {"title": "Automation", "description": "<PERSON><PERSON><PERSON><PERSON> lập tự động hóa tin nhắn và workflow"}}, "overview": {"title": "Zalo Marketing", "description": "<PERSON><PERSON><PERSON><PERSON> Official Account và chiến d<PERSON><PERSON>", "connectAccount": "<PERSON><PERSON><PERSON>", "noAccounts": "<PERSON><PERSON>a có tài k<PERSON>n nào", "noAccountsDescription": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Official Account để bắt đầu", "connectFirstAccount": "<PERSON><PERSON><PERSON> n<PERSON>i tài khoản đầu tiên", "connectedAccounts": "<PERSON><PERSON><PERSON> k<PERSON>n đã kết nối", "connectedAccountsDescription": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> Official Account đ<PERSON> kết nối", "viewAllAccounts": "<PERSON><PERSON> tất cả tài <PERSON>n", "stats": {"totalAccounts": "Tổng số OA", "activeAccounts": "<PERSON><PERSON> ho<PERSON>t động", "totalFollowers": "Tổng Followers", "newFollowersToday": "+12 hôm nay", "messagesSent": "<PERSON> nhắn đã gửi", "messagesToday": "+89 hôm nay", "engagementRate": "Tỷ lệ tương tác", "increaseFromLastWeek": "+2.1% từ tuần trước"}}, "accounts": {"title": "Tài khoản OA", "description": "<PERSON><PERSON><PERSON> n<PERSON>i và quản lý c<PERSON> Official Account", "connectNew": "<PERSON><PERSON>t nối OA mới", "connectAccount": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "T<PERSON>m kiếm theo tên OA...", "filters": {"title": "<PERSON><PERSON> lọc", "advanced": "<PERSON><PERSON> lọc nâng cao"}, "list": {"title": "<PERSON><PERSON> s<PERSON>ch tà<PERSON>", "description": "T<PERSON><PERSON> cộng {{count}} t<PERSON><PERSON>n", "noData": "<PERSON><PERSON>a có tài k<PERSON>n nào"}, "table": {"id": "ID", "name": "<PERSON><PERSON>n <PERSON>", "oaId": "OA ID", "followers": "Followers", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "lastUpdate": "<PERSON><PERSON><PERSON> nh<PERSON> cu<PERSON>", "actions": "<PERSON><PERSON>"}, "actions": {"refreshToken": "Refresh <PERSON>"}, "connect": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin để kết n<PERSON><PERSON> Official Account", "instructions": "<PERSON><PERSON> kết n<PERSON><PERSON>, bạn cần có quyền quản trị viên và lấy thông tin từ Zalo Developer Console.", "learnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "oaId": "OA ID", "oaIdDescription": "ID của Zalo Official Account (có thể tìm trong Zalo OA Manager)", "oaIdPlaceholder": "Nhập OA ID...", "name": "<PERSON><PERSON>n <PERSON>", "nameDescription": "<PERSON><PERSON><PERSON> hiển thị của Official Account", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên hiển thị...", "accessToken": "Access Token", "accessTokenDescription": "Access token từ Zalo Developer Console", "accessTokenPlaceholder": "Nhập access token...", "refreshToken": "Refresh <PERSON>", "refreshTokenDescription": "Refresh token để gia hạn access token", "refreshTokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON> refresh token...", "avatar": "Avatar URL (<PERSON><PERSON><PERSON>)", "avatarDescription": "URL ảnh đại diện của OA", "avatarPlaceholder": "https://...", "submit": "<PERSON><PERSON><PERSON>", "help": {"title": "Cần trợ gi<PERSON>?", "description": "<PERSON><PERSON> kh<PERSON>o tài liệu hướng dẫn kết n<PERSON><PERSON> Official Account", "viewDocs": "<PERSON><PERSON> tài li<PERSON>u", "step1": "1. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Console và tạo ứng dụng", "step2": "2. <PERSON><PERSON><PERSON> O<PERSON> từ phần quản lý Official Account", "step3": "3. Tạo access token và refresh token", "step4": "4. <PERSON><PERSON><PERSON> <PERSON>nh webhook URL nếu c<PERSON>n thiết"}, "page": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON> Official Account", "description": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức kết nối phù hợp với nhu cầu của bạn", "methods": {"oauth": {"title": "<PERSON><PERSON> v4", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị", "description": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> thông qua OAuth v4 củ<PERSON>"}, "api": {"title": "Zalo API Explorer", "value": "<PERSON><PERSON><PERSON> công", "description": "<PERSON>ết nối bằng Access Token và Refresh Token", "formTitle": "Zalo API Explorer", "formDescription": "Nhập Access Token và Refresh Token từ Zalo Developer Console", "accessTokenLabel": "Access Token *", "accessTokenPlaceholder": "Nhập access token", "refreshTokenLabel": "Refresh <PERSON> *", "refreshTokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON> refresh token", "connectButton": "<PERSON><PERSON><PERSON>", "backButton": "Quay lại"}}, "success": "<PERSON><PERSON><PERSON><PERSON> Official Account thành công!", "error": "<PERSON><PERSON> lỗi xảy ra khi kết nối"}}}, "followers": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> Followers", "description": "Followers của {{name}}", "descriptionDefault": "<PERSON><PERSON><PERSON><PERSON> lý danh s<PERSON>ch followers", "export": "Export", "sync": "<PERSON><PERSON><PERSON> bộ", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo tên, số điện thoại...", "stats": {"totalFollowers": "Tổng Followers", "activeFollowers": "<PERSON><PERSON><PERSON> đ<PERSON>", "newThisWeek": "<PERSON><PERSON><PERSON> tu<PERSON> này", "selected": "<PERSON><PERSON> ch<PERSON>n", "interacting": "<PERSON><PERSON> tư<PERSON> tác", "increase": "Tăng 15%"}, "actions": {"sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "addTag": "Th<PERSON><PERSON> tag"}, "bulkActions": {"title": "<PERSON><PERSON> t<PERSON> hàng lo<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> hiện thao tác cho {{count}} followers đã chọn", "addTag": "Th<PERSON><PERSON> tag", "sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "addTagVip": "Thêm tag \"VIP\"", "addTagNewCustomer": "Thêm tag \"<PERSON><PERSON><PERSON><PERSON> hàng mới\"", "sendBulkMessage": "<PERSON><PERSON><PERSON> tin nhắn hàng lo<PERSON>t"}, "table": {"name": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "followDate": "<PERSON><PERSON><PERSON> theo d<PERSON>i", "lastInteraction": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c cu<PERSON>i", "tags": "Tags", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON>"}}, "groups": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các nhóm Z<PERSON> và thành viên", "searchPlaceholder": "T<PERSON><PERSON> kiếm nhóm...", "createTitle": "Tạo nhóm mới", "createDescription": "<PERSON><PERSON><PERSON> năng đang đư<PERSON> phát triển", "editTitle": "Chỉnh sửa nhóm", "editDescription": "<PERSON><PERSON><PERSON> năng đang đư<PERSON> phát triển", "detailTitle": "<PERSON> tiết nh<PERSON>m", "loadingDetail": "<PERSON><PERSON> tải chi tiết nhóm...", "loadDetailError": "<PERSON><PERSON><PERSON><PERSON> thể tải chi tiết nhóm", "deleteSuccess": "<PERSON><PERSON><PERSON> nhóm thành công", "deleteError": "Có lỗi xảy ra khi xóa nhóm", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa nhóm này?", "groupLink": "Link <PERSON>", "maxMembers": "<PERSON><PERSON><PERSON> đa", "groupInfo": "Thông tin nhóm", "groupNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên n<PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả nhóm", "avatarUrl": "URL ảnh đại di<PERSON>n", "avatarUrlHelp": "<PERSON>hập U<PERSON>nh đại diện cho nh<PERSON> (tù<PERSON> chọn)", "avatarUrlPlaceholder": "https://example.com/avatar.jpg", "assetId": "Asset ID", "assetIdHelp": "<PERSON><PERSON><PERSON> để tạo nhóm", "selectAsset": "<PERSON><PERSON><PERSON>", "avatarUpload": "Ảnh đại diện nhóm", "changeAvatar": "<PERSON><PERSON><PERSON>", "clickToChange": "<PERSON>lick ảnh để thay đổi", "uploadAvatarHint": "<PERSON><PERSON><PERSON> đại <PERSON>", "avatarRequirements": "JPG, PNG • T<PERSON>i đa 5MB", "selectAvatar": "<PERSON><PERSON><PERSON>", "initialMembers": "<PERSON><PERSON><PERSON><PERSON> viên ban đầu", "memberUids": "User IDs thành viên", "memberUidsHelp": "Nhập User ID của các thành viên muốn thêm vào nhóm", "memberUidsPlaceholder": "Nhập User ID và nhấn Enter", "memberUidsNote": "Lưu ý: <PERSON><PERSON><PERSON> ít nhất 1 thành viên để tạo nhóm. User ID có thể lấy từ danh sách người dùng Zalo OA.", "availableAssets": "Assets có sẵn", "validThrough": "<PERSON><PERSON><PERSON>", "used": "Đã sử dụng", "available": "<PERSON><PERSON> sẵn", "invalidImageType": "<PERSON>ui lòng chọn file hình <PERSON>nh", "imageTooLarge": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON>nh không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 5MB", "uploadNotSupported": "<PERSON><PERSON> lòng nhập URL ảnh vào trường \"URL ảnh đại diện\" bên dư<PERSON>i", "updateNotImplemented": "<PERSON><PERSON><PERSON> năng cập nhật nhóm đang đư<PERSON><PERSON> phát triển", "createSuccess": "<PERSON><PERSON><PERSON> nhóm thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật nhóm", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo nhóm", "noGroups": "Chưa có nhóm nào", "inviteSuccess": "<PERSON><PERSON><PERSON> thành viên thành công", "inviteError": "<PERSON><PERSON> lỗi xảy ra khi mời thành viên", "removeMemberSuccess": "<PERSON><PERSON><PERSON> thành viên thành công", "removeMemberError": "Có lỗi xảy ra khi xóa thành viên", "toggleAdminSuccess": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> quyền admin thành công", "toggleAdminError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nh<PERSON>t quyền admin", "stats": {"totalGroups": "Tổng Groups", "activeGroups": "<PERSON><PERSON> ho<PERSON>t động", "totalMembers": "<PERSON><PERSON><PERSON> thành viên", "newMembers": "<PERSON><PERSON><PERSON><PERSON> viên mới"}, "table": {"name": "<PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON><PERSON> viên", "admins": "<PERSON><PERSON><PERSON><PERSON> trị viên", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "lastActivity": "<PERSON><PERSON><PERSON> động cu<PERSON>i", "actions": "<PERSON><PERSON>", "ariaLabel": "<PERSON><PERSON><PERSON> danh s<PERSON>ch n<PERSON><PERSON>"}, "filters": {"title": "<PERSON><PERSON> lọc", "byOA": "Theo <PERSON>", "byStatus": "<PERSON> tr<PERSON> thái", "byActivity": "<PERSON> đ<PERSON>", "byRole": "<PERSON> vai trò"}, "members": {"title": "<PERSON><PERSON> s<PERSON>ch thành viên", "avatar": "Avatar", "name": "<PERSON><PERSON><PERSON>", "role": "<PERSON>ai trò", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "joinedAt": "<PERSON><PERSON><PERSON> tham gia"}}, "zns": {"title": "ZNS Templates", "description": "Quản lý template thông báo Zalo Notification Service", "createTemplate": "<PERSON><PERSON>o <PERSON>late", "searchPlaceholder": "T<PERSON><PERSON> k<PERSON>ếm template...", "createNew": "<PERSON><PERSON><PERSON> mới", "collapse": "<PERSON><PERSON>", "stats": {"totalTemplates": "Tổng Templates", "approved": "Đ<PERSON>", "pending": "<PERSON>ờ <PERSON>", "rejected": "<PERSON><PERSON> chối", "avgCost": "Chi phí TB", "perMessage": "Mỗi tin nhắn", "newTemplates": "+2 template mới", "readyToUse": "Sẵn sàng sử dụng", "underReview": "<PERSON><PERSON> xem xét"}, "table": {"template": "Template", "templateId": "Template ID", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "quality": "<PERSON><PERSON><PERSON>", "cost": "Chi phí", "params": "<PERSON>ham s<PERSON>", "updated": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "status": {"approved": "<PERSON><PERSON><PERSON>", "pending": "<PERSON>ờ <PERSON>", "rejected": "<PERSON><PERSON> chối", "disabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a"}, "quality": {"high": "<PERSON>", "normal": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON><PERSON>"}, "sync": {"success": "<PERSON><PERSON><PERSON> bộ thành công {{count}} ZNS template từ Zalo API", "error": "<PERSON><PERSON><PERSON> bộ ZNS templates thất bại: {{error}}"}, "create": {"title": "Tạo ZNS Template", "success": "Template <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> tạo thành công", "description": "Tạo template thông báo ZNS mới", "instructions": {"title": "Hướng dẫn tạo ZNS Template", "description": "Template sẽ được gửi đến Zalo để duyệt trước khi sử dụng. Nội dung phải tuân thủ quy định của Zalo về ZNS. Sử dụng {param_name} để đánh dấu tham số động."}, "form": {"oaLabel": "Chọn Official Account", "oaHelp": "Chọn OA để tạo template", "oaPlaceholder": "Chọn Official Account...", "nameLabel": "<PERSON><PERSON><PERSON>", "nameHelp": "<PERSON>ên mô tả cho template (chỉ sử dụng nội bộ)", "namePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> đ<PERSON> h<PERSON>, <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> mãi...", "contentLabel": "<PERSON><PERSON>i dung Template", "contentHelp": "<PERSON><PERSON><PERSON> dung tin nhắn ZNS. Sử dụng {param_name} cho tham số động", "contentPlaceholder": "Ví dụ: <PERSON><PERSON> chà<PERSON> {customer_name}, đơn hàng #{order_id} của bạn đã được xác nhận với tổng giá trị {total_amount} VNĐ. Cảm ơn bạn đã mua hàng!"}}, "template": {"form": {"steps": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "components": "<PERSON><PERSON> b<PERSON>o nội dung", "review": "<PERSON><PERSON><PERSON>"}, "validation": {"incompleteTemplate": "<PERSON><PERSON><PERSON> ho<PERSON>n thiện template theo yêu cầu"}, "basicInfo": {"title": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "description": "<PERSON><PERSON> b<PERSON>o các thông tin bên dưới để tạo ra ZNS", "templateName": {"label": "Tên mẫu ZNS", "placeholder": "VD: <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> xác nhận đơn hàng", "required": "Tên template không đư<PERSON><PERSON> để trống"}, "officialAccount": {"label": "Chọn Official Account", "placeholder": "Chọn Official Account...", "required": "<PERSON><PERSON> lòng ch<PERSON>n Official Account", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch OA..."}, "contentType": {"label": "<PERSON><PERSON><PERSON> lo<PERSON>i nội dung Z<PERSON>", "placeholder": "<PERSON><PERSON><PERSON> lo<PERSON>i nội dung", "required": "<PERSON><PERSON> lòng chọn lo<PERSON>i nội dung"}, "templateType": {"label": "<PERSON><PERSON><PERSON> lo<PERSON> template", "placeholder": "<PERSON><PERSON><PERSON> lo<PERSON> template", "required": "<PERSON><PERSON> lòng chọn lo<PERSON> template"}, "note": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON> chú thêm về template..."}}, "components": {"title": "<PERSON><PERSON> b<PERSON>o nội dung", "description": "<PERSON><PERSON><PERSON><PERSON> kế nội dung ZNS của bạn", "sections": {"header": "Head<PERSON>", "body": "Nội dung ZNS", "footer": "<PERSON><PERSON><PERSON> thao tác"}, "empty": "<PERSON><PERSON> lòng thiết kế", "notImplemented": "Component ch<PERSON><PERSON> implement"}, "review": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>em lại thông tin và gửi template để duy<PERSON>t", "templateInfo": {"title": "Thông tin Template", "name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "tag": "<PERSON><PERSON>ã<PERSON>", "note": "<PERSON><PERSON><PERSON>", "components": "Components", "parameters": "<PERSON>ham s<PERSON>"}, "parameters": {"title": "<PERSON><PERSON> s<PERSON>", "name": "<PERSON>ham s<PERSON>", "type": "<PERSON><PERSON><PERSON> tham số", "sampleValue": "<PERSON><PERSON><PERSON> trị mẫu", "empty": "<PERSON><PERSON><PERSON><PERSON> có tham số nào đư<PERSON><PERSON> tìm thấy"}}, "actions": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "create": "Tạo", "back": "Quay lại", "cancel": "<PERSON><PERSON><PERSON>", "creating": "<PERSON><PERSON> tạo..."}}, "types": {"1": "ZNS tùy chỉnh", "2": "ZNS x<PERSON>c thực", "3": "ZNS y<PERSON>u c<PERSON>u <PERSON>h toán", "4": "ZNS thanh toán", "5": "ZNS Voucher"}, "tags": {"TRANSACTION": "<PERSON><PERSON><PERSON> (Cấp độ 1)", "CUSTOMER_CARE": "<PERSON><PERSON><PERSON><PERSON> (Cấp độ 2)", "PROMOTION": "<PERSON><PERSON><PERSON><PERSON><PERSON>ã<PERSON> (Cấp độ 3)", "OTP": "OTP (Cấp độ 1)"}}, "parameters": {"types": {"customer": "<PERSON><PERSON><PERSON> (30)", "phone": "<PERSON><PERSON> đi<PERSON> th<PERSON> (15)", "address": "Đ<PERSON>a chỉ (200)", "id": "<PERSON><PERSON> (30)", "personal": "<PERSON><PERSON><PERSON> tự chính (30)", "status": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i giao <PERSON> (30)", "contact": "<PERSON><PERSON><PERSON><PERSON> tin liên hệ (50)", "time": "<PERSON>i<PERSON>i tình / Danh <PERSON> (5)", "product": "<PERSON><PERSON><PERSON> sản phẩm / <PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON> (200)", "amount": "<PERSON><PERSON> / Số tiề<PERSON> (20)", "duration": "<PERSON><PERSON><PERSON><PERSON>ian (20)", "otp": "OTP (10)", "url": "URL (200)", "money": "<PERSON><PERSON><PERSON><PERSON> (VND) (12)", "bank_note": "Bank transfer note (90)"}, "sampleValues": {"customer": "<PERSON><PERSON><PERSON><PERSON>", "phone": "**********", "address": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM", "id": "CODE123", "personal": "Anh/Chị", "status": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "contact": "<EMAIL>", "time": "<PERSON><PERSON>", "product": "iPhone 15 Pro Max", "amount": "2", "duration": "30 phút", "otp": "123456", "url": "https://example.com", "money": "1,500,000", "bank_note": "<PERSON><PERSON> toan don hang #12345", "default": "<PERSON><PERSON><PERSON> trị {{paramName}}"}, "displayNames": {"customer": "<PERSON><PERSON><PERSON> h<PERSON>ng", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "id": "Mã số", "personal": "Nhân tự chính", "status": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "contact": "<PERSON>h<PERSON>ng tin liên hệ", "time": "G<PERSON><PERSON>i tính/Danh xưng", "product": "<PERSON><PERSON><PERSON> sản phẩm/<PERSON><PERSON><PERSON><PERSON><PERSON> hiệu", "amount": "Số lượng/Số tiền", "duration": "<PERSON><PERSON><PERSON><PERSON> gian", "otp": "Mã OTP", "url": "URL", "money": "<PERSON><PERSON><PERSON><PERSON> (VND)", "bank_note": "Bank transfer note"}, "otpDisplayName": "<PERSON><PERSON> x<PERSON>c th<PERSON>c <PERSON>"}, "componentSelector": {"components": {"images": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> vào ZNS"}, "logo": {"name": "Logo", "description": "<PERSON><PERSON> thư<PERSON> hi<PERSON>u"}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> đề ch<PERSON>h của tin nh<PERSON>n"}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON> văn", "description": "<PERSON><PERSON><PERSON> dung văn bản"}, "otp": {"name": "OTP", "description": "<PERSON><PERSON> x<PERSON>c th<PERSON>c <PERSON>"}, "table": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> thị dữ liệu dạng bảng"}, "voucher": {"name": "Voucher", "description": "Mã giảm giá/voucher"}, "payment": {"name": "<PERSON><PERSON> toán", "description": "Thông tin thanh toán"}, "rating": {"name": "Rating", "description": "Đánh gi<PERSON> d<PERSON>ch vụ"}, "buttons": {"name": "<PERSON><PERSON><PERSON> b<PERSON>", "description": "<PERSON><PERSON><PERSON> nút hành động"}}, "defaultData": {"title": "Ti<PERSON>u đề mẫu", "paragraph": "<PERSON><PERSON>i dung đoạn văn mẫu", "otp": "123456", "button": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "rating": {"1": "<PERSON><PERSON><PERSON> kh<PERSON>ng hài lòng", "2": "<PERSON><PERSON><PERSON><PERSON> hài lòng", "3": "<PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON> l<PERSON>", "5": "<PERSON><PERSON><PERSON> hài lòng"}}, "displayNames": {"LOGO": "Logo", "IMAGES": "<PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PARAGRAPH": "<PERSON><PERSON><PERSON><PERSON> văn", "TABLE": "<PERSON><PERSON><PERSON>", "OTP": "Mã OTP", "VOUCHER": "Voucher", "PAYMENT": "<PERSON><PERSON> toán", "RATING": "Đánh giá", "BUTTONS": "<PERSON><PERSON><PERSON> b<PERSON>"}, "defaultContent": {"title": "Ti<PERSON>u đề mẫu", "paragraph": "<PERSON><PERSON>i dung đoạn văn mẫu"}, "sections": {"header": "Header - <PERSON><PERSON>, hình ảnh", "body": "Body - <PERSON><PERSON><PERSON> dung Z<PERSON>", "footer": "Footer - <PERSON><PERSON><PERSON> thao tác", "headerRequired": "Logo, hình ảnh *", "ingredients": "<PERSON><PERSON><PERSON><PERSON> phần"}, "templateChange": {"modal": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>n thay đổi lo<PERSON>i template", "warning": "Bạn có chắc chắn muốn thay đổi loại template?", "description": "Tất cả dữ liệu hiện tại sẽ bị xóa và không thể khôi phục. Bạn sẽ cần thiết lập lại từ đầu.", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>"}}, "componentNames": {"logo": "Logo", "images": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "paragraph": "<PERSON><PERSON><PERSON><PERSON> văn", "table": "<PERSON><PERSON><PERSON>", "otp": "Mã OTP", "voucher": "Voucher", "payment": "<PERSON><PERSON> toán", "rating": "Đánh giá", "buttons": "<PERSON><PERSON><PERSON> b<PERSON>"}, "validation": {"headerLogoImageExclusive": "Header chỉ đượ<PERSON> chứa Logo hoặc <PERSON><PERSON><PERSON>nh, không đượ<PERSON> cả hai", "required": "{{component}} l<PERSON> b<PERSON><PERSON> bu<PERSON>", "minRequired": "{{component}} cần ít nhất {{min}} thành phần (hiện tại: {{current}})", "maxExceeded": "{{component}} chỉ được tối đa {{max}} thành phần (hiện tại: {{current}})", "voucherHeaderRequired": "Template Voucher ph<PERSON>i có ít nhất một trong hai: Logo hoặc Hình ảnh"}}, "components": {"common": {"delete": "Xóa component", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "optional": "<PERSON><PERSON><PERSON>", "maxLength": "T<PERSON>i đa {{max}} ký tự"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "help": "<PERSON><PERSON><PERSON><PERSON> đề ch<PERSON>h của tin nh<PERSON>n", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề..."}, "buttons": {"label": "<PERSON><PERSON><PERSON> thao tác", "help": "<PERSON><PERSON><PERSON> nút hành động", "types": {"1": "Mở URL trong ứng dụng", "2": "<PERSON><PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "3": "Mở URL trong trình <PERSON>", "4": "<PERSON><PERSON> sẻ", "5": "Sao chép", "6": "Mở ứng dụng", "7": "<PERSON><PERSON><PERSON> ký", "8": "Đặt hàng", "9": "K<PERSON><PERSON><PERSON>"}, "fields": {"type": "<PERSON><PERSON><PERSON> n<PERSON>", "title": "<PERSON><PERSON><PERSON> dung nút", "url": "URL liên kết", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "placeholders": {"selectType": "<PERSON><PERSON><PERSON> lo<PERSON> n<PERSON>", "title": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "url": "Nhập URL (ví dụ: https://example.com)", "phone": "<PERSON><PERSON><PERSON><PERSON> số đi<PERSON> tho<PERSON> (ví dụ: **********)"}, "validation": {"phoneValid": "<PERSON><PERSON><PERSON><PERSON> số điện tho<PERSON>i hợp lệ", "urlValid": "Nhập URL đầy đủ bao gồm http:// hoặc https://"}}, "images": {"label": "<PERSON><PERSON><PERSON>", "help": "Chỉ đư<PERSON><PERSON> thêm 1 hình ảnh", "upload": {"placeholder": "<PERSON><PERSON><PERSON> thả hoặc tải ảnh lên - <PERSON><PERSON><PERSON> dạng PNG, JPG<PERSON> <PERSON><PERSON><PERSON> thước: 16:9, tối đa 500 KB", "replace": "<PERSON><PERSON> thế <PERSON>nh hiện tại", "uploading": "<PERSON><PERSON> tải lên...", "uploaded": "✓ <PERSON><PERSON> tải lên", "error": "Lỗi: {{error}}"}, "validation": {"notImage": "File {{name}} không ph<PERSON>i là <PERSON>nh"}}, "logo": {"label": "Logo", "help": "Logo cho theme sáng và tối"}, "paragraph": {"label": "<PERSON><PERSON><PERSON><PERSON> văn", "help": "<PERSON><PERSON><PERSON> dung văn bản", "placeholder": "C<PERSON>m ơn bạn đã mua sản phẩm {{product_name}} tại cửa hàng chúng tôi."}, "otp": {"label": "Nội dung gửi kèm mã OTP", "help": "Nội dung gửi kèm mã OTP"}, "table": {"label": "<PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON> thị dữ liệu dạng bảng", "description": "Các components Table và Paragraph có thể thay đổi vị trí", "addRow": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "placeholders": {"title": "<PERSON><PERSON> đơn hàng", "value": "{{order_code}}"}}, "voucher": {"label": "Voucher", "help": "Mã giảm giá/voucher", "description": "Thông tin voucher", "fields": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "condition": "<PERSON><PERSON><PERSON><PERSON> kiện á<PERSON> dụng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "Hạn sử dụng", "voucherCode": "Mã voucher", "displayCode": "Hi<PERSON>n thị mã voucher"}, "placeholders": {"title": "Giảm 70.000đ", "condition": "<PERSON> đơn hàng trên 200K", "startDate": "<PERSON><PERSON><PERSON> ng<PERSON> b<PERSON><PERSON> đầu", "endDate": "<PERSON><PERSON><PERSON> hạn sử dụng", "voucherCode": "&lt;voucher_code&gt;"}, "displayTypes": {"barcode": "Barcode", "qrcode": "QR code", "textcode": "Text code"}}, "payment": {"label": "<PERSON><PERSON> toán", "help": "Thông tin thanh toán", "description": "Thông tin thanh toán", "fields": {"bank": "<PERSON><PERSON> h<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountNumber": "Số tài <PERSON>n", "amount": "<PERSON><PERSON> tiền (VND)", "note": "<PERSON><PERSON><PERSON> dung chuyển k<PERSON>n"}, "placeholders": {"selectBank": "-- <PERSON><PERSON><PERSON>", "accountName": "<PERSON><PERSON> Trí", "accountNumber": "**************", "amount": "<amount>", "note": "<amount>"}, "validation": {"accountNameRequired": "Tên tài khoản không được để trống", "accountNameLength": "<PERSON><PERSON>n tài k<PERSON>n từ 1-100 ký tự", "accountNumberRequired": "Số tài khoản không được để trống", "accountNumberLength": "Số tài k<PERSON>n từ 1-100 ký tự", "amountInvalid": "<PERSON><PERSON> tiền không hợp lệ", "amountMin": "<PERSON><PERSON> tiền tối thiểu 2,000 VND", "amountMax": "Số tiền tối đa 500,000,000 VND", "noteMaxLength": "<PERSON>ội dung chuyển khoản tối đa 90 ký tự"}, "loading": {"banks": "<PERSON><PERSON> tải danh sách ngân hàng..."}, "errors": {"loadBanks": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách ngân hàng", "loadBanksGeneral": "Lỗi khi tải danh sách ngân hàng"}, "hints": {"accountName": "Tối thiểu 1 ký tự, tối đa 100 ký tự", "accountNumber": "Tối thiểu 1 ký tự, tối đa 100 ký tự"}}, "rating": {"label": "Đánh giá", "help": "Đánh gi<PERSON> d<PERSON>ch vụ", "description": "Đánh gi<PERSON> d<PERSON>ch vụ", "title": "<PERSON><PERSON><PERSON>", "fields": {"scale": "<PERSON><PERSON> đi<PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "action": "<PERSON><PERSON><PERSON> đ<PERSON>", "question": "Câu hỏi", "answers": "<PERSON><PERSON><PERSON> trả lời", "thanks": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>"}, "placeholders": {"title": "<PERSON><PERSON><PERSON><PERSON> tiêu đề", "question": "VD: <PERSON><PERSON><PERSON> tôi có thể cải thiện điều gì?", "answer1": "<PERSON><PERSON><PERSON> gói hàng tốt hơn", "answer2": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> sản phẩm tốt hơn", "thanks": "Cảm ơn bạn đã góp ý!", "description": "Mọi góp ý của bạn đều rất giá trị. <PERSON>úng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa."}, "actions": {"addDetail": "<PERSON><PERSON><PERSON><PERSON> chi tiết", "addAnswer": "<PERSON><PERSON><PERSON><PERSON>"}, "defaultTitles": {"1": "<PERSON><PERSON><PERSON> kh<PERSON>ng hài lòng", "2": "<PERSON><PERSON><PERSON><PERSON> hài lòng", "3": "<PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON> l<PERSON>", "5": "<PERSON><PERSON><PERSON> hài lòng"}}}, "preview": {"title": "ZNS Preview", "empty": "<PERSON><PERSON> lòng thiết kế", "noContent": "<PERSON><PERSON> lòng thiết kế", "imageAlt": "<PERSON><PERSON><PERSON> xem trước", "imageCounter": "{{current}}/{{total}}", "voucher": {"title": "VOUCHER GIẢM GIÁ", "defaultName": "Giảm giá 20%", "defaultCondition": "<PERSON><PERSON> dụng cho đơn hàng tiếp theo", "code": "Mã voucher:", "validFrom": "<PERSON><PERSON> hi<PERSON> l<PERSON> từ", "validUntil": "<PERSON><PERSON> hi<PERSON> l<PERSON> đ<PERSON>n"}, "rating": {"title": "Đánh gi<PERSON> d<PERSON>ch vụ", "subtitle": "<PERSON><PERSON> lòng đ<PERSON>h giá trải nghiệm của bạn", "submit": "<PERSON><PERSON><PERSON> đ<PERSON>h giá"}, "payment": {"title": "Thông tin thanh toán", "amount": "<PERSON><PERSON> tiền", "bankName": "<PERSON><PERSON> h<PERSON>", "accountNumber": "Số tài <PERSON>n", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "transferNote": "<PERSON><PERSON><PERSON> dung chuyển k<PERSON>n"}, "table": {"noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu"}, "buttons": {"defaultText": "Nhấn vào đây"}}, "messages": {"success": {"create": "Tạo ZNS template thành công!", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t Z<PERSON> template thành công!", "delete": "Xóa ZNS template thành công!"}, "error": {"create": "Tạo ZNS template thất bại. <PERSON><PERSON> lòng thử lại.", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t ZNS template thất bại. <PERSON><PERSON> lòng thử lại.", "delete": "Xóa ZNS template thất bại. <PERSON><PERSON> lòng thử lại.", "loadOA": "<PERSON><PERSON><PERSON><PERSON> thể tải danh s<PERSON>ch Official Account", "loadTags": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách template tags", "selectOA": "<PERSON><PERSON> lòng ch<PERSON>n Official Account"}}, "actions": {"create": "<PERSON><PERSON><PERSON> mới", "edit": "Chỉnh sửa", "delete": "Xóa", "preview": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "Quay lại", "submit": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Xóa", "upload": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>"}, "templates": {"custom": "<PERSON><PERSON><PERSON> chỉnh", "authentication": "<PERSON><PERSON><PERSON> th<PERSON>c", "payment": "<PERSON><PERSON> toán", "voucher": "Voucher", "rating": "Đánh giá", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON> template", "step1": "Bước 1: <PERSON><PERSON><PERSON>", "step2": "Bước 2: <PERSON><PERSON><PERSON><PERSON>ế", "step3": "Bước 3: <PERSON><PERSON> s<PERSON>", "step4": "Bước 4: <PERSON><PERSON> tr<PERSON><PERSON><PERSON>"}, "notifications": {"createSuccess": "Tạo template thành công", "createError": "Tạo template thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nhật template thành công", "updateError": "<PERSON><PERSON><PERSON> nhật template thất bại", "deleteSuccess": "Xóa template thành công", "deleteError": "Xóa template thất bại", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "uploadError": "<PERSON><PERSON><PERSON> lên thất bại"}, "validation": {"templateNameRequired": "Tên template là bắt buộc", "templateTypeRequired": "Loại template là bắt buộc", "componentRequired": "Ít nhất một component là bắt buộc", "titleRequired": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "contentRequired": "<PERSON>ội dung là bắt buộc", "imageRequired": "<PERSON><PERSON><PERSON> là b<PERSON> buộc", "buttonTitleRequired": "Tiêu đề nút là bắt buộc", "buttonUrlRequired": "URL nút là bắt buộc", "parameterNameRequired": "<PERSON>ên tham số là bắt buộc", "parameterTypeRequired": "<PERSON><PERSON>i tham số là bắt buộc", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "maxLength": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {max} ký tự", "minLength": "<PERSON><PERSON><PERSON> có <PERSON>t nhất {min} ký tự", "invalidUrl": "URL không hợp lệ", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalidPhone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}}, "ecosystem": {"title": "Zalo Ecosystem", "description": "Tổng quan và tích hợp Zalo OA & Zalo Ads", "viewReports": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "oa": {"title": "Zalo Official Account", "description": "<PERSON><PERSON><PERSON><PERSON> lý tà<PERSON> O<PERSON>, followers và tin nh<PERSON>n", "accounts": "Tài khoản OA", "followers": "Followers", "messages": "<PERSON>", "engagement": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "manage": "Quản lý OA"}, "ads": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> cáo trả phí để mở rộng tiếp cận", "accounts": "<PERSON><PERSON><PERSON> Ads", "campaigns": "<PERSON><PERSON><PERSON>", "spend": "Chi phí", "roas": "ROAS", "reports": "Báo cáo"}, "integration": {"title": "<PERSON><PERSON> hội tích hợp", "description": "Tối ưu hóa hiệu quả bằng cách kết hợp OA và Ads", "retargeting": {"title": "Retargeting từ OA", "description": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch quảng cáo targeting followers của OA"}, "lookalike": {"title": "Lookalike Audience", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng tư<PERSON> tự như followers hiệ<PERSON> tại"}, "zns": {"title": "ZNS sau Ads", "description": "Gửi ZNS follow-up cho người tương tác với quảng cáo"}}, "performance": {"title": "So s<PERSON>h hi<PERSON>u su<PERSON>t", "reach": "<PERSON><PERSON><PERSON><PERSON> cận", "engagement": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "conversion": "<PERSON><PERSON><PERSON><PERSON> đổi", "cost": "Chi phí", "roas": "ROAS", "free": "<PERSON><PERSON><PERSON> phí", "recommendation": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị", "description": "<PERSON><PERSON><PERSON> hợp <PERSON>A để nurture leads và Ads để mở rộng tiếp cận. Sử dụng ZNS để duy trì kết nối với khách hàng."}}, "quickActions": {"title": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "connectOA": "<PERSON><PERSON><PERSON>", "connectAds": "<PERSON><PERSON><PERSON><PERSON>", "createZNS": "Tạo ZNS", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch"}}, "oaManagement": {"title": "Tài khoản OA", "navigation": {"content": "<PERSON><PERSON>i dung", "zns": "ZNS"}, "tabs": {"articles": "<PERSON><PERSON><PERSON> vi<PERSON>", "znsTemplates": "ZNS Templates", "znsConfiguration": "ZNS Configuration", "znsResources": "ZNS Resources"}, "configuration": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hình các thông số cho ZNS templates", "settings": "Cài đặt", "general": "<PERSON>", "advanced": "<PERSON><PERSON><PERSON> cao", "save": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "reset": "Đặt lại", "export": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "import": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u hình"}, "resources": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>, logo và tài nguyên kh<PERSON>c", "images": "<PERSON><PERSON><PERSON>", "logos": "Logo", "files": "<PERSON><PERSON>p tin", "upload": "<PERSON><PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> lý", "delete": "Xóa", "edit": "Chỉnh sửa", "usage": "<PERSON><PERSON><PERSON><PERSON> sử dụng"}, "chips": {"officialAccount": "Official Account", "templatesZalo": "Temp<PERSON> <PERSON><PERSON>", "templateTags": "Template Tags", "rating": "Rating"}, "templateTags": {"title": "Template Tags", "description": "<PERSON><PERSON><PERSON> nội dung ZNS mà Official Account <PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON>:", "loading": "<PERSON>ang tải template tags...", "noData": "Không có template tags", "note": "Dựa theo chất lượng gửi ZNS của OA, <PERSON>alo sẽ tự động điều chỉnh loại nội dung OA được gửi.", "types": {"TRANSACTION": "<PERSON><PERSON><PERSON> (Cấp độ 1)", "CUSTOMER_CARE": "<PERSON><PERSON><PERSON><PERSON> (Cấp độ 2)", "PROMOTION": "<PERSON><PERSON><PERSON> (<PERSON>ấp độ 3)"}}, "templates": {"title": "Temp<PERSON> <PERSON><PERSON>", "loading": "<PERSON><PERSON> tải templates...", "noData": "Không có templates", "table": {"templateId": "Template ID", "templateName": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "templateTag": "Template Tag", "price": "Giá", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "actions": "<PERSON><PERSON>"}, "actions": {"viewDetail": "<PERSON>em chi tiết", "ratings": "Ratings"}}, "ratings": {"title": "Ratings cho Template ID: {{templateId}}", "loading": "<PERSON><PERSON> tải dữ liệu ratings...", "noData": "K<PERSON>ông có dữ liệu ratings cho template này", "backToTemplates": "← Quay lại Templates", "description": "<PERSON><PERSON> liệu ratings cho template {{templateId}}"}, "templateDetail": {"title": "<PERSON> ti<PERSON>", "loading": "<PERSON><PERSON> tải chi tiết template...", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy template", "basicInfo": {"title": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "templateId": "Template ID", "templateName": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "templateTag": "Template Tag", "price": "Giá"}, "additionalDetails": {"title": "<PERSON> ti<PERSON><PERSON> b<PERSON> sung", "templateQuality": "<PERSON><PERSON><PERSON>", "timeout": "Timeout", "applyTemplateQuota": "<PERSON><PERSON>", "previewUrl": "URL Xem trước", "viewPreview": "<PERSON><PERSON>"}, "parameters": {"title": "<PERSON>ham s<PERSON>", "count": "<PERSON>ham số ({{count}})", "type": "Loại:", "length": "<PERSON><PERSON> dài:", "acceptNull": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>:", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>"}, "buttons": {"title": "<PERSON><PERSON><PERSON>", "count": "N<PERSON>t ({{count}})", "type": "Loại {{type}}", "content": "Nội dung:"}, "rejectionReason": {"title": "<PERSON>ý do từ chối"}, "actions": {"close": "Đ<PERSON><PERSON>"}, "status": {"REJECT": "<PERSON><PERSON> chối", "APPROVED": "Đ<PERSON>", "PENDING": "<PERSON>ờ <PERSON>"}, "minutes": "<PERSON><PERSON><PERSON><PERSON>"}}, "znsImages": {"form": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> lê<PERSON>/logo cho ZNS template theo quy định c<PERSON><PERSON>", "requirements": "<PERSON><PERSON> <PERSON> c<PERSON><PERSON>:", "formatRequirement": "Định dạng: PNG, JPG", "sizeRequirement": "<PERSON>ng lư<PERSON><PERSON> tối đa: 500 KB", "ratioRequirement": "Tỉ lệ: 16:9 (khuyến nghị: 1920x1080px)", "limitRequirement": "Hạn mức: 5000 ảnh/tháng/ứng dụng", "files": "<PERSON><PERSON><PERSON>", "uploadPlaceholder": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh (PNG, JPG - tối đa 500KB)", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho ảnh...", "submit": "<PERSON><PERSON><PERSON>", "invalidFile": "File không hợp lệ", "fileRequired": "<PERSON>ui lòng chọn ít nhất một file", "fileValidation": "File phải là PNG/JPG và không quá 500KB", "aspectRatio": "<PERSON><PERSON><PERSON>nh phải có tỉ lệ 16:9", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "descriptionTooLong": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> quá 255 ký tự", "invalidDimensions": "<PERSON><PERSON><PERSON>nh phải có tỉ lệ 16:9. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>n nghị: 1920x1080px", "useTemplate": "Sử dụng template", "editImage": "Chỉnh sửa ảnh", "needsEditing": "Ảnh cần chỉnh sửa", "editNow": "Chỉnh sửa ngay", "fileTooLarge": "File quá lớn, cầ<PERSON> nén hoặc chỉnh sửa", "wrongRatio": "Tỉ lệ ảnh không đúng 16:9, c<PERSON><PERSON> c<PERSON><PERSON> lại"}, "table": {"title": "<PERSON><PERSON>", "filename": "Tên file", "description": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả"}}, "imageEditor": {"title": "Chỉnh sửa ảnh", "loading": "<PERSON><PERSON> tả<PERSON>...", "crop": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON> lọc", "preview": "<PERSON><PERSON>", "cropControls": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> c<PERSON><PERSON>", "autoFit": "Tự động khớp tỉ lệ", "filterControls": "<PERSON><PERSON> lọc", "brightness": "<PERSON><PERSON> sáng", "contrast": "<PERSON><PERSON> tư<PERSON> phản", "saturation": "<PERSON><PERSON> bão hòa", "blur": "<PERSON><PERSON> mờ", "save": "<PERSON><PERSON><PERSON>"}, "templates": {"title": "<PERSON><PERSON><PERSON>", "znsDesc": "Template <PERSON><PERSON> <PERSON><PERSON> (16:9)", "logoDesc": "Template cho logo (400x96)", "customText": "<PERSON><PERSON><PERSON> bản tùy chỉnh", "textPlaceholder": "<PERSON><PERSON><PERSON><PERSON> văn bản của bạn...", "preview": "<PERSON><PERSON>", "selectToPreview": "<PERSON><PERSON><PERSON> template để xem trước", "promoTemplate1": "Khuyến mãi 1", "promoDesc1": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi với gradient xanh", "promoTemplate2": "Khuyến mãi 2", "promoDesc2": "Template khu<PERSON><PERSON>n mãi với gradient đỏ", "infoTemplate1": "Thông báo 1", "infoDesc1": "Template th<PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> g<PERSON>n", "logoTemplate1": "Logo đơn giản 1", "logoDesc1": "Logo với nền gradient xanh", "logoTemplate2": "Logo đơn giản 2", "logoDesc2": "Logo với nền trắng", "logoTemplate3": "<PERSON><PERSON> hiện đại", "logoDesc3": "Logo với thiết kế hiện đại"}, "znsTemplates": {"title": "ZNS Templates", "description": "<PERSON><PERSON><PERSON><PERSON> lý các mẫu tin nhắn ZNS của bạn", "table": {"templateName": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "templateTag": "Lo<PERSON>i Template", "createdAt": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "<PERSON>ờ <PERSON>", "approved": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON> từ chối", "disabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a"}, "tag": {"transaction": "<PERSON><PERSON><PERSON>", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "otp": "OTP"}, "filter": {"status": "<PERSON><PERSON><PERSON> theo trạng thái", "type": "<PERSON><PERSON><PERSON> the<PERSON>"}, "create": {"title": "Tạo ZNS Template mới"}}, "personalCampaigns": {"title": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> c<PERSON> nhân", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dị<PERSON> c<PERSON> nhân", "table": {"name": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "type": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "messageType": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "createdAt": "<PERSON><PERSON><PERSON>"}, "campaignTypes": {"sendAll": "<PERSON><PERSON><PERSON> tin nhắn hàng lo<PERSON>t", "crawlFriends": "<PERSON><PERSON> danh s<PERSON>ch bạn bè", "crawlGroups": "<PERSON><PERSON> danh s<PERSON>ch nhóm", "sendFriendRequest": "<PERSON><PERSON><PERSON> lời mời kết bạn"}, "messageTypes": {"text": "<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "qr_code": "Mã QR"}, "status": {"draft": "Nháp", "scheduled": "<PERSON><PERSON> lên l<PERSON>ch", "active": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "actions": {"start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "pause": "<PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch thành công"}, "create": {"title": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>n dị<PERSON> cá nhân mới", "subtitle": "<PERSON><PERSON><PERSON> loại chiến dịch bạn muốn tạo", "configureSubtitle": "<PERSON><PERSON><PERSON> hình chi tiết cho chiến dịch", "formNotImplemented": "Form cho loại chiến dịch này đang đư<PERSON><PERSON> phát triển."}, "forms": {"crawFriendsList": {"title": "<PERSON><PERSON> danh s<PERSON>ch bạn bè", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "campaignName": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "campaignNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chiến dịch craw danh sách bạn bè", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho chiến dịch craw danh sách bạn bè", "account": "<PERSON><PERSON><PERSON>", "accountPlaceholder": "<PERSON><PERSON><PERSON> tài kho<PERSON>n <PERSON>alo để craw danh sách bạn bè", "accounts": "<PERSON><PERSON> s<PERSON>ch tà<PERSON>", "accountsPlaceholder": "<PERSON><PERSON><PERSON> tài kho<PERSON>n <PERSON>alo để craw danh sách bạn bè", "delayBetweenRequests": "Thời gian delay giữa các request (giây)", "delayPlaceholder": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian delay (1-60 giây)", "submit": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "createSuccess": "T<PERSON><PERSON> chiến dịch thành công!", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo chiến dịch"}}}, "oaCampaigns": {"title": "<PERSON><PERSON><PERSON>", "description": "Tài khoản OA", "table": {"name": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "type": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "createdAt": "<PERSON><PERSON><PERSON>"}, "actions": {"start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "pause": "<PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch thành công"}, "create": {"title": "<PERSON><PERSON><PERSON> ch<PERSON>ến dịch OA mới", "subtitle": "<PERSON><PERSON><PERSON> loại chiến dịch bạn muốn tạo", "configureSubtitle": "<PERSON><PERSON><PERSON> hình chi tiết cho chiến dịch", "createButton": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch"}, "addMemberToGroup": {"title": "Thê<PERSON> thành viên vào group", "description": "Form cấu hình thêm thành viên vào group đang được phát triển."}, "sendOAMessage": {"title": "Gử<PERSON> tin nhắn OA", "description": "Form cấu hình gửi tin nhắn OA đang đượ<PERSON> phát triển."}, "sendZNSMessage": {"title": "<PERSON><PERSON><PERSON> tin nh<PERSON>n Z<PERSON>", "description": "Form cấu hình gửi tin nhắn ZNS đang đư<PERSON><PERSON> phát triển."}}}, "tag": {"name": "Tên tag", "color": "<PERSON><PERSON><PERSON>", "objectCount": "S<PERSON> lượng đối tượng", "validation": {"nameRequired": "Tên tag là bắt buộc", "colorInvalid": "<PERSON>ã màu phải có định dạng HEX (ví dụ: #FF0000)"}}, "emailMarketing": {"title": "Email", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến d<PERSON><PERSON>, mẫu <PERSON><PERSON>", "totalTemplates": "Tổng số mẫu", "manage": "<PERSON><PERSON><PERSON><PERSON>"}, "facebook": {"title": "Facebook Marketing", "description": "<PERSON><PERSON><PERSON><PERSON> lý tất cả dịch vụ <PERSON>", "pages": {"title": "Q<PERSON><PERSON>n lý Facebook Pages", "description": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi các trang <PERSON> của do<PERSON>h nghi<PERSON>p", "name": "<PERSON><PERSON><PERSON> trang", "followers": "<PERSON><PERSON><PERSON><PERSON> theo dõi", "likes": "<PERSON><PERSON><PERSON><PERSON>", "engagement": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "insights": "<PERSON><PERSON><PERSON><PERSON> kê", "managePosts": "<PERSON><PERSON><PERSON><PERSON> lý bài viết", "connect": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "ads": {"title": "Facebook Ads", "description": "<PERSON>u<PERSON>n lý và tối ưu hóa chiến dịch quảng cáo Facebook"}, "analytics": {"title": "Phân tích Facebook", "description": "<PERSON><PERSON> tích hiệu suất quảng cáo và trang Facebook"}, "instagram": {"title": "Instagram Business", "description": "<PERSON><PERSON><PERSON><PERSON> lý Instagram doanh nghiệp"}, "pixel": {"title": "Facebook Pixel", "description": "Theo <PERSON> và phân tích website"}, "messenger": {"title": "Facebook Messenger", "description": "<PERSON>n và chatbot"}, "pageMetadata": {"title": "Page Public Metadata", "description": "<PERSON><PERSON> tích dữ liệu công khai của trang Facebook", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON>m trang Facebook...", "searchButton": "<PERSON><PERSON><PERSON>", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang nào", "loading": "<PERSON><PERSON> tìm kiếm...", "pageInfo": {"title": "Thông tin trang", "name": "<PERSON><PERSON><PERSON> trang", "category": "<PERSON><PERSON>", "description": "<PERSON><PERSON>", "website": "Website", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "address": "Địa chỉ", "founded": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p", "verified": "Đ<PERSON> x<PERSON>c <PERSON>h", "checkmark": "<PERSON><PERSON><PERSON> tích xanh"}, "engagement": {"title": "<PERSON><PERSON><PERSON> độ tương tác", "likes": "<PERSON><PERSON><PERSON><PERSON>", "followers": "<PERSON><PERSON><PERSON><PERSON> theo dõi", "checkins": "<PERSON><PERSON><PERSON><PERSON> check-in", "talkingAbout": "<PERSON><PERSON> n<PERSON> về", "posts": "<PERSON><PERSON><PERSON> vi<PERSON>", "photos": "Ảnh", "videos": "Video", "events": "<PERSON><PERSON> kiện"}, "analytics": {"title": "<PERSON><PERSON> tích chi tiết", "overview": "<PERSON><PERSON><PERSON> quan", "comparison": "So sánh", "insights": "Th<PERSON>ng tin chi tiết", "trends": "<PERSON>", "demographics": "<PERSON><PERSON><PERSON><PERSON> học", "performance": "<PERSON><PERSON><PERSON>", "competitorAnalysis": "<PERSON><PERSON> tích đối thủ"}, "comparison": {"title": "So s<PERSON>h trang", "addPage": "<PERSON>h<PERSON><PERSON> trang để so sánh", "removePage": "Xóa trang", "compareMetrics": "So sánh chỉ số", "exportReport": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "noPages": "<PERSON><PERSON>a có trang nào để so sánh"}, "insights": {"title": "Báo cáo Insights", "generateReport": "Tạo báo cáo", "downloadReport": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "shareReport": "Chia sẻ báo cáo", "reportType": "Loại báo cáo", "dateRange": "<PERSON><PERSON><PERSON><PERSON> thời gian", "metrics": "Chỉ số", "summary": "<PERSON><PERSON><PERSON>", "recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị"}, "permissions": {"title": "<PERSON><PERSON><PERSON><PERSON> truy cập", "description": "<PERSON><PERSON><PERSON> năng này yêu c<PERSON>u quyền Page Public Metadata Access từ Facebook", "requestPermission": "<PERSON><PERSON><PERSON> c<PERSON> quyền", "permissionGranted": "<PERSON><PERSON> cấp quyền", "permissionPending": "<PERSON><PERSON> chờ du<PERSON>", "permissionDenied": "<PERSON><PERSON> từ chối"}, "demo": {"title": "<PERSON><PERSON> t<PERSON>h n<PERSON>ng", "description": "Khám phá các tính năng phân tích trang Facebook", "samplePages": "Trang mẫu", "tryFeature": "<PERSON><PERSON><PERSON> t<PERSON>h n<PERSON>ng"}}, "connection": {"status": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i"}}, "verified": "Đ<PERSON> x<PERSON>c <PERSON>h", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "connected": "<PERSON><PERSON> kết nối"}, "analytics": {"name": "<PERSON><PERSON><PERSON>", "impressions": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n thị", "reach": "<PERSON><PERSON><PERSON><PERSON> cận", "clicks": "<PERSON><PERSON><PERSON><PERSON>", "spend": "Chi phí", "conversions": "<PERSON><PERSON><PERSON><PERSON> đổi", "performance": "<PERSON><PERSON><PERSON>", "efficiency": "<PERSON><PERSON><PERSON> quả", "detailedReport": "<PERSON><PERSON>o c<PERSON>o chi tiết", "export": "<PERSON><PERSON><PERSON> dữ liệu", "breakdown": "<PERSON><PERSON> tích chi tiết", "exportAll": "<PERSON><PERSON><PERSON> tất cả", "refresh": "<PERSON><PERSON><PERSON>", "filters": "<PERSON><PERSON> lọc", "detailedAnalytics": "<PERSON><PERSON> tích chi tiết", "performanceTrends": "<PERSON> hiệu <PERSON>", "totalImpressions": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> hiển thị", "totalReach": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> cận", "totalClicks": "<PERSON><PERSON><PERSON> l<PERSON><PERSON>", "totalSpend": "Tổng chi phí", "totalConversions": "<PERSON><PERSON><PERSON> chuyển đổi", "avgCTR": "CTR trung bình", "avgCPC": "CPC trung bình", "avgROAS": "ROAS trung bình", "dateRange": {"title": "<PERSON><PERSON><PERSON><PERSON> thời gian", "today": "<PERSON><PERSON><PERSON> nay", "yesterday": "<PERSON><PERSON><PERSON> qua", "last7days": "7 ngày qua", "last30days": "30 ngày qua", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "lastMonth": "<PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON> chỉnh"}, "metrics": {"title": "Chỉ số", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "performance": "<PERSON><PERSON><PERSON>", "engagement": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "conversion": "<PERSON><PERSON><PERSON><PERSON> đổi", "cost": "Chi phí"}}, "performanceOverview": "<PERSON><PERSON><PERSON> quan hiệu su<PERSON>t", "quickActions": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "recentCampaigns": "<PERSON><PERSON><PERSON> dịch gần đ<PERSON>y", "conversions": "chuy<PERSON>n đổi", "actions": {"createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "createCampaignDesc": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch quảng cáo mới", "manageAudiences": "<PERSON><PERSON><PERSON><PERSON> lý đối tư<PERSON>", "manageAudiencesDesc": "Tạo và quản lý đối tượng khách hàng", "viewAnalytics": "<PERSON>em ph<PERSON> tích", "viewAnalyticsDesc": "<PERSON> hi<PERSON>u su<PERSON> chi<PERSON> dịch", "adAccountSettings": "Cài đặt tài k<PERSON>n", "adAccountSettingsDesc": "Quản lý cài đặt tài khoản quảng cáo"}, "metrics": {"totalSpend": "Tổng chi phí", "impressions": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n thị", "clicks": "<PERSON><PERSON><PERSON><PERSON>", "conversions": "<PERSON><PERSON><PERSON><PERSON> đổi", "avgCTR": "CTR trung bình", "avgCPC": "CPC trung bình", "avgROAS": "ROAS trung bình"}}, "segment": {"title": "<PERSON><PERSON> đ<PERSON>n", "description": "<PERSON><PERSON> đoạn khách hàng theo các tiêu chí khác nhau", "totalSegments": "Tổng số phân đoạn", "manage": "<PERSON><PERSON><PERSON><PERSON> lý phân đoạn", "name": "<PERSON><PERSON><PERSON> phân đoạn", "audience": "<PERSON><PERSON><PERSON>", "totalContacts": "<PERSON><PERSON> liên hệ", "addNew": "<PERSON>hê<PERSON> phân đoạn mới", "deleteSuccess": "Đã xóa phân đoạn thành công", "deleteError": "Có lỗi xảy ra khi xóa phân đoạn", "createSuccess": "<PERSON><PERSON> tạo phân đoạn thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo phân đoạn. <PERSON><PERSON> lòng thử lại sau.", "updateSuccess": "<PERSON><PERSON> cập nhật phân đoạn thành công", "updateError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật phân đoạn. <PERSON><PERSON> lòng thử lại sau.", "edit": "Chỉnh sửa phân đoạn", "selectSegmentsToDelete": "<PERSON><PERSON> lòng chọn ít nhất một phân đoạn để xóa", "bulkDeleteSuccess": "Đã xóa phân đoạn thành công", "bulkDeleteError": "Có lỗi xảy ra khi xóa phân đoạn", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa phân đoạn này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa phân đoạn đã chọn?", "noSegmentsSelected": "<PERSON><PERSON><PERSON><PERSON> có phân đoạn nào đ<PERSON><PERSON><PERSON> chọn", "customFields": "Trường tùy chỉnh", "selectCustomField": "<PERSON><PERSON><PERSON> trường...", "form": {"title": "Thông tin phân đoạn", "name": "<PERSON><PERSON><PERSON> phân đoạn", "audience": "<PERSON><PERSON><PERSON>", "totalContacts": "<PERSON><PERSON> liên hệ", "addNew": "<PERSON>hê<PERSON> phân đoạn mới", "deleteSuccess": "Đã xóa phân đoạn thành công", "deleteError": "Có lỗi xảy ra khi xóa phân đoạn", "createSuccess": "<PERSON><PERSON> tạo phân đoạn thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo phân đoạn. <PERSON><PERSON> lòng thử lại sau.", "updateSuccess": "<PERSON><PERSON> cập nhật phân đoạn thành công", "updateError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật phân đoạn. <PERSON><PERSON> lòng thử lại sau.", "edit": "Chỉnh sửa phân đoạn", "selectSegmentsToDelete": "<PERSON><PERSON> lòng chọn ít nhất một phân đoạn để xóa", "bulkDeleteSuccess": "Đã xóa phân đoạn thành công", "bulkDeleteError": "Có lỗi xảy ra khi xóa phân đoạn", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa phân đoạn này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa phân đoạn đã chọn?", "noSegmentsSelected": "<PERSON><PERSON><PERSON><PERSON> có phân đoạn nào đ<PERSON><PERSON><PERSON> chọn", "customFields": "Trường tùy chỉnh", "selectCustomField": "<PERSON><PERSON><PERSON> trường...", "form": {"title": "Thông tin phân đoạn", "name": "<PERSON><PERSON><PERSON> phân đoạn", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên phân đoạn...", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả phân đoạn...", "conditions": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "conditionsDescription": "<PERSON><PERSON><PERSON><PERSON> lập các điều kiện để phân đoạn khách hàng", "addGroup": "<PERSON><PERSON><PERSON><PERSON> nhóm điều kiện", "addCondition": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u kiện", "removeGroup": "Xóa nhóm", "removeCondition": "<PERSON><PERSON><PERSON> đi<PERSON>u kiện", "field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator": "<PERSON><PERSON> tử", "value": "<PERSON><PERSON><PERSON> trị", "valuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị...", "and": "VÀ", "or": "HOẶC", "operators": {"equals": "Bằng", "not_equals": "Không bằng", "contains": "<PERSON><PERSON><PERSON>", "not_contains": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON>", "starts_with": "<PERSON><PERSON><PERSON> đầu bằng", "ends_with": "<PERSON><PERSON><PERSON> th<PERSON>c bằng", "greater_than": "<PERSON><PERSON><PERSON>", "less_than": "Nhỏ hơn", "greater_than_or_equal": "Lớn hơn hoặc bằng", "less_than_or_equal": "Nhỏ hơn hoặc bằng", "is_empty": "<PERSON><PERSON><PERSON><PERSON>", "is_not_empty": "<PERSON><PERSON><PERSON><PERSON> trống"}, "validation": {"nameRequired": "<PERSON>ên phân đoạn là bắt buộc", "nameMinLength": "Tên phân đoạn phải có ít nhất 2 ký tự", "nameMaxLength": "Tên phân đoạn không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "conditionsRequired": "<PERSON><PERSON><PERSON> có ít nhất một điều kiện", "fieldRequired": "<PERSON>rư<PERSON><PERSON> là b<PERSON> buộc", "operatorRequired": "<PERSON><PERSON> tử là b<PERSON> buộc", "valueRequired": "<PERSON><PERSON><PERSON> trị là b<PERSON>t buộc"}, "buttons": {"save": "<PERSON><PERSON><PERSON>", "cancel": "Đ<PERSON><PERSON>", "saveTooltip": "<PERSON><PERSON><PERSON> phân đo<PERSON>n", "cancelTooltip": "Hủy và đóng form"}, "systemField": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON> thống"}}}, "campaign": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các chiến dịch marketing", "activeCampaigns": "<PERSON><PERSON><PERSON> d<PERSON>ch đang ch<PERSON>y", "manage": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch"}, "tags": {"title": "<PERSON><PERSON><PERSON><PERSON> lý thẻ", "description": "Tạo và quản lý các thẻ cho khách hàng", "totalTags": "Tổng số thẻ", "manage": "<PERSON><PERSON><PERSON><PERSON> lý thẻ", "addNew": "Thêm tag mới", "edit": "<PERSON><PERSON><PERSON> tag", "form": {"name": "Tên tag", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tag", "color": "Mã màu", "colorPlaceholder": "<PERSON><PERSON><PERSON> màu cho tag", "randomColor": "<PERSON><PERSON><PERSON> ngẫu nhiên"}, "validation": {"nameRequired": "Tên tag là bắt buộc", "colorInvalid": "<PERSON>ã màu phải có định dạng HEX (ví dụ: #FF0000)"}, "deleteSuccess": "<PERSON>ó<PERSON> tag thành công", "deleteError": "<PERSON><PERSON><PERSON> tag thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t tag thành công", "updateError": "<PERSON><PERSON><PERSON> nhật tag thất bại", "selectToDelete": "<PERSON><PERSON> lòng chọn tag để xóa", "bulkDeleteSuccess": "Xóa {{count}} tag thành công", "bulkDeleteError": "<PERSON><PERSON><PERSON> nhi<PERSON>u tag thất bại", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} tag đã chọn?"}, "tag": {"name": "Tên tag", "color": "<PERSON><PERSON><PERSON>", "objectCount": "S<PERSON> lượng đối tượng", "validation": {"nameRequired": "Tên tag là bắt buộc", "colorInvalid": "<PERSON>ã màu phải có định dạng HEX (ví dụ: #FF0000)"}}, "customField": {"configId": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của hệ thống", "add": "Thêm trường tùy chỉnh", "edit": "Chỉnh sửa trường tùy chỉnh", "dataType": "<PERSON><PERSON><PERSON> dữ liệu", "dataTypes": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Đúng/Sai", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>"}, "createSuccess": "Tạo trường tùy chỉnh thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công", "deleteSuccess": "<PERSON>óa trường tùy chỉnh thành công", "deleteMultipleSuccess": "<PERSON><PERSON> xóa {{count}} trường tùy chỉnh", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa trường tùy chỉnh này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?", "errors": {"fetchError": "Lỗi khi tải danh sách trường tùy chỉnh", "fetchDetailError": "Lỗi khi tải chi tiết trường tùy chỉnh", "createError": "Lỗi khi tạo trường tùy chỉnh", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "deleteMultipleError": "Lỗi khi xóa nhiều trường tùy chỉnh", "fetchConfigExamplesError": "Lỗi khi tải ví dụ cấu hình"}, "addForm": "Thêm trường tùy chỉnh mới", "editForm": "Chỉnh sửa trường tùy chỉnh", "component": "<PERSON><PERSON><PERSON> thành phần", "components": {"input": "Ô nhập liệu", "textarea": "Ô văn bản", "select": "<PERSON><PERSON>n", "checkbox": "<PERSON><PERSON><PERSON>", "radio": "Nút radio", "date": "<PERSON><PERSON><PERSON>", "number": "Số", "file": "<PERSON><PERSON>p tin", "multiSelect": "<PERSON><PERSON><PERSON>"}, "type": "<PERSON><PERSON><PERSON> dữ liệu", "type.string": "<PERSON><PERSON><PERSON>", "type.number": "Số", "type.boolean": "Có/<PERSON>hông", "type.date": "<PERSON><PERSON><PERSON>", "type.object": "<PERSON><PERSON><PERSON>", "type.array": "<PERSON><PERSON><PERSON>", "types": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Có/<PERSON>hông", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON><PERSON>", "string": "<PERSON><PERSON><PERSON>"}, "name": "<PERSON><PERSON><PERSON> tr<PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "placeholder": "Placeholder", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "options": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "validation": {"minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa"}, "form": {"componentRequired": "<PERSON><PERSON> lòng chọn loại thành phần", "labelRequired": "<PERSON><PERSON> lòng nh<PERSON><PERSON> nh<PERSON>n", "typeRequired": "<PERSON><PERSON> lòng chọn kiểu dữ liệu", "idRequired": "<PERSON><PERSON> lòng nhập tên trường định danh", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n hiển thị", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho trường này", "placeholderPlaceholder": "<PERSON>h<PERSON>p placeholder", "defaultValuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị mặc định", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> các tù<PERSON>, ph<PERSON> cách bằng dấu phẩy hoặc định dạng JSON", "selectOptionsPlaceholder": "Nhập giá trị theo cấu trúc: Tên hiển thị:<PERSON><PERSON><PERSON> trị, Mỗi cặp giá trị trên 1 dòng. VD:\nNam:male\n<PERSON><PERSON>:female\n<PERSON>hác:other", "booleanDefaultPlaceholder": "<PERSON><PERSON><PERSON> giá trị mặc định", "dateDefaultPlaceholder": "<PERSON><PERSON><PERSON> ngày mặc định", "description": "<PERSON><PERSON>", "labelTagRequired": "<PERSON><PERSON> lòng thêm ít nhất một nhãn", "fieldKey": "<PERSON><PERSON>ó<PERSON> trường", "fieldKeyPlaceholder": "full_name", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNamePlaceholder": "Họ và tên", "tags": "Thẻ", "tagsPlaceholder": "personal, required, contact", "fieldIdLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> hiển thị", "displayNameRequired": "<PERSON><PERSON> lòng nhập tên trường hiển thị", "labelInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter", "tagsCount": "nh<PERSON>n đã thêm", "patternSuggestions": "Gợi ý pattern phổ biến:", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "minValue": "<PERSON><PERSON><PERSON> trị tối thiểu", "maxValue": "<PERSON><PERSON><PERSON> trị tối đa", "pattern": "Mẫu kiểm tra", "options": "<PERSON><PERSON><PERSON>", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa", "placeholder": "Placeholder", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "labels": "<PERSON><PERSON>ã<PERSON>", "showAdvancedSettings": "<PERSON><PERSON>n thị cài đặt nâng cao", "validation": {"fieldKeyRequired": "Khóa trường là bắt buộc", "fieldKeyPattern": "<PERSON><PERSON>óa trường chỉ được chứa chữ cái, số, gạch dưới và gạch ngang", "displayNameRequired": "<PERSON><PERSON><PERSON> hiển thị là bắt buộc", "dataTypeRequired": "Loại dữ liệu là bắt buộc"}}, "createError": "Lỗi khi tạo trường tùy chỉnh", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "loadError": "Lỗi khi tải trường tùy chỉnh", "booleanValues": {"true": "<PERSON><PERSON>", "false": "K<PERSON>ô<PERSON>"}, "patterns": {"email": "Email", "phoneVN": "Số điện thoại VN", "phoneIntl": "<PERSON><PERSON> điện thoại quốc tế", "postalCodeVN": "<PERSON><PERSON> b<PERSON><PERSON> ch<PERSON> VN", "lettersOnly": "Chỉ chữ cái", "numbersOnly": "Chỉ số", "alphanumeric": "Chữ và số", "noSpecialChars": "<PERSON><PERSON><PERSON><PERSON> có ký tự đặc biệt", "url": "URL", "ipv4": "IPv4", "strongPassword": "<PERSON><PERSON><PERSON><PERSON> mạnh", "vietnameseName": "<PERSON><PERSON><PERSON> (c<PERSON>)", "studentId": "<PERSON><PERSON> sinh viên", "nationalId": "CMND/CCCD", "taxCode": "<PERSON><PERSON> số thuế", "dateFormat": "<PERSON>ày (dd/mm/yyyy)", "timeFormat": "Giờ (hh:mm)", "hexColor": "Hex color", "base64": "Base64", "uuid": "UUID", "filename": "Tên file", "urlSlug": "Slug URL", "variableName": "<PERSON><PERSON><PERSON>", "creditCard": "Số thẻ tín dụng", "qrCode": "Mã QR", "gpsCoordinate": "Tọa độ GPS", "rgbColor": "Mã màu RGB", "domain": "<PERSON><PERSON><PERSON>", "decimal": "<PERSON><PERSON> thập phân", "barcode": "Mã vạch"}, "bulkDeleteSuccess": "<PERSON><PERSON> xóa thành công {{count}} trường tùy chỉnh", "bulkDeleteError": "C<PERSON> lỗi xảy ra khi xóa trường tùy chỉnh", "selectedItems": "<PERSON><PERSON> chọn {{count}} mục", "totalFields": "Tổng số trường tùy chỉnh", "manage": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả"}, "reports": {"title": "Báo cáo", "description": "<PERSON><PERSON> c<PERSON>c báo cáo về hoạt động marketing", "totalReports": "Tổng số báo cáo", "view": "<PERSON><PERSON> b<PERSON>o c<PERSON>o"}, "templateEmail": {"title": "Mẫu email", "name": "<PERSON><PERSON>n mẫu", "subject": "Ti<PERSON><PERSON> đ<PERSON> email", "statuses": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "draft": "<PERSON><PERSON><PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}}, "emailMarketing": {"title": "Email", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến d<PERSON><PERSON>, mẫu <PERSON><PERSON>", "totalTemplates": "Tổng số mẫu", "manage": "<PERSON><PERSON><PERSON><PERSON>"}, "smsMarketing": {"title": "SMS", "description": "<PERSON><PERSON><PERSON> và quản lý chiến dịch <PERSON>", "totalCampaigns": "<PERSON><PERSON><PERSON> chi<PERSON>n d<PERSON>ch", "manage": "<PERSON><PERSON><PERSON><PERSON>", "comingSoon": "<PERSON><PERSON>h năng SMS Marketing đang được phát triển. Vui lòng quay lại sau!"}, "googleAds": {"title": "Google Ads", "description": "<PERSON><PERSON><PERSON> hợp và quản lý chiến dịch Google Ads từ hệ thống", "totalAccounts": "<PERSON><PERSON><PERSON>", "manage": "Quản lý Google Ads", "comingSoon": "<PERSON><PERSON><PERSON> năng đang đượ<PERSON> phát triển. Vui lòng quay lại sau!", "connectAccount": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n", "connectAccountDescription": "Form kết nối tài khoản sẽ được phát triển ở giai đoạn tiếp theo.", "name": "<PERSON><PERSON><PERSON> tà<PERSON>", "customerId": "Customer ID", "viewCampaigns": "<PERSON><PERSON> d<PERSON>", "accountCreated": "T<PERSON>i k<PERSON>n Google Ads đã đư<PERSON>c tạo thành công", "accountCreateError": "Lỗi khi tạo tài kho<PERSON>n Google Ads", "accountUpdated": "<PERSON><PERSON><PERSON> k<PERSON>n Google Ads đã đ<PERSON><PERSON><PERSON> cập nhật", "accountUpdateError": "Lỗi khi cập nhật tà<PERSON> k<PERSON>n Google Ads", "accountDeleted": "Tài khoản Google Ads đã đư<PERSON>c xóa", "accountDeleteError": "Lỗi khi xóa tài khoản Google Ads", "accounts": "Tài khoản Google Ads", "campaigns": "Chiến dịch Google Ads", "campaignName": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "campaignId": "<PERSON> chi<PERSON>n d<PERSON>ch", "campaignType": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "budget": "<PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>n dịch mới", "editCampaign": "Chỉnh s<PERSON>a chiến dịch", "campaignCreated": "<PERSON><PERSON><PERSON> dịch Google Ads đã đư<PERSON><PERSON> tạo thành công", "campaignCreateError": "Lỗi khi tạo chiến dịch Google Ads", "campaignUpdated": "<PERSON><PERSON><PERSON> d<PERSON>ch Google Ads đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "campaignUpdateError": "Lỗi khi cập nhật chiến dịch Google Ads", "campaignDeleted": "<PERSON>ến dịch Google Ads đã đ<PERSON><PERSON><PERSON> xóa", "campaignDeleteError": "Lỗi khi xóa chiến dịch Google Ads", "campaignEnabled": "<PERSON>ến dịch Google Ads đã đ<PERSON><PERSON><PERSON> kích ho<PERSON>t", "campaignPaused": "<PERSON><PERSON><PERSON> dịch Google Ads đã đ<PERSON><PERSON><PERSON> tạm dừng", "campaignStatusUpdateError": "Lỗi khi cập nhật trạng thái chiến dịch Google Ads", "campaignTypes": {"SEARCH": "<PERSON><PERSON><PERSON>", "DISPLAY": "<PERSON><PERSON><PERSON> thị", "VIDEO": "Video", "SHOPPING": "<PERSON><PERSON>", "APP": "Ứng dụng", "PERFORMANCE_MAX": "<PERSON><PERSON><PERSON> su<PERSON>t tối đa"}, "campaignStatuses": {"ENABLED": "<PERSON><PERSON> ho<PERSON>t động", "PAUSED": "<PERSON><PERSON><PERSON>", "REMOVED": "Đã xóa", "DRAFT": "<PERSON><PERSON><PERSON>"}, "tabs": {"accounts": "<PERSON><PERSON><PERSON>", "campaigns": "<PERSON><PERSON><PERSON>", "performance": "<PERSON><PERSON><PERSON>"}}, "zaloAds": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý quảng c<PERSON><PERSON>", "overview": {"title": "Zalo Ads Overview", "description": "<PERSON>u<PERSON><PERSON> lý và theo dõi hiệu suất quảng c<PERSON><PERSON>", "manageAccounts": "<PERSON><PERSON><PERSON><PERSON> lý tài <PERSON>n", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "stats": {"totalCampaigns": "<PERSON><PERSON><PERSON> chi<PERSON>n d<PERSON>ch", "active": "<PERSON>ang ch<PERSON>y", "totalSpend": "Tổng chi phí", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "totalClicks": "Tổng clicks", "roas": "ROAS", "returnOnAdSpend": "Return on Ad Spend"}, "quickActions": {"createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>n dịch mới", "createCampaignDesc": "<PERSON><PERSON><PERSON> đầu quảng c<PERSON><PERSON> trên <PERSON>", "viewCampaigns": "<PERSON><PERSON> d<PERSON>", "viewCampaignsDesc": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch hiện tại", "viewReports": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "viewReportsDesc": "<PERSON><PERSON> tích hiệu su<PERSON>t quảng cáo"}, "recentPerformance": {"title": "<PERSON><PERSON><PERSON> su<PERSON>t gần đây", "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "impressions": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n thị", "clicks": "<PERSON><PERSON><PERSON><PERSON>", "conversions": "<PERSON><PERSON><PERSON><PERSON> đổi"}, "gettingStarted": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>u v<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> chiến dịch quảng cáo đầu tiên để tiếp cận hàng triệu người dùng <PERSON>", "createFirst": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch đầu tiên"}}, "accounts": {"title": "Zalo Ads Accounts", "description": "<PERSON>u<PERSON>n lý tài khoản quảng c<PERSON><PERSON>", "connectAccount": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n", "viewAccount": "<PERSON>em chi tiết tài <PERSON>n", "stats": {"totalAccounts": "<PERSON><PERSON><PERSON> tài <PERSON>n", "connected": "<PERSON><PERSON> kết nối", "activeAccounts": "<PERSON><PERSON><PERSON>n ho<PERSON>t động", "readyToUse": "Sẵn sàng sử dụng", "totalBalance": "Tổng số dư", "availableBalance": "Số dư khả dụng", "pendingAccounts": "Chờ x<PERSON>c minh", "needsVerification": "<PERSON><PERSON><PERSON> x<PERSON>h"}, "table": {"account": "<PERSON><PERSON><PERSON>", "business": "<PERSON><PERSON><PERSON>", "noBusiness": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>t", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "balance": "Số dư", "timeZone": "<PERSON><PERSON><PERSON> giờ", "updated": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "suspended": "<PERSON><PERSON> tạm ng<PERSON>ng", "pending": "Chờ x<PERSON>c minh"}, "connect": {"title": "<PERSON><PERSON><PERSON> n<PERSON>i tài k<PERSON>", "description": "<PERSON><PERSON>t nối tài k<PERSON><PERSON>n Zalo <PERSON>s để bắt đầu quảng cáo", "instructions": {"title": "Hướng dẫn kết n<PERSON>i <PERSON>", "description": "<PERSON><PERSON><PERSON> theo các bước sau để kết nối tài k<PERSON><PERSON>n Zalo <PERSON>", "step1": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Manager", "description": "<PERSON><PERSON><PERSON> nhập v<PERSON><PERSON> tà<PERSON> k<PERSON>n <PERSON> của bạn", "action": "Mở <PERSON>alo Ads"}, "step2": {"title": "Lấy Access Token", "description": "<PERSON><PERSON>ấ<PERSON> vào nút bên dưới để lấy access token", "action": "<PERSON><PERSON><PERSON>"}, "step3": {"title": "<PERSON><PERSON><PERSON><PERSON> thông tin", "description": "Sao chép access token và refresh token vào form bên d<PERSON><PERSON>i"}, "note": {"title": "<PERSON><PERSON><PERSON> ý quan trọng", "description": "Access token có thời hạn sử dụng. <PERSON><PERSON> thống sẽ tự động gia hạn token khi cần thiết."}, "continue": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"title": "Thông tin tài k<PERSON>n", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin để kết nối tài k<PERSON><PERSON>n <PERSON>", "accountName": {"label": "<PERSON><PERSON><PERSON> tà<PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên tà<PERSON>n"}, "businessName": {"label": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên do<PERSON>h <PERSON> (t<PERSON><PERSON> ch<PERSON>)"}, "accessToken": {"label": "Access Token", "placeholder": "Nhập access token từ Zalo Ads"}, "refreshToken": {"label": "Refresh <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> refresh token từ Zalo <PERSON>s"}, "connecting": "<PERSON><PERSON> kết nối...", "connect": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n", "error": {"title": "Lỗi kết nối", "message": "<PERSON><PERSON><PERSON><PERSON> thể kết nối tài k<PERSON>n. <PERSON><PERSON> lòng kiểm tra lại thông tin."}, "success": {"title": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>n <PERSON>alo <PERSON>s đã đư<PERSON>c kết nối thành công."}}}}, "campaigns": {"title": "Zalo Ads Campaigns", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch quảng c<PERSON><PERSON>", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "viewPerformance": "<PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON><PERSON><PERSON>", "stats": {"totalCampaigns": "<PERSON><PERSON><PERSON> chi<PERSON>n d<PERSON>ch", "allTime": "<PERSON><PERSON><PERSON> cả thời gian", "activeCampaigns": "<PERSON><PERSON>", "currentlyRunning": "<PERSON><PERSON><PERSON> đang ch<PERSON>y", "totalSpend": "Tổng chi phí", "totalBudget": "<PERSON><PERSON><PERSON> ng<PERSON> s<PERSON>ch", "performance": "<PERSON><PERSON><PERSON>", "averageRoas": "ROAS trung bình"}, "table": {"campaign": "<PERSON><PERSON><PERSON>", "objective": "<PERSON><PERSON><PERSON> ti<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "budget": "<PERSON><PERSON>", "bidStrategy": "<PERSON><PERSON>u giá", "placements": "<PERSON><PERSON> trí", "updated": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "status": {"active": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON>", "pending": "<PERSON>ờ <PERSON>", "rejected": "<PERSON><PERSON> chối"}, "create": {"title": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> chiến dịch quảng cáo mới trên <PERSON>"}, "edit": {"title": "Chỉnh s<PERSON>a chiến dịch"}}, "reports": {"title": "Zalo Ads Reports", "description": "<PERSON><PERSON> tích hiệu su<PERSON>t quảng c<PERSON><PERSON>", "export": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "selectDateRange": "<PERSON><PERSON><PERSON> k<PERSON> thời gian", "dateRange": {"today": "<PERSON><PERSON><PERSON> nay", "yesterday": "<PERSON><PERSON><PERSON> qua", "last7days": "7 ngày qua", "last30days": "30 ngày qua", "last90days": "90 ngày qua"}, "metrics": {"impressions": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n thị", "clicks": "<PERSON><PERSON><PERSON><PERSON>", "conversions": "<PERSON><PERSON><PERSON><PERSON> đổi", "spend": "Chi phí", "vsLastPeriod": "so v<PERSON><PERSON> kỳ trước", "conversionRate": "Tỷ lệ chuyển đổi"}, "performance": {"title": "<PERSON><PERSON><PERSON> su<PERSON>t theo thời gian", "description": "<PERSON> c<PERSON>c chỉ số quan trọng"}, "period": {"day": "<PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON><PERSON>"}, "chart": {"placeholder": "<PERSON>iểu đồ hiệu suất sẽ được hiển thị ở đây", "note": "<PERSON><PERSON><PERSON> hợp với thư viện chart sẽ được thêm vào sau"}, "table": {"title": "<PERSON> tiết hi<PERSON>u su<PERSON>t", "date": "<PERSON><PERSON><PERSON>", "impressions": "<PERSON><PERSON><PERSON> thị", "clicks": "<PERSON><PERSON><PERSON><PERSON>", "ctr": "CTR", "conversions": "<PERSON><PERSON><PERSON><PERSON> đổi", "spend": "Chi phí", "cpc": "CPC"}, "insights": {"title": "Th<PERSON>ng tin chi tiết", "bestPerforming": "<PERSON><PERSON><PERSON> su<PERSON>t tốt nhất", "bestPerformingDesc": "Ngày 26/01 có hiệu suất tốt nhất với 1,320 clicks và 65 conversions.", "recommendation": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị", "recommendationDesc": "Tăng ngân sách cho các chiến dịch có ROAS > 3.0 để tối ưu hóa hiệu quả."}}}, "email": {"title": "Email", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch email marketing và templates", "overview": {"title": "Email", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch email marketing và templates", "createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "manageTemplates": "<PERSON><PERSON><PERSON><PERSON>", "viewReports": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "stats": {"totalCampaigns": "<PERSON><PERSON><PERSON> chi<PERSON>n d<PERSON>ch", "activeCampaigns": "<PERSON><PERSON>", "totalTemplates": "Tổng templates", "readyToUse": "Sẵn sàng sử dụng", "emailsSent": "<PERSON><PERSON> g<PERSON>", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "openRate": "Tỷ lệ mở", "averageRate": "Tỷ lệ trung bình"}, "quickActions": {"createCampaign": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch email", "createCampaignDesc": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> ch<PERSON>n dịch email marketing mới", "manageTemplates": "<PERSON><PERSON><PERSON><PERSON>", "manageTemplatesDesc": "Tạo và chỉnh sửa email templates", "viewAnalytics": "<PERSON>em ph<PERSON> tích", "viewAnalyticsDesc": "<PERSON> hi<PERSON> email campaigns"}, "recentActivity": {"title": "<PERSON><PERSON><PERSON> động gần đây", "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "campaignSent": "<PERSON><PERSON><PERSON> dịch đ<PERSON> g<PERSON>i", "templateCreated": "Template <PERSON><PERSON><PERSON><PERSON>", "reportGenerated": "<PERSON><PERSON>o c<PERSON><PERSON><PERSON><PERSON> t<PERSON>o"}, "gettingStarted": {"title": "Bắt đầu với Email Marketing", "description": "Tạo template email đầu tiên để bắt đầu chiến dịch marketing", "createTemplate": "Tạo template đầu tiên"}}, "templates": {"title": "Email Templates", "description": "Tạo và quản lý các mẫu email marketing", "createTemplate": "<PERSON><PERSON>o <PERSON>late", "stats": {"totalTemplates": "Tổng Templates", "newTemplates": "+3 template mới", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "readyToUse": "Sẵn sàng sử dụng", "draft": "<PERSON><PERSON><PERSON>", "incomplete": "<PERSON><PERSON><PERSON> ho<PERSON>n thành", "testSent": "Đã gửi test", "thisWeek": "<PERSON><PERSON><PERSON>"}, "table": {"template": "Template", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "tags": "Tags", "variables": "<PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "draft": "<PERSON><PERSON><PERSON>", "archived": "<PERSON><PERSON> lưu trữ"}, "type": {"welcome": "<PERSON><PERSON><PERSON> m<PERSON>ng", "promotional": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "newsletter": "Newsletter", "transactional": "<PERSON><PERSON><PERSON>"}, "preview": {"title": "<PERSON><PERSON>", "variables": "Biến template:", "required": "<PERSON><PERSON><PERSON> b<PERSON>"}, "create": {"title": "<PERSON><PERSON><PERSON> Email Te<PERSON>", "description": "Tạo template email marketing mới"}, "edit": {"title": "Chỉnh s<PERSON>a <PERSON>", "description": "Chỉnh sửa template email marketing"}, "form": {"editors": {"title": "<PERSON><PERSON><PERSON> Editor", "emailBuilder": {"name": "Email Builder", "description": "<PERSON>r<PERSON><PERSON> tạo email kéo thả trực quan"}, "richTextEditor": {"name": "Rich Text Editor", "description": "<PERSON>r<PERSON><PERSON> soạn thảo văn bản WYSIWYG"}, "code": {"name": "HTML Code", "description": "<PERSON><PERSON><PERSON><PERSON> soạn thảo HTML thô"}}, "richTextEditor": {"label": "Nội dung Rich Text", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nội dung email..."}, "basicInfo": {"title": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản"}, "name": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON> dụ: Newsletter tháng 1, <PERSON><PERSON><PERSON><PERSON><PERSON> mãi Black Friday..."}, "type": {"label": "Lo<PERSON>i Template", "placeholder": "<PERSON><PERSON><PERSON> lo<PERSON> template"}, "subject": {"label": "<PERSON>i<PERSON><PERSON> đ<PERSON>ail", "placeholder": "Ví dụ: 🎉 Khuyến mãi đặc biệt dành cho bạn!"}, "content": {"title": "<PERSON>ội dung Email", "designMode": "Design", "codeMode": "HTML"}, "htmlContent": {"label": "HTML Content", "placeholder": "<!DOCTYPE html>\\n<html>\\n<head>\\n  <title>Email Template</title>\\n</head>\\n<body>\\n  <h1><PERSON>n chào {customer_name}!</h1>\\n  <p>Cảm ơn bạn đã đăng ký newsletter của chúng tôi.</p>\\n</body>\\n</html>"}, "textContent": {"label": "Text Content (<PERSON><PERSON><PERSON>)", "placeholder": "<PERSON>n chào {customer_name}!\\n\\nCảm ơn bạn đã đăng ký newsletter của chúng tôi..."}, "variables": {"title": "<PERSON><PERSON><PERSON><PERSON> động", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "typePlaceholder": "<PERSON><PERSON><PERSON>", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "defaultValuePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON> h<PERSON>ng"}, "tags": {"title": "Tags", "label": "Tags", "placeholder": "Nhập tag và nhấn Enter"}, "submitButton": "<PERSON><PERSON>o <PERSON>late", "instructions": {"title": "<PERSON><PERSON>ớng dẫn tạo Email Template", "description": "Sử dụng Email Builder để tạo nội dung email đẹp mắt. Thêm biến động bằng cú pháp {variable_name}. Preview text sẽ hiển thị trong inbox preview."}, "validation": {"nameRequired": "Tên template là bắt buộc", "subjectRequired": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> email là bắ<PERSON> buộc", "contentRequired": "Nội dung HTML là bắt buộc"}}}, "campaigns": {"syncStatus": {"tooltip": "<PERSON><PERSON><PERSON> nhật trạng thái"}, "form": {"name": {"label": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "placeholder": "Ví dụ: <PERSON><PERSON><PERSON><PERSON><PERSON> mãi Black Friday 2024"}, "description": {"label": "<PERSON><PERSON> t<PERSON> chiến dịch", "placeholder": "<PERSON><PERSON> tả ngắn gọn về chiến dịch nà<PERSON>..."}, "audience": {"totalCount": "Tổng số ng<PERSON>ờ<PERSON> nh<PERSON>n:", "calculating": "<PERSON><PERSON> t<PERSON> toán...", "table": {"title": "<PERSON><PERSON> s<PERSON>ch ng<PERSON><PERSON>n", "subtitle": "<PERSON><PERSON> s<PERSON>ch audience từ segments đ<PERSON> chọn", "selectedSubtitle": "<PERSON><PERSON> s<PERSON>ch <PERSON> đ<PERSON> ch<PERSON>n", "name": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "createdAt": "<PERSON><PERSON><PERSON>", "noName": "<PERSON><PERSON><PERSON><PERSON> có tên", "noEmail": "K<PERSON><PERSON>ng có email", "noPhone": "Không có SĐT"}}}, "customer": {"table": {"title": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "subtitle": "<PERSON><PERSON> s<PERSON>ch khách hàng đã chọn", "name": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "noName": "<PERSON><PERSON><PERSON><PERSON> có tên", "noEmail": "K<PERSON><PERSON>ng có email", "noPhone": "Không có SĐT"}}}}, "articles": {"title": "<PERSON><PERSON><PERSON><PERSON> lý bài viết", "description": "Tạo và quản lý bài viết marketing", "list": "<PERSON><PERSON> s<PERSON>ch b<PERSON>i vi<PERSON>t", "listDescription": "<PERSON><PERSON><PERSON><PERSON> lý tất cả bài viết marketing của bạn", "create": "<PERSON><PERSON><PERSON> b<PERSON>i viết", "edit": "Chỉnh sửa bài viết", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t bà<PERSON> viết", "publish": "<PERSON><PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON> tr<PERSON>", "articleTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "titlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề bài viết", "excerpt": "<PERSON><PERSON><PERSON><PERSON> dẫn", "excerptPlaceholder": "<PERSON><PERSON><PERSON><PERSON> trích dẫn ngắn gọn", "author": "Tác g<PERSON>", "authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tác g<PERSON>", "content": "<PERSON><PERSON>i dung", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nội dung bài viết", "imageUrl": "<PERSON><PERSON><PERSON> đạ<PERSON> di<PERSON>n", "imageUrlPlaceholder": "Nhập URL hình ảnh hoặc tải lên", "tags": "Thẻ", "tagsPlaceholder": "<PERSON><PERSON>ập thẻ và nhấn Enter", "relatedArticles": "<PERSON> liên quan", "selectRelatedArticles": "<PERSON><PERSON><PERSON> tối đa 6 bài viết liên quan", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "metadata": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "stats": "<PERSON><PERSON><PERSON><PERSON> kê", "views": "<PERSON><PERSON><PERSON><PERSON> xem", "likes": "<PERSON><PERSON><PERSON><PERSON>", "status": {"draft": "<PERSON><PERSON><PERSON>", "published": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "archived": "<PERSON><PERSON> lưu trữ"}, "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "deleteConfirm": "<PERSON>óa b<PERSON>i vi<PERSON>t", "deleteDescription": "Bạn có chắc chắn muốn xóa bài viết này không?", "bulkDeleteConfirm": "<PERSON>óa {{count}} b<PERSON><PERSON> vi<PERSON>t", "bulkDeleteDescription": "Bạn có chắc chắn muốn xóa các bài viết đã chọn không?", "validation": {"titleRequired": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "authorRequired": "<PERSON>ác g<PERSON><PERSON> là b<PERSON> bu<PERSON>c", "contentRequired": "<PERSON>ội dung là bắt buộc", "maxRelatedArticles": "<PERSON><PERSON><PERSON> đa 6 bài viết liên quan"}}, "resources": {"title": "T<PERSON>i <PERSON> Marketing", "description": "<PERSON><PERSON> sưu tập công cụ và mẫu để hỗ trợ hoạt động marketing của bạn", "tabs": {"systemLibrary": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> hệ thống", "personalLibrary": "<PERSON><PERSON><PERSON> vi<PERSON>n của tôi"}, "categories": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "emailTemplates": "Mẫu Email", "znsTemplates": "Mẫu ZNS", "analytics": "<PERSON><PERSON>ng cụ phân tích", "automation": "<PERSON><PERSON> động hóa", "content": "<PERSON><PERSON>i dung"}, "emailTemplates": {"title": "Mẫu Email Marketing", "description": "<PERSON><PERSON> sưu tập mẫu email marketing chuyê<PERSON> nghiệ<PERSON>, sẵn sàng sử dụng", "examples": {"title": "Mẫu Email Marketing", "description": "<PERSON><PERSON> sưu tập mẫu email marketing chuyên nghiệp"}, "categories": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "newsletter": "Newsletter", "promotional": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "welcome": "<PERSON><PERSON><PERSON> m<PERSON>ng", "transactional": "<PERSON><PERSON><PERSON>", "abandonedCart": "Giỏ hàng bỏ quên", "followUp": "<PERSON>"}}, "znsTemplates": {"title": "Mẫu <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> viện mẫu tin nhắn ZNS đa dạng cho các mục đích khác nhau"}, "analytics": {"campaign": {"title": "<PERSON><PERSON> tích chiến dịch", "description": "<PERSON><PERSON>ng cụ phân tích hiệu quả chiến dịch marketing"}, "roi": {"title": "Tính ROI", "description": "Công cụ tính toán ROI cho các hoạt động marketing"}}, "analyticsTools": {"campaignAnalyzer": {"title": "<PERSON><PERSON> tích chiến dịch", "description": "<PERSON><PERSON>ng cụ phân tích hiệu quả chiến dịch marketing"}}}, "videos": {"title": "<PERSON><PERSON><PERSON><PERSON> video", "description": "Tạo và quản lý video marketing", "list": "<PERSON><PERSON> video", "listDescription": "<PERSON><PERSON><PERSON><PERSON> lý tất cả video marketing của bạn", "create": "Tạo video", "edit": "Chỉnh sửa video", "update": "<PERSON><PERSON><PERSON> video", "publish": "<PERSON><PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON> tr<PERSON>", "videoTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "titlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề video", "videoDescription": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả video", "url": "URL Video", "urlPlaceholder": "Nhập URL video", "thumbnailUrl": "<PERSON><PERSON>nh thu nhỏ", "thumbnailUrlPlaceholder": "Nhập URL hình thu nhỏ", "duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "durationDescription": "Định dạng: 5:30 hoặc 330 (giây)", "category": "<PERSON><PERSON>", "selectCategory": "<PERSON><PERSON><PERSON> danh mục", "tags": "Thẻ", "tagsPlaceholder": "<PERSON><PERSON>ập thẻ và nhấn Enter", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "videoDetails": "<PERSON> ti<PERSON> video", "metadata": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "stats": "<PERSON><PERSON><PERSON><PERSON> kê", "views": "<PERSON><PERSON><PERSON><PERSON> xem", "likes": "<PERSON><PERSON><PERSON><PERSON>", "status": {"draft": "<PERSON><PERSON><PERSON>", "published": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "archived": "<PERSON><PERSON> lưu trữ"}, "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "deleteConfirm": "Xóa video", "deleteDescription": "Bạn có chắc chắn muốn xóa video này không?", "bulkDeleteConfirm": "Xóa {{count}} video", "bulkDeleteDescription": "Bạn có chắc chắn muốn xóa các video đã chọn không?", "validation": {"titleRequired": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "urlRequired": "URL video là bắt buộc", "invalidUrl": "URL không hợp lệ", "invalidDuration": "<PERSON>hờ<PERSON> lư<PERSON>ng không hợp lệ"}}, "seedingGroups": {"form": {"create": {"title": "<PERSON><PERSON>u h<PERSON>nh Seeding Group"}}}}