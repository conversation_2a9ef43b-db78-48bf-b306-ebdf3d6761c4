import React, { useRef, useState, useEffect } from 'react';
import { List, Plus, Minus, AlignLeft, AlignCenter, AlignRight } from 'lucide-react';

interface ListInlineEditorProps {
  content: string;
  listType?: 'ul' | 'ol';
  listStyle?: string;
  textAlign?: 'left' | 'center' | 'right';
  lineHeight?: number;
  onUpdate: (updates: {
    content?: string;
    listType?: 'ul' | 'ol';
    listStyle?: string;
    textAlign?: 'left' | 'center' | 'right';
    lineHeight?: number;
  }) => void;
  onClickOutside?: () => void;
  className?: string;
}

const ListInlineEditor: React.FC<ListInlineEditorProps> = ({
  content,
  listType = 'ul',
  listStyle = 'disc',
  textAlign = 'left',
  lineHeight = 1.6,
  onUpdate,
  onClickOutside,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [localContent, setLocalContent] = useState(content);
  const [localListType, setLocalListType] = useState(listType);
  const [localListStyle, setLocalListStyle] = useState(listStyle);
  const [localTextAlign, setLocalTextAlign] = useState(textAlign);
  const [localLineHeight, setLocalLineHeight] = useState(lineHeight);
  const [items, setItems] = useState<string[]>([]);

  console.log('ListInlineEditor rendered with:', {
    content,
    listType,
    listStyle,
    textAlign,
    lineHeight,
    hasOnClickOutside: !!onClickOutside,
  });

  // Parse content to items on mount
  useEffect(() => {
    if (content) {
      // Extract list items from HTML content
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, 'text/html');
      const listItems = doc.querySelectorAll('li');
      const itemTexts = Array.from(listItems).map(li => li.textContent || '');
      setItems(itemTexts.length > 0 ? itemTexts : ['Mục danh sách']);
    } else {
      setItems(['Mục danh sách']);
    }
  }, [content]);

  useEffect(() => {
    if (!onClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        // Don't close if clicking on related elements
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.list-inline-editor') ||
            element.closest('button') ||
            element.closest('input') ||
            element.closest('select') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'INPUT');

        if (!isToolbarClick) {
          onClickOutside();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClickOutside]);

  const generateListHTML = (items: string[]) => {
    const listItems = items.map(item => `<li>${item}</li>`).join('');
    return `<${localListType} style="list-style-type: ${localListStyle}; text-align: ${localTextAlign}; line-height: ${localLineHeight};">${listItems}</${localListType}>`;
  };

  const handleListTypeChange = (newType: 'ul' | 'ol') => {
    setLocalListType(newType);
    // Update list style based on type
    const newStyle = newType === 'ul' ? 'disc' : 'decimal';
    setLocalListStyle(newStyle);
    const newContent = generateListHTML(items);
    setLocalContent(newContent);
    onUpdate({ listType: newType, listStyle: newStyle, content: newContent });
  };

  const handleListStyleChange = (newStyle: string) => {
    setLocalListStyle(newStyle);
    const newContent = generateListHTML(items);
    setLocalContent(newContent);
    onUpdate({ listStyle: newStyle, content: newContent });
  };

  const handleTextAlignChange = (newAlign: 'left' | 'center' | 'right') => {
    setLocalTextAlign(newAlign);
    const newContent = generateListHTML(items);
    setLocalContent(newContent);
    onUpdate({ textAlign: newAlign, content: newContent });
  };

  const handleLineHeightChange = (newHeight: number) => {
    setLocalLineHeight(newHeight);
    const newContent = generateListHTML(items);
    setLocalContent(newContent);
    onUpdate({ lineHeight: newHeight, content: newContent });
  };

  const handleItemChange = (index: number, newValue: string) => {
    const newItems = [...items];
    newItems[index] = newValue;
    setItems(newItems);
    const newContent = generateListHTML(newItems);
    setLocalContent(newContent);
    onUpdate({ content: newContent });
  };

  const addItem = () => {
    const newItems = [...items, 'Mục mới'];
    setItems(newItems);
    const newContent = generateListHTML(newItems);
    setLocalContent(newContent);
    onUpdate({ content: newContent });
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      const newItems = items.filter((_, i) => i !== index);
      setItems(newItems);
      const newContent = generateListHTML(newItems);
      setLocalContent(newContent);
      onUpdate({ content: newContent });
    }
  };

  const ulStyles = [
    { value: 'disc', label: '• Disc' },
    { value: 'circle', label: '○ Circle' },
    { value: 'square', label: '■ Square' },
    { value: 'none', label: 'None' },
  ];

  const olStyles = [
    { value: 'decimal', label: '1. Numbers' },
    { value: 'lower-alpha', label: 'a. Lower Alpha' },
    { value: 'upper-alpha', label: 'A. Upper Alpha' },
    { value: 'lower-roman', label: 'i. Lower Roman' },
    { value: 'upper-roman', label: 'I. Upper Roman' },
  ];

  return (
    <div
      ref={editorRef}
      className={`list-inline-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* List Type Toggle */}
        <div className="flex items-center gap-1">
          <button
            className={`px-2 py-1 text-xs rounded transition-colors ${
              localListType === 'ul'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleListTypeChange('ul')}
            title="Unordered List"
          >
            UL
          </button>
          <button
            className={`px-2 py-1 text-xs rounded transition-colors ${
              localListType === 'ol'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleListTypeChange('ol')}
            title="Ordered List"
          >
            OL
          </button>
        </div>

        {/* List Style Selector */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Style:</label>
          <select
            value={localListStyle}
            onChange={e => handleListStyleChange(e.target.value)}
            className="text-xs border border-gray-300 dark:border-gray-600 rounded px-1 py-0.5"
          >
            {(localListType === 'ul' ? ulStyles : olStyles).map(style => (
              <option key={style.value} value={style.value}>
                {style.label}
              </option>
            ))}
          </select>
        </div>

        {/* Line Height */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Line:</label>
          <input
            type="number"
            value={localLineHeight}
            onChange={e => handleLineHeightChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="1"
            max="3"
            step="0.1"
          />
        </div>

        {/* Divider */}
        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500"></div>

        {/* Alignment Buttons */}
        <div className="flex items-center gap-1">
          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'left'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('left')}
            title="Align Left"
          >
            <AlignLeft size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'center'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('center')}
            title="Align Center"
          >
            <AlignCenter size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'right'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('right')}
            title="Align Right"
          >
            <AlignRight size={12} />
          </button>
        </div>

        {/* Add Item Button */}
        <button
          className="flex items-center gap-1 px-2 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-xs"
          onClick={addItem}
          title="Add Item"
        >
          <Plus size={12} />
          Add
        </button>
      </div>

      {/* List Items Editor */}
      <div className="p-3 bg-white dark:bg-gray-800">
        <div className="space-y-2">
          {items.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="text-xs text-gray-500 w-6">
                {localListType === 'ol' ? `${index + 1}.` : '•'}
              </span>
              <input
                type="text"
                value={item}
                onChange={e => handleItemChange(index, e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
                placeholder="Nhập nội dung mục"
              />
              {items.length > 1 && (
                <button
                  className="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                  onClick={() => removeItem(index)}
                  title="Remove Item"
                >
                  <Minus size={12} />
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ListInlineEditor;
