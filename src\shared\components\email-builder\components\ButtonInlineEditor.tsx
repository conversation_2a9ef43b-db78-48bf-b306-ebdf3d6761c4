import React, { useRef, useState, useEffect } from 'react';
import { Type, Palette, Link, CornerDownRight } from 'lucide-react';

interface ButtonInlineEditorProps {
  text: string;
  url: string;
  backgroundColor?: string;
  color?: string;
  fontSize?: number;
  paddingX?: number;
  paddingY?: number;
  borderRadius?: number;
  onUpdate: (updates: {
    text?: string;
    url?: string;
    backgroundColor?: string;
    color?: string;
    fontSize?: number;
    paddingX?: number;
    paddingY?: number;
    borderRadius?: number;
  }) => void;
  onClickOutside?: () => void;
  className?: string;
}

const ButtonInlineEditor: React.FC<ButtonInlineEditorProps> = ({
  text,
  url,
  backgroundColor = '#007bff',
  color = '#ffffff',
  fontSize = 16,
  paddingX = 24,
  paddingY = 12,
  borderRadius = 6,
  onUpdate,
  onClickOutside,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [localText, setLocalText] = useState(text);
  const [localUrl, setLocalUrl] = useState(url);
  const [localBackgroundColor, setLocalBackgroundColor] = useState(backgroundColor);
  const [localColor, setLocalColor] = useState(color);
  const [localFontSize, setLocalFontSize] = useState(fontSize);
  const [localPaddingX, setLocalPaddingX] = useState(paddingX);
  const [localPaddingY, setLocalPaddingY] = useState(paddingY);
  const [localBorderRadius, setLocalBorderRadius] = useState(borderRadius);

  console.log('ButtonInlineEditor rendered with:', {
    text,
    url,
    backgroundColor,
    color,
    fontSize,
    paddingX,
    paddingY,
    borderRadius,
    hasOnClickOutside: !!onClickOutside,
  });

  useEffect(() => {
    if (!onClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        // Don't close if clicking on related elements
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.button-inline-editor') ||
            element.closest('button') ||
            element.closest('input') ||
            element.closest('select') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'INPUT');

        if (!isToolbarClick) {
          onClickOutside();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClickOutside]);

  const handleTextChange = (newText: string) => {
    setLocalText(newText);
    onUpdate({ text: newText });
  };

  const handleUrlChange = (newUrl: string) => {
    setLocalUrl(newUrl);
    onUpdate({ url: newUrl });
  };

  const handleBackgroundColorChange = (newColor: string) => {
    setLocalBackgroundColor(newColor);
    onUpdate({ backgroundColor: newColor });
  };

  const handleColorChange = (newColor: string) => {
    setLocalColor(newColor);
    onUpdate({ color: newColor });
  };

  const handleFontSizeChange = (newSize: number) => {
    setLocalFontSize(newSize);
    onUpdate({ fontSize: newSize });
  };

  const handlePaddingXChange = (newPadding: number) => {
    setLocalPaddingX(newPadding);
    onUpdate({ paddingX: newPadding });
  };

  const handlePaddingYChange = (newPadding: number) => {
    setLocalPaddingY(newPadding);
    onUpdate({ paddingY: newPadding });
  };

  const handleBorderRadiusChange = (newRadius: number) => {
    setLocalBorderRadius(newRadius);
    onUpdate({ borderRadius: newRadius });
  };

  return (
    <div
      ref={editorRef}
      className={`button-inline-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* Text Input */}
        <div className="flex items-center gap-1">
          <Type size={12} className="text-gray-600 dark:text-gray-300" />
          <input
            type="text"
            value={localText}
            onChange={(e) => handleTextChange(e.target.value)}
            className="w-24 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="Button text"
          />
        </div>

        {/* URL Input */}
        <div className="flex items-center gap-1">
          <Link size={12} className="text-gray-600 dark:text-gray-300" />
          <input
            type="url"
            value={localUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            className="w-32 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="https://..."
          />
        </div>

        {/* Font Size */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Size:</label>
          <input
            type="number"
            value={localFontSize}
            onChange={(e) => handleFontSizeChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="10"
            max="32"
          />
        </div>

        {/* Padding X */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">PX:</label>
          <input
            type="number"
            value={localPaddingX}
            onChange={(e) => handlePaddingXChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="0"
            max="100"
          />
        </div>

        {/* Padding Y */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">PY:</label>
          <input
            type="number"
            value={localPaddingY}
            onChange={(e) => handlePaddingYChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="0"
            max="50"
          />
        </div>

        {/* Border Radius */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Radius:</label>
          <input
            type="number"
            value={localBorderRadius}
            onChange={(e) => handleBorderRadiusChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="0"
            max="50"
          />
        </div>
      </div>

      {/* Color Controls */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* Background Color */}
        <div className="flex items-center gap-1">
          <Palette size={12} className="text-gray-600 dark:text-gray-300" />
          <label className="text-xs text-gray-600 dark:text-gray-300">BG:</label>
          <input
            type="color"
            value={localBackgroundColor}
            onChange={(e) => handleBackgroundColorChange(e.target.value)}
            className="w-8 h-6 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
          />
          <input
            type="text"
            value={localBackgroundColor}
            onChange={(e) => handleBackgroundColorChange(e.target.value)}
            className="w-16 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="#007bff"
          />
        </div>

        {/* Text Color */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Text:</label>
          <input
            type="color"
            value={localColor}
            onChange={(e) => handleColorChange(e.target.value)}
            className="w-8 h-6 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
          />
          <input
            type="text"
            value={localColor}
            onChange={(e) => handleColorChange(e.target.value)}
            className="w-16 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="#ffffff"
          />
        </div>
      </div>

      {/* Button Preview */}
      <div className="p-4 bg-white dark:bg-gray-800 flex justify-center">
        <button
          style={{
            backgroundColor: localBackgroundColor,
            color: localColor,
            fontSize: `${localFontSize}px`,
            paddingLeft: `${localPaddingX}px`,
            paddingRight: `${localPaddingX}px`,
            paddingTop: `${localPaddingY}px`,
            paddingBottom: `${localPaddingY}px`,
            borderRadius: `${localBorderRadius}px`,
            border: 'none',
            cursor: 'pointer',
            fontWeight: '500',
            transition: 'all 0.2s ease',
          }}
          className="hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          onClick={(e) => e.preventDefault()}
        >
          {localText || 'Button Text'}
        </button>
      </div>
    </div>
  );
};

export default ButtonInlineEditor;
