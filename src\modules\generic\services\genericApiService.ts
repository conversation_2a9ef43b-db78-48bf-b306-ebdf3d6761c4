/**
 * Generic Page API Service
 * Quản lý API calls cho trang generic
 */

import { apiClient } from '@/shared/api/axios';
import { 
  GenericPageResponse, 
  CreateGenericWidgetRequest, 
  UpdateGenericLayoutRequest,
  GenericWidget,
  GenericLayout
} from '../types';

/**
 * Lấy state hiện tại của generic page
 */
export const getGenericPageState = async (sessionId?: string): Promise<GenericPageResponse> => {
  const params = sessionId ? { sessionId } : {};
  const response = await apiClient.get<GenericPageResponse>('/generic/state', { params });
  return response.result;
};

/**
 * Tạo widget mới
 */
export const createGenericWidget = async (
  sessionId: string,
  request: CreateGenericWidgetRequest
): Promise<GenericWidget> => {
  const response = await apiClient.post<GenericWidget>('/generic/widgets', {
    ...request,
    sessionId,
  });
  return response.result;
};

/**
 * Xóa widget
 */
export const deleteGenericWidget = async (
  sessionId: string, 
  widgetId: string
): Promise<void> => {
  await apiClient.delete(`/generic/widgets/${widgetId}`, {
    data: { sessionId }
  });
};

/**
 * Cập nhật layout
 */
export const updateGenericLayout = async (
  sessionId: string,
  request: UpdateGenericLayoutRequest
): Promise<GenericLayout[]> => {
  const response = await apiClient.put<GenericLayout[]>('/generic/layout', {
    ...request,
    sessionId,
  });
  return response.result;
};

/**
 * Sync state với server
 */
export const syncGenericState = async (
  sessionId: string,
  widgets: GenericWidget[],
  layout: GenericLayout[]
): Promise<GenericPageResponse> => {
  const response = await apiClient.post<GenericPageResponse>('/generic/sync', {
    sessionId,
    widgets,
    layout,
  });
  return response.result;
};

/**
 * Tạo session mới
 */
export const createGenericSession = async (): Promise<{ sessionId: string }> => {
  const response = await apiClient.post<{ sessionId: string }>('/generic/sessions');
  return response.result;
};

/**
 * Xóa session
 */
export const deleteGenericSession = async (sessionId: string): Promise<void> => {
  await apiClient.delete(`/generic/sessions/${sessionId}`);
};
