import React, { useRef, useState, useEffect } from 'react';
import { Link, Type, Palette, AlignLeft, AlignCenter, AlignRight } from 'lucide-react';

interface LinkInlineEditorProps {
  text: string;
  url: string;
  color?: string;
  fontSize?: number;
  fontWeight?: string;
  textDecoration?: string;
  textAlign?: 'left' | 'center' | 'right';
  onUpdate: (updates: {
    text?: string;
    url?: string;
    color?: string;
    fontSize?: number;
    fontWeight?: string;
    textDecoration?: string;
    textAlign?: 'left' | 'center' | 'right';
  }) => void;
  onClickOutside?: () => void;
  className?: string;
}

const LinkInlineEditor: React.FC<LinkInlineEditorProps> = ({
  text,
  url,
  color = '#007bff',
  fontSize = 16,
  fontWeight = 'normal',
  textDecoration = 'underline',
  textAlign = 'left',
  onUpdate,
  onClickOutside,
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [localText, setLocalText] = useState(text);
  const [localUrl, setLocalUrl] = useState(url);
  const [localColor, setLocalColor] = useState(color);
  const [localFontSize, setLocalFontSize] = useState(fontSize);
  const [localFontWeight, setLocalFontWeight] = useState(fontWeight);
  const [localTextDecoration, setLocalTextDecoration] = useState(textDecoration);
  const [localTextAlign, setLocalTextAlign] = useState(textAlign);

  console.log('LinkInlineEditor rendered with:', {
    text,
    url,
    color,
    fontSize,
    fontWeight,
    textDecoration,
    textAlign,
    hasOnClickOutside: !!onClickOutside,
  });

  useEffect(() => {
    if (!onClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.link-inline-editor') ||
            element.closest('button') ||
            element.closest('input') ||
            element.closest('select') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'INPUT');

        if (!isToolbarClick) {
          onClickOutside();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClickOutside]);

  const handleTextChange = (newText: string) => {
    setLocalText(newText);
    onUpdate({ text: newText });
  };

  const handleUrlChange = (newUrl: string) => {
    setLocalUrl(newUrl);
    onUpdate({ url: newUrl });
  };

  const handleColorChange = (newColor: string) => {
    setLocalColor(newColor);
    onUpdate({ color: newColor });
  };

  const handleFontSizeChange = (newSize: number) => {
    setLocalFontSize(newSize);
    onUpdate({ fontSize: newSize });
  };

  const handleFontWeightChange = (newWeight: string) => {
    setLocalFontWeight(newWeight);
    onUpdate({ fontWeight: newWeight });
  };

  const handleTextDecorationChange = (newDecoration: string) => {
    setLocalTextDecoration(newDecoration);
    onUpdate({ textDecoration: newDecoration });
  };

  const handleTextAlignChange = (newAlign: 'left' | 'center' | 'right') => {
    setLocalTextAlign(newAlign);
    onUpdate({ textAlign: newAlign });
  };

  return (
    <div
      ref={editorRef}
      className={`link-inline-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* Text Input */}
        <div className="flex items-center gap-1">
          <Type size={12} className="text-gray-600 dark:text-gray-300" />
          <input
            type="text"
            value={localText}
            onChange={(e) => handleTextChange(e.target.value)}
            className="w-24 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="Link text"
          />
        </div>

        {/* URL Input */}
        <div className="flex items-center gap-1">
          <Link size={12} className="text-gray-600 dark:text-gray-300" />
          <input
            type="url"
            value={localUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            className="w-32 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="https://..."
          />
        </div>

        {/* Font Size */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Size:</label>
          <input
            type="number"
            value={localFontSize}
            onChange={(e) => handleFontSizeChange(Number(e.target.value))}
            className="w-12 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            min="10"
            max="32"
          />
        </div>

        {/* Font Weight */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Weight:</label>
          <select
            value={localFontWeight}
            onChange={(e) => handleFontWeightChange(e.target.value)}
            className="text-xs border border-gray-300 dark:border-gray-600 rounded px-1 py-0.5"
          >
            <option value="normal">Normal</option>
            <option value="bold">Bold</option>
            <option value="lighter">Light</option>
            <option value="bolder">Bolder</option>
          </select>
        </div>

        {/* Text Decoration */}
        <div className="flex items-center gap-1">
          <label className="text-xs text-gray-600 dark:text-gray-300">Style:</label>
          <select
            value={localTextDecoration}
            onChange={(e) => handleTextDecorationChange(e.target.value)}
            className="text-xs border border-gray-300 dark:border-gray-600 rounded px-1 py-0.5"
          >
            <option value="underline">Underline</option>
            <option value="none">None</option>
            <option value="overline">Overline</option>
            <option value="line-through">Strike</option>
          </select>
        </div>
      </div>

      {/* Color & Alignment Row */}
      <div className="flex items-center gap-2 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 flex-wrap">
        {/* Color */}
        <div className="flex items-center gap-1">
          <Palette size={12} className="text-gray-600 dark:text-gray-300" />
          <label className="text-xs text-gray-600 dark:text-gray-300">Color:</label>
          <input
            type="color"
            value={localColor}
            onChange={(e) => handleColorChange(e.target.value)}
            className="w-8 h-6 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
          />
          <input
            type="text"
            value={localColor}
            onChange={(e) => handleColorChange(e.target.value)}
            className="w-16 px-1 py-0.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
            placeholder="#007bff"
          />
        </div>

        {/* Divider */}
        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500"></div>

        {/* Alignment Buttons */}
        <div className="flex items-center gap-1">
          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'left'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('left')}
            title="Align Left"
          >
            <AlignLeft size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'center'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('center')}
            title="Align Center"
          >
            <AlignCenter size={12} />
          </button>

          <button
            className={`p-1 rounded transition-colors ${
              localTextAlign === 'right'
                ? 'bg-blue-500 text-white'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => handleTextAlignChange('right')}
            title="Align Right"
          >
            <AlignRight size={12} />
          </button>
        </div>
      </div>

      {/* Link Preview */}
      <div 
        className="p-4 bg-white dark:bg-gray-800 flex"
        style={{ 
          justifyContent: localTextAlign === 'center' ? 'center' : localTextAlign === 'right' ? 'flex-end' : 'flex-start'
        }}
      >
        <a
          href={localUrl || '#'}
          style={{
            color: localColor,
            fontSize: `${localFontSize}px`,
            fontWeight: localFontWeight,
            textDecoration: localTextDecoration,
          }}
          className="hover:opacity-80 transition-opacity"
          onClick={(e) => e.preventDefault()}
        >
          {localText || 'Link Text'}
        </a>
      </div>
    </div>
  );
};

export default LinkInlineEditor;
