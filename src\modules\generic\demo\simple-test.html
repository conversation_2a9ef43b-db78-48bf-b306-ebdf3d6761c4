<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple WebSocket Test</title>
</head>
<body>
    <h1>Simple WebSocket Test</h1>
    <div id="status">Disconnected</div>
    <button onclick="connect()">Connect</button>
    <div id="log"></div>

    <script>
        let ws = null;
        const sessionId = 'simple-test-' + Date.now();

        function log(message) {
            const logEl = document.getElementById('log');
            logEl.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function connect() {
            const url = 'ws://localhost:8081/ws/generic?sessionId=' + sessionId;
            log('Connecting to: ' + url);
            
            ws = new WebSocket(url);
            
            ws.onopen = function() {
                log('✅ Connected!');
                document.getElementById('status').textContent = 'Connected';
            };
            
            ws.onmessage = function(event) {
                log('📥 Message: ' + event.data);
            };
            
            ws.onclose = function() {
                log('🔌 Disconnected');
                document.getElementById('status').textContent = 'Disconnected';
            };
            
            ws.onerror = function(error) {
                log('❌ Error: ' + error);
            };
        }

        // Auto connect
        setTimeout(connect, 1000);
    </script>
</body>
</html>
