{"marketing": {"title": "Marketing", "description": "Manage marketing campaigns and target audiences", "menu": {"audience": "Audience", "segment": "Segment", "campaign": "Campaign", "tags": "Tags", "customFields": "Custom Fields", "reports": "Reports", "templateEmail": "Email Templates", "articles": "Articles", "videos": "Videos"}, "systemEmailTemplates": {"table": {"template": "Template", "type": "Type", "tags": "Tags", "createdAt": "Created At", "actions": "Actions"}}, "featureInDevelopment": {"title": "Feature Under Development", "description": "We are working hard to develop this feature to bring you the best experience. Please come back later!", "comingSoon": "Coming Soon:", "feature1": "User-friendly and easy-to-use interface", "feature2": "Full integration with Zalo system", "feature3": "High performance and stability", "contact": "Have questions? Contact us at"}, "dashboard": {"title": "Marketing Dashboard", "description": "Overview of marketing activities"}, "segment": {"title": "Segmentation", "description": "Segment customers by different criteria", "manage": "Manage Segments", "addNew": "Add New Segment", "edit": "Edit Segment", "detail": "Segment Details", "name": "Segment Name", "audienceCount": "Audience Count", "audience": "Audience", "status": "Status", "totalContacts": "Total Contacts", "conditions": "Conditions", "confirmDelete": "Are you sure you want to delete this segment?", "totalSegments": "Total Segments", "types": {"demographic": "Demographic", "behavioral": "Behavioral", "geographic": "Geographic", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Not Contains", "greater_than": "Greater Than", "less_than": "Less Than", "between": "Between"}, "form": {"title": "Segment Information", "name": "Segment Name", "namePlaceholder": "Enter segment name...", "description": "Description", "descriptionPlaceholder": "Enter segment description...", "conditions": "Conditions", "conditionsDescription": "Set up conditions to segment customers", "addGroup": "Add Condition Group", "addCondition": "Add Condition", "removeGroup": "Remove Group", "removeCondition": "Remove Condition", "field": "Field", "operator": "Operator", "value": "Value", "valuePlaceholder": "Enter value...", "and": "AND", "or": "OR", "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Does Not Contain", "starts_with": "Starts With", "ends_with": "Ends With", "greater_than": "Greater Than", "less_than": "Less Than", "greater_than_or_equal": "Greater Than or Equal", "less_than_or_equal": "Less Than or Equal", "is_empty": "Is Empty", "is_not_empty": "Is Not Empty"}, "validation": {"nameRequired": "Segment name is required", "nameMinLength": "Segment name must be at least 2 characters", "nameMaxLength": "Segment name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters", "conditionsRequired": "At least one condition is required", "fieldRequired": "Field is required", "operatorRequired": "Operator is required", "valueRequired": "Value is required"}, "buttons": {"save": "Save", "cancel": "Close", "saveTooltip": "Save segment", "cancelTooltip": "Cancel and close form"}, "systemField": "System Field"}, "selectCustomField": "Select field..."}, "tags": {"title": "Tag Management", "description": "Create and manage tags for customers", "addNew": "Add New Tag", "edit": "Edit Tag", "name": "Tag Name", "status": "Status", "confirmDelete": "Are you sure you want to delete this tag?", "statuses": {"active": "Active", "inactive": "Inactive"}, "form": {"name": "Tag Name", "namePlaceholder": "Enter tag name", "color": "Color Code", "colorPlaceholder": "Choose color for tag", "randomColor": "Random Color"}, "validation": {"nameRequired": "Tag name is required", "colorInvalid": "Color code must be in HEX format (e.g., #FF0000)"}, "deleteSuccess": "Tag deleted successfully", "deleteError": "Failed to delete tag", "selectToDelete": "Please select tags to delete", "bulkDeleteSuccess": "Successfully deleted {{count}} tags", "bulkDeleteError": "Failed to delete multiple tags", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected tags?", "totalTags": "Total Tags", "manage": "Manage Tags"}, "customFields": {"title": "Custom Fields", "description": "Manage custom fields for audiences", "searchPlaceholder": "Enter keywords to search for custom fields...", "noFields": "No custom fields yet. Use the search box above to add fields."}, "reports": {"title": "Reports", "description": "View marketing reports and analytics"}, "mediaResources": {"title": "Media Resources", "description": "Manage media files for Zalo OA", "table": {"preview": "Preview", "filename": "Filename", "type": "Type", "size": "Size", "mimeType": "MIME Type", "description": "Description", "uploadedAt": "Upload Date"}, "type": {"image": "Image", "gif": "GIF", "file": "File"}, "filter": {"type": "Filter by type"}, "form": {"title": "Add Media Resource", "officialAccount": {"label": "Official Account", "placeholder": "Select Official Account"}, "file": {"label": "File Upload", "placeholder": "Drag and drop or click to upload file", "supportedTypes": "Supported file types:", "imageTypes": "Images: JPG, PNG (max 1MB)", "documentTypes": "Documents: PDF, DOC, DOCX, CSV (max 10MB)", "gifTypes": "GIF: GIF (max 5MB)"}, "description": {"label": "Description", "placeholder": "Enter file description (optional)"}, "submit": "Upload File"}, "validation": {"oaRequired": "Please select Official Account", "fileRequired": "Please select file to upload", "imageSizeExceeded": "Image must not exceed 1MB", "documentSizeExceeded": "Document must not exceed 10MB", "gifSizeExceeded": "GIF must not exceed 5MB", "unsupportedFileType": "Unsupported file type"}, "success": {"uploadSuccess": "File uploaded successfully!"}, "error": {"uploadFailed": "File upload failed. Please try again."}}, "templateEmail": {"title": "Email Template Management", "description": "Create and manage email marketing templates"}, "articles": {"title": "Article Management", "description": "Create and manage marketing articles"}, "videos": {"title": "Video Management", "description": "Create and manage marketing videos"}, "email": {"title": "Email", "description": "Manage email marketing campaigns"}, "sms": {"title": "SMS", "description": "Manage SMS marketing campaigns"}, "googleAds": {"title": "Google Ads", "description": "Manage Google advertising", "detail": {"title": "Google Ads Details"}, "accounts": {"title": "Google Ads Accounts", "description": "Manage Google Ads accounts"}, "campaigns": {"title": "Google Ads Campaigns", "description": "Manage Google Ads campaigns"}, "keywords": {"title": "Google Ads Keywords", "description": "Manage Google Ads keywords"}, "ads": {"title": "Google Ads", "description": "Manage Google Ads"}, "reports": {"title": "Google Ads Reports", "description": "Google Ads reports"}, "settings": {"title": "Google Ads Settings", "description": "Google Ads settings"}}, "facebookAds": {"title": "Facebook Ads", "description": "Integrate and manage Facebook Ads campaigns", "totalAccounts": "Accounts", "manage": "Manage Facebook Ads", "comingSoon": "This feature is under development. Please check back later!", "overview": {"title": "Facebook Ads Overview", "description": "Manage and monitor Facebook advertising performance", "stats": "Overview Statistics", "quickActions": "Quick Actions", "performance": "Performance", "recentActivity": "Recent Activity", "connectedAs": "Connected as", "notConnected": "Not connected", "connectAccount": "Connect Account", "totalAccounts": "Total Accounts", "activeAccounts": "Active Accounts", "totalCampaigns": "Total Campaigns", "activeCampaigns": "Active Campaigns"}, "stats": {"totalCampaigns": "Total Campaigns", "totalSpend": "Total Spend", "impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "Click-through Rate (CTR)", "cpc": "Cost per Click (CPC)", "reach": "Reach", "frequency": "Frequency", "conversions": "Conversions", "costPerConversion": "Cost per Conversion", "roas": "ROAS", "adSpend": "Ad Spend"}, "actions": {"createCampaign": "Create Campaign", "createCampaignDesc": "Create a new advertising campaign", "manageAccounts": "Manage Accounts", "manageAccountsDesc": "Connect and manage Facebook accounts", "viewReports": "View Reports", "viewReportsDesc": "Analyze campaign performance", "manageCampaigns": "Manage Campaigns", "manageCampaignsDesc": "View and manage existing campaigns", "optimizeCampaigns": "Optimize Campaigns", "optimizeCampaignsDesc": "Improve advertising performance", "audienceInsights": "Audience Insights", "audienceInsightsDesc": "Analyze customer audience"}, "accounts": {"title": "Facebook Account Management", "description": "Connect and manage Facebook advertising accounts", "addManual": "Add Manually", "overview": {"stats": "Account Statistics", "quickActions": "Quick Actions"}, "stats": {"totalAccounts": "Total Accounts", "activeAccounts": "Active Accounts", "totalBalance": "Total Balance", "monthlySpend": "Monthly Spend"}, "actions": {"connectAccount": "Connect Account", "connectAccountDesc": "Connect new Facebook Ads account", "syncAccounts": "Sync Accounts", "syncAccountsDesc": "Update information from Facebook", "managePermissions": "Manage Permissions", "managePermissionsDesc": "Configure access permissions", "viewReports": "View Reports", "viewReportsDesc": "Detailed account reports"}, "modal": {"title": "Connect Facebook Account", "accessToken": "Access Token", "accessTokenPlaceholder": "Enter Facebook Access Token", "accountId": "Account ID", "accountIdPlaceholder": "Enter Facebook Account ID (act_...)", "connect": "Connect"}}, "status": {"active": "Active", "paused": "Paused", "completed": "Completed", "draft": "Draft", "pending": "Pending", "rejected": "Rejected"}, "metrics": {"impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "CTR", "cpc": "CPC", "spend": "Spend", "reach": "Reach", "frequency": "Frequency", "conversions": "Conversions", "costPerConversion": "Cost per Conversion", "roas": "ROAS", "quality": "Quality", "relevance": "Relevance"}}, "zaloAds": {"title": "<PERSON><PERSON>", "description": "Manage Zalo advertising", "overview": {"title": "Zalo Ads Overview"}, "accounts": {"title": "Zalo Ads Account Management", "description": "Manage Zalo Ads accounts"}, "campaigns": {"title": "Zalo Ads Campaign Management", "description": "Manage Zalo Ads campaigns"}, "reports": {"title": "Zalo Ads Reports", "description": "Zalo Ads reports"}}, "tiktokAds": {"title": "TikTok Ads", "description": "Manage TikTok advertising", "overview": {"title": "TikTok Ads Overview"}, "accounts": {"title": "TikTok Ads Accounts", "description": "Manage TikTok Ads accounts"}, "campaigns": {"title": "TikTok Ads Campaigns", "description": "Manage TikTok Ads campaigns"}, "creatives": {"title": "TikTok Ads Creatives", "description": "Manage TikTok Ads creatives"}, "audiences": {"title": "TikTok Ads Audiences", "description": "Manage TikTok Ads audiences"}, "reports": {"title": "TikTok Ads Reports", "description": "TikTok Ads reports"}, "settings": {"title": "TikTok Ads Settings", "description": "TikTok Ads settings"}}, "resources": {"title": "Resources", "description": "Template library and support tools"}, "zns": {"campaign": {"list": {"title": "ZNS Campaign List"}, "create": {"title": "Create New ZNS Campaign", "success": "Campaign created successfully", "error": "Failed to create campaign"}, "form": {"name": "Campaign Name", "namePlaceholder": "Enter campaign name", "description": "Description", "descriptionPlaceholder": "Enter campaign description (optional)", "integration": "Official Account", "integrationPlaceholder": "Select Official Account...", "template": "ZNS Template", "templatePlaceholder": "Select ZNS template...", "customerListType": "Setup Method", "customerListTypePlaceholder": "Select method...", "audience": "Select Audience", "audiencePlaceholder": "Select audience...", "phoneNumbers": "Phone Numbers", "uploadFile": "Upload Excel file with phone numbers", "uploadFileDescription": "Excel file must have phone numbers in the first column (skip header row)", "uploadFileButton": "Click to select Excel file", "uploadFileSupport": "Supports .xlsx, .xls", "uploadSuccess": "Uploaded {{count}} phone numbers", "uploadError": "Error reading Excel file. Please check file format.", "downloadTemplate": "Download Template", "addPhoneButton": "+ Add Number", "addPhoneInstruction": "Click \"Add Number\" to add phone numbers", "phonePlaceholder": "**********", "templateDataField": "Field Name", "templateDataValue": "Enter value...", "templateDataValuePlaceholder": "<value>", "addTemplateField": "+ Add Data Field", "sendingMode": "Sending Mode"}, "sendingMode": {"normal": "Normal Send - ZNS messages sent through standard mechanism", "overLimit": "Over Limit Send - Mechanism allows OA to send ZNS tag 3 over limit"}, "customerList": {"audience": "Select from audience list", "upload": "Upload Excel file", "manual": "Manual input"}, "validation": {"nameRequired": "Campaign name is required", "nameMaxLength": "Campaign name must not exceed 255 characters", "descriptionMaxLength": "Description must not exceed 1000 characters", "integrationRequired": "Please select Zalo OA account", "templateRequired": "Please select ZNS template", "customerListTypeRequired": "Please select customer list setup method", "phoneInvalid": "Invalid phone number", "sendingModeRequired": "Please select sending mode", "scheduleTypeRequired": "Please select schedule type", "scheduledTimeInvalid": "Scheduled time must be greater than current time"}, "templateData": {"fallback": {"customerName": "Customer", "message": "System notification"}}, "table": {"name": "Campaign Name", "template": "Template", "status": "Status", "audience": "Audience", "metrics": "Metrics", "createdAt": "Created Date", "performance": "Performance", "scheduled": "Scheduled"}, "actions": {"start": "Start", "pause": "Pause", "resume": "Resume", "stop": "Stop", "duplicate": "Duplicate"}, "status": {"draft": "Draft", "scheduled": "Scheduled", "running": "Running", "paused": "Paused", "completed": "Completed", "cancelled": "Cancelled", "failed": "Failed"}, "delete": {"title": "Delete Campaign", "message": "Are you sure you want to delete campaign \"{{name}}\"?"}}}, "common": {"moduleTitle": "Marketing", "all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "add": "Add New", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "search": "Search", "filter": "Filter", "actions": "Actions", "status": "Status", "name": "Name", "description": "Description", "createdAt": "Created At", "updatedAt": "Updated At", "type": "Type", "id": "ID", "followers": "Followers", "manage": "Manage"}, "audience": {"title": "Audience Management", "description": "Manage leads and customers", "totalContacts": "Total Contacts", "manage": "Manage Audience", "addNew": "Add New Audience", "edit": {"title": "Edit", "save": "Save", "cancel": "Cancel", "success": "Save successful", "successMessage": "Audience information has been updated", "error": "Save error", "errorMessage": "An error occurred while saving audience information"}, "detail": "Audience Details", "name": "Audience Name", "email": "Email", "channel": "Channel", "platform": {"zalo": "<PERSON><PERSON>", "zaloPersonal": "<PERSON><PERSON>", "facebook": "Facebook", "email": "Email", "phone": "Phone", "web": "Web"}, "phone": "Phone Number", "address": "Address", "type": "Audience Type", "status": "Status", "tags": "Tags", "customFields": "Custom Fields", "generalInfo": "General Information", "detailForm": "Audience Details", "loadError": "Error loading audience information", "notFound": "Audience not found", "notFoundDescription": "This audience may have been deleted or does not exist", "createSuccess": "Audience created successfully", "createError": "Failed to create audience", "form": {"addTitle": "Add New Audience", "namePlaceholder": "Enter audience name", "emailPlaceholder": "Enter email", "phonePlaceholder": "Enter phone number", "addressPlaceholder": "Enter address", "tagsPlaceholder": "Select tags..."}, "validation": {"nameRequired": "Audience name is required", "emailInvalid": "Invalid email format"}, "avatar": {"clickToChange": "Click to change avatar", "invalidFileType": "Please select an image file", "fileTooLarge": "File too large. Please select a file smaller than 5MB", "uploadSuccess": "Avatar uploaded successfully", "uploadError": "An error occurred while uploading avatar"}, "interactionHistory": {"title": "Interaction History & Status", "email": "Email", "zalo": "<PERSON><PERSON>", "phone": "Phone", "lastSent": "Last Sent", "lastMessage": "Last Message", "lastCall": "Last Call", "openRate": "Open Rate", "clickRate": "Click Rate", "responseRate": "Response Rate", "deliveryRate": "Delivery Rate", "callDuration": "Call Duration", "smsCount": "SMS Count", "status": {"active": "Active", "inactive": "Not Connected", "verified": "Verified"}}, "socialInfo": {"title": "Social Information", "profileUrl": "Profile URL", "lastActivity": "Last Activity", "username": "Username", "followers": "Followers", "connections": "Connections", "handle": "<PERSON><PERSON>", "status": {"connected": "Connected", "notConnected": "Not Connected"}}}, "zaloArticle": {"title": "Title", "image": "Image", "createDate": "Create Date", "updateDate": "Update Date", "status": "Status", "statusShow": "Published", "statusHide": "Hidden", "statusFailed": "Failed", "selectOA": "Select Official Account to create article", "selectOAPlaceholder": "Select Official Account...", "selectOARequired": "Please select an Official Account", "stats": "Statistics", "views": "Views", "likes": "<PERSON>s", "shares": "shares", "view": "View", "selectOfficialAccount": "Select Official Account", "selectOfficialAccountPlaceholder": "Select Official Account...", "filterByType": "Filter by Type", "selectTypePlaceholder": "Select article type...", "deleteConfirm": "Confirm Delete Article", "deleteDescription": "Are you sure you want to delete this article? This action cannot be undone.", "type": {"normal": "Article", "video": "Video"}, "form": {"editTitle": "Edit Article", "createTitle": "Create New Article", "officialAccount": "Official Account", "selectOAError": "Please select an Official Account", "coverImage": "Cover Image", "uploadCoverImage": "Click to add cover image", "editCoverImage": "Edit image", "articleTitle": "Article Title", "titlePlaceholder": "Enter article title", "author": "Author", "authorPlaceholder": "Enter author name", "description": "Description", "descriptionPlaceholder": "Enter article description", "content": "Article Content", "contentPlaceholder": "Enter article content...", "relatedArticles": "Related Articles", "addRelated": "Add", "hideRelated": "<PERSON>de", "editRelated": "Edit", "selectRelatedDescription": "Select related articles (maximum 5 articles):", "addRelatedPlaceholder": "Click to add related articles", "saveDraft": "Save Draft", "schedule": "Schedule", "publish": "Publish", "uploading": "Uploading...", "save": "Save", "cancel": "Cancel"}, "preview": {"selectOAPlaceholder": "Select Official Account", "addTitle": "Click to add title", "addAuthor": "Click to add author", "addDescription": "Click to add description", "addContent": "Click to add article content...", "viewsCount": "views", "moreArticles": "more articles"}, "scheduler": {"title": "Schedule Publication", "description": "Select time to automatically publish the article", "selectDateTime": "Select date and time", "selectScheduleTime": "Please select schedule time", "confirmSchedule": "Confirm Schedule"}, "notifications": {"imageUploadSuccess": "Image uploaded successfully", "imageUploadError": "Error occurred while uploading image", "createSuccess": "Article created successfully", "createError": "Error occurred while creating article", "updateSuccess": "Article updated successfully", "updateError": "Error occurred while updating article"}}, "zaloVideo": {"status": "Status", "published": "Published", "draft": "Draft", "failed": "Failed", "retry": "Retry", "retrySuccess": "Video retried successfully", "retryError": "Error occurred while retrying video"}, "totalContacts": "Total Contacts", "attributes": "Attributes", "confirmDelete": "Are you sure you want to delete this audience?", "type": {"customer": "Customer", "lead": "Lead", "subscriber": "Subscriber", "custom": "Custom"}, "detail": {"overview": "Overview", "totalContacts": "Total Contacts", "type": "Type", "status": "Status"}, "overview": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "tags": "Tags", "timeInfo": "Time Information", "customFields": "Custom Fields"}, "customField": {"configId": "Field Identifier Name", "title": "Custom Fields", "description": "Manage custom fields", "adminDescription": "Manage system custom fields", "add": "Add Custom Field", "edit": "Edit Custom Field", "dataType": "Data Type", "dataTypes": {"text": "Text", "number": "Number", "boolean": "True/False", "date": "Date", "select": "Select", "object": "Object"}, "addForm": "Add New Custom Field", "editForm": "Edit Custom Field", "component": "Component Type", "components": {"input": "Input Field", "textarea": "Text Area", "select": "Select List", "checkbox": "Checkbox", "radio": "Radio Button", "date": "Date", "number": "Number", "file": "File", "multiSelect": "Multi Select"}, "type": "Data Type", "type.string": "Text", "type.number": "Number", "type.boolean": "Yes/No", "type.date": "Date", "type.object": "Object", "type.array": "Array", "types": {"text": "Text", "number": "Number", "boolean": "Yes/No", "date": "Date", "select": "Select Box", "object": "Object", "array": "Array", "string": "Text"}, "name": "Field Name", "label": "Label", "placeholder": "Placeholder", "defaultValue": "Default Value", "options": "Options", "required": "Required", "validation": {"minLength": "Minimum Length", "maxLength": "Maximum Length", "pattern": "Pattern", "min": "Minimum Value", "max": "Maximum Value"}, "form": {"fieldKeyLabel": "Identifier field", "fieldKey": "Field Key", "fieldKeyPlaceholder": "full_name", "displayName": "Display Name", "tags": "Tags", "tagsPlaceholder": "personal, required, contact", "componentRequired": "Please select component type", "labelRequired": "Please enter label", "typeRequired": "Please select data type", "idRequired": "Please enter field identifier name", "labelPlaceholder": "Enter display label", "descriptionPlaceholder": "Enter description for this field", "placeholderPlaceholder": "Enter placeholder", "defaultValuePlaceholder": "Enter default value", "optionsPlaceholder": "Enter options, separated by commas or JSON format", "selectOptionsPlaceholder": "Enter values in format: Name|Value, Each pair on a new line. Example:\na|1\nb|2", "booleanDefaultPlaceholder": "Select default value", "dateDefaultPlaceholder": "Select default date", "description": "Description", "labelTagRequired": "Please add at least one label", "fieldIdLabel": "Field Identifier Name", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "Field Display Name", "displayNamePlaceholder": "Enter display name for this field", "displayNameRequired": "Please enter field display name", "labelInputPlaceholder": "Enter label and press Enter", "tagsCount": "labels added", "patternSuggestions": "Common pattern suggestions:", "defaultValue": "Default Value", "minLength": "Minimum Length", "maxLength": "Maximum Length", "pattern": "Pattern", "options": "Options", "min": "Minimum Value", "max": "Maximum Value", "minValue": "Minimum Value", "maxValue": "Maximum Value", "placeholder": "Placeholder", "required": "Required", "labels": "Labels", "showAdvancedSettings": "Show Advanced Settings"}, "createSuccess": "Custom field created successfully", "createError": "Error creating custom field", "updateSuccess": "Custom field updated successfully", "updateError": "Error updating custom field", "deleteSuccess": "Custom field deleted successfully", "deleteError": "Error deleting custom field", "loadError": "Error loading custom field", "booleanValues": {"true": "Yes", "false": "No"}, "patterns": {"email": "Email", "phoneVN": "Vietnam Phone", "phoneIntl": "International Phone", "postalCodeVN": "Vietnam Postal Code", "lettersOnly": "Letters Only", "numbersOnly": "Numbers Only", "alphanumeric": "Letters and Numbers", "noSpecialChars": "No Special Characters", "url": "URL", "ipv4": "IPv4", "strongPassword": "Strong Password", "vietnameseName": "Vietnamese Name", "studentId": "Student ID", "nationalId": "National ID", "taxCode": "Tax Code", "dateFormat": "Date (dd/mm/yyyy)", "timeFormat": "Time (hh:mm)", "hexColor": "Hex Color", "base64": "Base64", "uuid": "UUID", "filename": "Filename", "urlSlug": "URL Slug", "variableName": "Variable Name", "creditCard": "Credit Card Number", "qrCode": "QR Code", "gpsCoordinate": "GPS Coordinate", "rgbColor": "RGB Color", "domain": "Domain", "decimal": "Decimal", "barcode": "Barcode"}, "confirmDeleteMessage": "Are you sure you want to delete this custom field?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected custom fields?", "bulkDeleteSuccess": "Successfully deleted {{count}} custom fields", "bulkDeleteError": "Error occurred while deleting custom fields", "selectedItems": "Selected {{count}} items", "totalFields": "Total Custom Fields", "manage": "Manage Custom Fields", "noDescription": "No description"}, "types": {"customer": "Customer", "lead": "Lead", "subscriber": "Subscriber", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "form": {"nameLabel": "Audience Name", "namePlaceholder": "Enter audience name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter audience description", "typeLabel": "Type", "statusLabel": "Status", "attributesLabel": "Attributes", "addAttribute": "Add Attribute", "attributeName": "Attribute Name", "attributeValue": "Value"}, "zalo": {"title": "<PERSON><PERSON>", "description": "Integrate Zalo Official Account, send ZNS messages", "totalAccounts": "Accounts", "manage": "<PERSON><PERSON>", "comingSoon": "This feature is under development. Please check back later!", "messages": "Messages", "articles": {"title": "<PERSON><PERSON>", "description": "Manage articles on Zalo Official Account", "table": {"title": "Title", "type": "Type", "status": "Status", "author": "Author", "stats": "Statistics", "publishTime": "Publish Time"}, "type": {"title": "Type", "normal": "Normal Article", "text": "Text", "image": "Image", "video": "Video", "link": "Link"}, "status": {"draft": "Draft", "published": "Published", "archived": "Archived", "deleted": "Deleted", "failed": "Failed"}, "actions": {"show": "Show", "hide": "<PERSON>de", "retry": "Retry"}, "znsCampaigns": {"title": "ZNS Campaigns", "description": "Manage Zalo Notification Service campaigns"}, "showSuccess": "Article shown successfully", "hideSuccess": "Article hidden successfully", "showError": "Error occurred while showing article", "hideError": "Error occurred while hiding article", "retrySuccess": "Article retried successfully", "retryError": "Error occurred while retrying article", "deleteError": "Error occurred while deleting articles", "syncSuccess": "Successfully synced articles from all OAs", "syncError": "Error occurred while syncing articles. Please try again."}, "automation": {"title": "Automation", "description": "Set up message automation"}, "analytics": {"title": "Analytics", "description": "Reports and performance analysis"}, "modules": {"znsTemplates": {"title": "ZNS Templates", "description": "Manage ZNS notification templates for customers", "sync": "Sync templates from <PERSON><PERSON>", "table": {"templateName": "Template Name", "status": "Status", "templateType": "Template Type", "createdAt": "Created At", "actions": "Actions"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "disabled": "Disabled"}, "filter": {"status": "Filter by status", "type": "Filter by type"}, "tag": {"transaction": "Transaction", "promotion": "Promotion", "otp": "OTP"}}, "oaTemplates": {"title": "OA Message Templates", "description": "Create and manage Official Account message templates"}, "officialAccount": {"title": "OA Account", "description": "Manage Zalo Official Account and connections"}, "personalAccount": {"title": "Personal Account", "description": "Manage personal Zalo account and integrations", "table": {"stt": "No.", "accountName": "Account Name", "status": "Status"}, "actions": {"addIntegration": "Add New Integration", "relogin": "Re-login", "checkStatus": "Check Login Status"}, "messages": {"reloginSuccess": "Re-login successful", "reloginError": "Error during re-login", "statusChecked": "Status checked", "statusCheckError": "Error checking status", "deleteSuccess": "Integration deleted successfully", "deleteError": "Error deleting integration"}, "deleteModal": {"title": "Confirm Delete", "description": "Are you sure you want to delete {{count}} selected integrations?"}, "integration": {"title": "Zalo Personal Integration", "description": "Connect your personal Zalo account to use marketing features", "oauth": {"title": "<PERSON><PERSON><PERSON>", "description": "Recommended method - Secure login via Zalo", "instructions": "Click the button below to login to <PERSON><PERSON> and grant permissions to the application", "loginButton": "Login with <PERSON><PERSON>"}, "manual": {"title": "Manual Integration", "description": "Enter authentication information manually"}, "form": {"integrationName": "Integration Name", "integrationNamePlaceholder": "Enter name for this integration...", "authCode": "Auth Code", "authCodeDescription": "Authentication code from Zalo Developer Console", "authCodePlaceholder": "Enter auth code...", "redirectUri": "Redirect URI", "redirectUriDescription": "Callback URL after successful authentication", "redirectUriPlaceholder": "Enter redirect URI...", "createButton": "Create Integration"}, "messages": {"createSuccess": "Integration created successfully", "createError": "Error creating integration", "oauthComingSoon": "OAuth feature is under development"}, "errors": {"nameRequired": "Integration name is required", "authCodeRequired": "Auth code is required", "redirectUriRequired": "Redirect URI is required"}}}, "znsCampaigns": {"title": "ZNS Campaigns", "description": "Create and manage ZNS sending campaigns"}, "oaMessages": {"title": "OA Messages", "description": "Send and manage messages via Official Account"}, "followers": {"title": "Manage Followers", "description": "Track and manage OA followers"}, "groups": {"title": "Manage Groups", "description": "Manage and organize user groups"}, "analytics": {"title": "Analytics", "description": "Reports and campaign performance analysis"}, "automation": {"title": "Automation", "description": "Set up message automation and workflows"}, "content": {"title": "Content Management", "description": "Manage articles and marketing videos"}}, "overview": {"title": "Zalo Marketing", "description": "Man<PERSON> Zalo Official Account and ZNS campaigns", "connectAccount": "Connect OA", "noAccounts": "No accounts yet", "noAccountsDescription": "Connect Zalo Official Account to get started", "connectFirstAccount": "Connect first account", "connectedAccounts": "Connected accounts", "connectedAccountsDescription": "Manage connected Zalo Official Accounts", "viewAllAccounts": "View all accounts", "stats": {"totalAccounts": "Total OAs", "activeAccounts": "Active", "totalFollowers": "Total Followers", "newFollowersToday": "+12 today", "messagesSent": "Messages sent", "messagesToday": "+89 today", "engagementRate": "Engagement rate", "increaseFromLastWeek": "+2.1% from last week"}}, "accounts": {"title": "Manage Zalo OA", "description": "Connect and manage Zalo Official Accounts", "connectNew": "Connect new OA", "searchPlaceholder": "Search by OA name...", "filters": {"title": "Filters", "advanced": "Advanced filters"}, "list": {"title": "Account list", "description": "Total {{count}} accounts", "noData": "No accounts yet"}, "table": {"name": "OA Name", "oaId": "OA ID", "followers": "Followers", "status": "Status", "lastUpdate": "Last update", "actions": "Actions"}, "actions": {"refreshToken": "Refresh <PERSON>"}, "connect": {"title": "Connect Zalo OA", "description": "Enter information to connect Zalo Official Account", "instructions": "To connect Zalo OA, you need admin privileges and information from Zalo Developer Console.", "learnMore": "Learn more", "oaId": "OA ID", "oaIdDescription": "Zalo Official Account ID (can be found in Zalo OA Manager)", "oaIdPlaceholder": "Enter OA ID...", "name": "OA Name", "nameDescription": "Display name of the Official Account", "namePlaceholder": "Enter display name...", "accessToken": "Access Token", "accessTokenDescription": "Access token from Zalo Developer Console", "accessTokenPlaceholder": "Enter access token...", "refreshToken": "Refresh <PERSON>", "refreshTokenDescription": "Refresh token to renew access token", "refreshTokenPlaceholder": "Enter refresh token...", "avatar": "Avatar URL (Optional)", "avatarDescription": "OA avatar image URL", "avatarPlaceholder": "https://...", "submit": "Connect", "help": {"title": "Need help?", "description": "Refer to the Zalo Official Account connection guide", "viewDocs": "View documentation", "step1": "1. Access Zalo Developer Console and create application", "step2": "2. Get OA ID from Official Account management", "step3": "3. Generate access token and refresh token", "step4": "4. Configure webhook URL if needed"}, "page": {"title": "Connect Zalo Official Account", "description": "Choose the connection method that suits your needs", "methods": {"oauth": {"title": "<PERSON><PERSON> v4", "value": "Recommended", "description": "Connect via Zalo OAuth v4"}, "api": {"title": "Zalo API Explorer", "value": "Manual", "description": "Connect using Access Token and Refresh Token", "formTitle": "Zalo API Explorer", "formDescription": "Enter Access Token and Refresh Token from Zalo Developer Console", "accessTokenLabel": "Access Token *", "accessTokenPlaceholder": "Enter access token", "refreshTokenLabel": "Refresh <PERSON> *", "refreshTokenPlaceholder": "Enter refresh token", "connectButton": "Connect", "backButton": "Back"}}, "success": "Official Account connected successfully!", "error": "An error occurred while connecting"}}}, "followers": {"title": "Manage Followers", "description": "Followers of {{name}}", "descriptionDefault": "Manage followers list", "export": "Export", "sync": "Sync", "searchPlaceholder": "Search by name, phone...", "stats": {"totalFollowers": "Total Followers", "activeFollowers": "Active", "newThisWeek": "New this week", "selected": "Selected", "interacting": "Interacting", "increase": "Increase 15%"}, "actions": {"sendMessage": "Send message", "addTag": "Add tag"}, "bulkActions": {"title": "Bulk actions", "description": "Perform action for {{count}} selected followers", "addTag": "Add tag", "sendMessage": "Send message", "addTagVip": "Add \"VIP\" tag", "addTagNewCustomer": "Add \"New customer\" tag", "sendBulkMessage": "Send bulk message"}, "table": {"name": "Name", "phone": "Phone", "followDate": "Follow date", "lastInteraction": "Last interaction", "tags": "Tags", "status": "Status", "actions": "Actions"}}, "groups": {"title": "Manage Groups", "description": "Manage Zalo groups and members", "searchPlaceholder": "Search groups...", "stats": {"totalGroups": "Total Groups", "activeGroups": "Active", "totalMembers": "Total Members", "newMembers": "New Members"}, "table": {"name": "Group Name", "members": "Members", "status": "Status", "createdAt": "Created At", "lastActivity": "Last Activity", "actions": "Actions"}, "filters": {"title": "Filters", "byOA": "By OA", "byStatus": "By Status", "byActivity": "By Activity"}}, "zns": {"title": "ZNS Templates", "description": "Manage Zalo Notification Service templates", "createTemplate": "Create Template", "searchPlaceholder": "Search templates...", "createNew": "Create New", "collapse": "Collapse", "stats": {"totalTemplates": "Total Templates", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "avgCost": "Avg Cost", "perMessage": "Per message", "newTemplates": "+2 new templates", "readyToUse": "Ready to use", "underReview": "Under review"}, "table": {"template": "Template", "templateId": "Template ID", "status": "Status", "quality": "Quality", "cost": "Cost", "params": "Parameters", "updated": "Updated", "actions": "Actions"}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "disabled": "Disabled"}, "quality": {"high": "High", "normal": "Normal", "low": "Low"}, "sync": {"success": "Successfully synced {{count}} ZNS templates from Zalo API", "error": "Failed to sync ZNS templates: {{error}}"}, "create": {"title": "Create ZNS Template", "success": "Template created successfully", "description": "Create new ZNS notification template", "instructions": {"title": "ZNS Template Creation Guide", "description": "Template will be sent to Zalo for approval before use. Content must comply with Zalo's ZNS regulations. Use {param_name} to mark dynamic parameters."}, "form": {"oaLabel": "Select Official Account", "oaHelp": "Choose OA to create template", "oaPlaceholder": "Select Official Account...", "nameLabel": "Template Name", "nameHelp": "Descriptive name for template (internal use only)", "namePlaceholder": "Example: Order confirmation, Promotion notification...", "contentLabel": "Template Content", "contentHelp": "ZNS message content. Use {param_name} for dynamic parameters", "contentPlaceholder": "Example: Hello {customer_name}, your order #{order_id} has been confirmed with total value {total_amount} VND. Thank you for your purchase!"}}, "template": {"form": {"steps": {"basicInfo": "Basic Information", "components": "Content Declaration", "review": "Submit for Review"}, "validation": {"incompleteTemplate": "Template needs to be completed according to requirements"}, "basicInfo": {"title": "Basic Information", "description": "Declare the information below to create ZNS", "templateName": {"label": "ZNS Template Name", "placeholder": "E.g.: Order confirmation notification", "required": "Template name cannot be empty"}, "officialAccount": {"label": "Select Official Account", "placeholder": "Select Official Account...", "required": "Please select Official Account", "loading": "Loading OA list..."}, "contentType": {"label": "Select ZNS content type", "placeholder": "Select content type", "required": "Please select content type"}, "templateType": {"label": "Select template type", "placeholder": "Select template type", "required": "Please select template type"}, "note": {"label": "Note", "placeholder": "Additional notes about template..."}}, "components": {"title": "Content Declaration", "description": "Design your ZNS content", "sections": {"header": "<PERSON><PERSON>", "body": "ZNS Content", "footer": "Action Buttons"}, "empty": "Please design", "notImplemented": "Component not implemented"}, "review": {"title": "Submit for Review", "description": "Review information and submit template for approval", "templateInfo": {"title": "Template Information", "name": "Name", "type": "Type", "tag": "Tag", "note": "Note", "components": "Components", "parameters": "Parameters"}, "parameters": {"title": "Template Parameters", "name": "Parameter", "type": "Parameter Type", "sampleValue": "Sample Value", "empty": "No parameters found"}}, "actions": {"continue": "Continue", "create": "Create", "back": "Back", "cancel": "Cancel", "creating": "Creating..."}}, "types": {"1": "Custom ZNS", "2": "Authentication ZNS", "3": "Payment Request ZNS", "4": "Payment ZNS", "5": "Voucher ZNS"}, "tags": {"TRANSACTION": "Transaction (Level 1)", "CUSTOMER_CARE": "Customer Care (Level 2)", "PROMOTION": "Promotion (Level 3)", "OTP": "OTP (Level 1)"}}, "parameters": {"types": {"customer": "Customer Name (30)", "phone": "Phone Number (15)", "address": "Address (200)", "id": "ID Code (30)", "personal": "Personal Pronoun (30)", "status": "Transaction Status (30)", "contact": "Contact Information (50)", "time": "Gender / Title (5)", "product": "Product Name / Brand (200)", "amount": "Quantity / Amount (20)", "duration": "Duration (20)", "otp": "OTP (10)", "url": "URL (200)", "money": "Currency (VND) (12)", "bank_note": "Bank transfer note (90)"}, "sampleValues": {"customer": "<PERSON>", "phone": "**********", "address": "123 ABC Street, District 1, HCMC", "id": "CODE123", "personal": "Mr./Ms.", "status": "Confirmed", "contact": "<EMAIL>", "time": "Mr.", "product": "iPhone 15 Pro Max", "amount": "2", "duration": "30 minutes", "otp": "123456", "url": "https://example.com", "money": "1,500,000", "bank_note": "Payment for order #12345", "default": "Value {{paramName}}"}, "displayNames": {"customer": "Customer Name", "phone": "Phone Number", "address": "Address", "id": "ID Code", "personal": "Personal Pronoun", "status": "Transaction Status", "contact": "Contact Information", "time": "Gender/Title", "product": "Product Name/Brand", "amount": "Quantity/Amount", "duration": "Duration", "otp": "OTP Code", "url": "URL", "money": "Currency (VND)", "bank_note": "Bank transfer note"}, "otpDisplayName": "OTP Verification Code"}, "componentSelector": {"components": {"images": {"name": "Images", "description": "Add images to ZNS"}, "logo": {"name": "Logo", "description": "Brand logo"}, "title": {"name": "Title", "description": "Main message title"}, "paragraph": {"name": "Paragraph", "description": "Text content"}, "otp": {"name": "OTP", "description": "OTP verification code"}, "table": {"name": "Table", "description": "Display data in table format"}, "voucher": {"name": "Voucher", "description": "Discount code/voucher"}, "payment": {"name": "Payment", "description": "Payment information"}, "rating": {"name": "Rating", "description": "Service rating"}, "buttons": {"name": "Buttons", "description": "Action buttons"}}, "defaultData": {"title": "Sample Title", "paragraph": "Sample paragraph content", "otp": "123456", "button": "Learn More", "rating": {"1": "Very Dissatisfied", "2": "Dissatisfied", "3": "Neutral", "4": "Satisfied", "5": "Very Satisfied"}}, "displayNames": {"LOGO": "Logo", "IMAGES": "Images", "TITLE": "Title", "PARAGRAPH": "Paragraph", "TABLE": "Table", "OTP": "OTP Code", "VOUCHER": "Voucher", "PAYMENT": "Payment", "RATING": "Rating", "BUTTONS": "Buttons"}, "defaultContent": {"title": "Sample Title", "paragraph": "Sample paragraph content"}, "sections": {"header": "Head<PERSON> - <PERSON><PERSON>, images", "body": "Body - ZNS content", "footer": "Footer - Action buttons", "headerRequired": "Logo, images *", "ingredients": "Ingredients"}, "templateChange": {"modal": {"title": "Confirm Template Type Change", "warning": "Are you sure you want to change the template type?", "description": "All current data will be deleted and cannot be recovered. You will need to set up everything from scratch.", "cancel": "Cancel", "confirm": "Confirm"}}, "componentNames": {"logo": "Logo", "images": "Images", "title": "Title", "paragraph": "Paragraph", "table": "Table", "otp": "OTP Code", "voucher": "Voucher", "payment": "Payment", "rating": "Rating", "buttons": "Buttons"}, "validation": {"headerLogoImageExclusive": "Header can only contain Logo or Images, not both", "required": "{{component}} is required", "minRequired": "{{component}} requires at least {{min}} component(s) (current: {{current}})", "maxExceeded": "{{component}} allows maximum {{max}} component(s) (current: {{current}})", "voucherHeaderRequired": "Voucher template must have at least one: Logo or Images"}}, "components": {"common": {"delete": "Delete component", "required": "Required", "optional": "Optional", "maxLength": "Maximum {{max}} characters"}, "title": {"label": "Title", "help": "Main message title", "placeholder": "Enter title..."}, "buttons": {"label": "Action Buttons", "help": "Action buttons", "types": {"1": "Open URL in app", "2": "Make phone call", "3": "Open URL in browser", "4": "Share", "5": "Copy", "6": "Open app", "7": "Register", "8": "Order", "9": "Other"}, "fields": {"type": "Button type", "title": "Button content", "url": "Link URL", "phone": "Phone number"}, "placeholders": {"selectType": "Select button type", "title": "Learn more", "url": "Enter URL (e.g.: https://example.com)", "phone": "Enter phone number (e.g.: **********)"}, "validation": {"phoneValid": "Enter valid phone number", "urlValid": "Enter full URL including http:// or https://"}}, "images": {"label": "Images", "help": "Only 1 image allowed", "upload": {"placeholder": "Drag & drop or upload image - PNG, JPG format. Size: 16:9, max 500 KB", "replace": "Replace current image", "uploading": "Uploading...", "uploaded": "✓ Uploaded", "error": "Error: {{error}}"}, "validation": {"notImage": "File {{name}} is not an image"}}, "logo": {"label": "Logo", "help": "Logo for light and dark themes"}, "paragraph": {"label": "Paragraph", "help": "Text content", "placeholder": "Thank you for purchasing {{product_name}} at our store."}, "otp": {"label": "Content with OTP code", "help": "Content with OTP code"}, "table": {"label": "Table", "help": "Display data in table format", "description": "Table and Paragraph components can change positions", "addRow": "Add Row", "placeholders": {"title": "Order Code", "value": "{{order_code}}"}}, "voucher": {"label": "Voucher", "help": "Discount code/voucher", "description": "Voucher information", "fields": {"title": "Title", "condition": "Application Conditions", "startDate": "Start Date", "endDate": "Expiry Date", "voucherCode": "Voucher Code", "displayCode": "Display Voucher Code"}, "placeholders": {"title": "Save 70,000đ", "condition": "For orders over 200K", "startDate": "Select start date", "endDate": "Select expiry date", "voucherCode": "&lt;voucher_code&gt;"}, "displayTypes": {"barcode": "Barcode", "qrcode": "QR code", "textcode": "Text code"}}, "payment": {"label": "Payment", "help": "Payment information", "description": "Payment information", "fields": {"bank": "Bank", "accountName": "Account Name", "accountNumber": "Account Number", "amount": "Amount (VND)", "note": "Transfer Note"}, "placeholders": {"selectBank": "-- Select Bank", "accountName": "<PERSON>", "accountNumber": "**************", "amount": "<amount>", "note": "<amount>"}, "validation": {"accountNameRequired": "Account name cannot be empty", "accountNameLength": "Account name must be 1-100 characters", "accountNumberRequired": "Account number cannot be empty", "accountNumberLength": "Account number must be 1-100 characters", "amountInvalid": "Invalid amount", "amountMin": "Minimum amount is 2,000 VND", "amountMax": "Maximum amount is 500,000,000 VND", "noteMaxLength": "Transfer note maximum 90 characters"}, "loading": {"banks": "Loading bank list..."}, "errors": {"loadBanks": "Unable to load bank list", "loadBanksGeneral": "Error loading bank list"}, "hints": {"accountName": "Minimum 1 character, maximum 100 characters", "accountNumber": "Minimum 1 character, maximum 100 characters"}}, "rating": {"label": "Rating", "help": "Service rating", "description": "Service rating", "title": "Rating", "fields": {"scale": "Rating Scale", "title": "Title", "action": "Action", "question": "Question", "answers": "Answers", "thanks": "Thank You Message", "description": "Description"}, "placeholders": {"title": "Enter title", "question": "E.g.: What can we improve?", "answer1": "Better packaging", "answer2": "Better product quality", "thanks": "Thank you for your feedback!", "description": "All your feedback is valuable. We will continue to strive to serve you better."}, "actions": {"addDetail": "Add Details", "addAnswer": "Add"}, "defaultTitles": {"1": "Very Dissatisfied", "2": "Dissatisfied", "3": "Neutral", "4": "Satisfied", "5": "Very Satisfied"}}}, "preview": {"title": "ZNS Preview", "empty": "Please design", "noContent": "Please design", "imageAlt": "Preview image", "imageCounter": "{{current}}/{{total}}", "voucher": {"title": "DISCOUNT VOUCHER", "defaultName": "20% OFF", "defaultCondition": "Apply to next order", "code": "Voucher code:", "validFrom": "<PERSON>id from", "validUntil": "Valid until"}, "rating": {"title": "Service Rating", "subtitle": "Please rate your experience", "submit": "Submit Rating"}, "payment": {"title": "Payment Information", "amount": "Amount", "bankName": "Bank", "accountNumber": "Account Number", "accountName": "Account Name", "transferNote": "Transfer Note"}, "table": {"noData": "No data"}, "buttons": {"defaultText": "Click here"}}, "messages": {"success": {"create": "ZNS template created successfully!", "update": "ZNS template updated successfully!", "delete": "ZNS template deleted successfully!"}, "error": {"create": "Failed to create ZNS template. Please try again.", "update": "Failed to update ZNS template. Please try again.", "delete": "Failed to delete ZNS template. Please try again.", "loadOA": "Unable to load Official Account list", "loadTags": "Unable to load template tags list", "selectOA": "Please select Official Account"}}, "actions": {"create": "Create New", "edit": "Edit", "delete": "Delete", "preview": "Preview", "save": "Save", "cancel": "Cancel", "next": "Next", "previous": "Previous", "submit": "Submit for Review", "add": "Add", "remove": "Remove", "upload": "Upload", "select": "Select"}, "templates": {"custom": "Custom", "authentication": "Authentication", "payment": "Payment", "voucher": "Voucher", "rating": "Rating", "selectType": "Select template type", "step1": "Step 1: Select type", "step2": "Step 2: Design", "step3": "Step 3: Parameters", "step4": "Step 4: Preview"}, "notifications": {"createSuccess": "Template created successfully", "createError": "Failed to create template", "updateSuccess": "Template updated successfully", "updateError": "Failed to update template", "deleteSuccess": "Template deleted successfully", "deleteError": "Failed to delete template", "uploadSuccess": "Upload successful", "uploadError": "Upload failed"}, "validation": {"templateNameRequired": "Template name is required", "templateTypeRequired": "Template type is required", "componentRequired": "At least one component is required", "titleRequired": "Title is required", "contentRequired": "Content is required", "imageRequired": "Image is required", "buttonTitleRequired": "Button title is required", "buttonUrlRequired": "Button URL is required", "parameterNameRequired": "Parameter name is required", "parameterTypeRequired": "Parameter type is required", "required": "This field is required", "maxLength": "Must not exceed {max} characters", "minLength": "Must be at least {min} characters", "invalidUrl": "Invalid URL", "invalidEmail": "Invalid email", "invalidPhone": "Invalid phone number"}}, "oaManagement": {"title": "Zalo OA Management", "navigation": {"content": "Content", "zns": "ZNS"}, "tabs": {"articles": "Articles", "znsTemplates": "ZNS Templates", "znsConfiguration": "ZNS Configuration", "znsResources": "ZNS Resources"}, "configuration": {"title": "ZNS Configuration", "description": "Configure settings for ZNS templates", "settings": "Settings", "general": "General", "advanced": "Advanced", "save": "Save configuration", "reset": "Reset", "export": "Export configuration", "import": "Import configuration"}, "resources": {"title": "ZNS Resources", "description": "Manage images, logos and other resources", "images": "Images", "logos": "Logos", "files": "Files", "upload": "Upload", "manage": "Manage", "delete": "Delete", "edit": "Edit", "usage": "Usage Count"}, "chips": {"officialAccount": "Official Account", "templatesZalo": "Temp<PERSON> <PERSON><PERSON>", "templateTags": "Template Tags", "rating": "Rating"}, "templateTags": {"title": "Template Tags", "description": "ZNS content types that the Official Account is allowed to send:", "loading": "Loading template tags...", "noData": "No template tags available", "note": "Based on the OA's ZNS sending quality, <PERSON><PERSON> will automatically adjust the content types the OA can send.", "types": {"TRANSACTION": "Transaction (Level 1)", "CUSTOMER_CARE": "Customer Care (Level 2)", "PROMOTION": "Promotion (Level 3)"}}, "templates": {"title": "Temp<PERSON> <PERSON><PERSON>", "loading": "Loading templates...", "noData": "No templates available", "table": {"templateId": "Template ID", "templateName": "Template Name", "status": "Status", "templateTag": "Template Tag", "price": "Price", "createdTime": "Created Time", "actions": "Actions"}, "actions": {"viewDetail": "View Details", "ratings": "Ratings"}}, "ratings": {"title": "Ratings for Template ID: {{templateId}}", "loading": "Loading ratings data...", "noData": "No ratings data available for this template", "backToTemplates": "← Back to Templates", "description": "Ratings data for template {{templateId}}"}, "templateDetail": {"title": "Template Details", "loading": "Loading template details...", "notFound": "Temp<PERSON> not found", "basicInfo": {"title": "Basic Information", "templateId": "Template ID", "templateName": "Template Name", "status": "Status", "templateTag": "Template Tag", "price": "Price"}, "additionalDetails": {"title": "Additional Details", "templateQuality": "Template Quality", "timeout": "Timeout", "applyTemplateQuota": "Apply Template <PERSON><PERSON><PERSON>", "previewUrl": "Preview URL", "viewPreview": "View Preview"}, "parameters": {"title": "Parameters", "count": "Parameters ({{count}})", "type": "Type:", "length": "Length:", "acceptNull": "Accept Null:", "required": "Required", "yes": "Yes", "no": "No"}, "buttons": {"title": "Buttons", "count": "Buttons ({{count}})", "type": "Type {{type}}", "content": "Content:"}, "rejectionReason": {"title": "Rejection Reason"}, "actions": {"close": "Close"}, "status": {"REJECT": "Rejected", "APPROVED": "Approved", "PENDING": "Pending"}, "minutes": "minutes"}}, "znsImages": {"form": {"title": "Add ZNS Image", "subtitle": "Upload images/logos for ZNS templates according to Zalo regulations", "requirements": "Zalo Requirements:", "formatRequirement": "Format: PNG, JPG", "sizeRequirement": "Maximum size: 500 KB", "ratioRequirement": "Aspect ratio: 16:9 (recommended: 1920x1080px)", "limitRequirement": "Limit: 5000 images/month/app", "files": "Select Images", "uploadPlaceholder": "Drag and drop or click to upload images (PNG, JPG - max 500KB)", "description": "Description", "descriptionPlaceholder": "Enter image description...", "submit": "Upload", "invalidFile": "Invalid file", "fileRequired": "Please select at least one file", "fileValidation": "File must be PNG/JPG and not exceed 500KB", "aspectRatio": "Image must have 16:9 aspect ratio", "descriptionRequired": "Description is required", "descriptionTooLong": "Description must not exceed 255 characters", "invalidDimensions": "Image must have 16:9 aspect ratio. Recommended size: 1920x1080px", "useTemplate": "Use template", "editImage": "Edit image", "needsEditing": "Image needs editing", "editNow": "Edit now", "fileTooLarge": "File too large, needs compression or editing", "wrongRatio": "Wrong aspect ratio, needs cropping"}, "table": {"title": "ZNS Images List", "filename": "Filename", "description": "Description", "type": "Type", "size": "Size", "created": "Created", "actions": "Actions", "noDescription": "No description"}}, "imageEditor": {"title": "Edit Image", "loading": "Loading image...", "crop": "Crop", "filter": "Filter", "preview": "Preview", "cropControls": "Crop Controls", "autoFit": "Auto Fit Ratio", "filterControls": "Filters", "brightness": "Brightness", "contrast": "Contrast", "saturation": "Saturation", "blur": "Blur", "save": "Save"}, "templates": {"title": "Choose Image Template", "znsDesc": "Templates for ZNS images (16:9)", "logoDesc": "Templates for logos (400x96)", "customText": "Custom Text", "textPlaceholder": "Enter your text...", "preview": "Preview", "selectToPreview": "Select template to preview", "promoTemplate1": "Promotion 1", "promoDesc1": "Promotion template with blue gradient", "promoTemplate2": "Promotion 2", "promoDesc2": "Promotion template with red gradient", "infoTemplate1": "Notification 1", "infoDesc1": "Simple notification template", "logoTemplate1": "Simple Logo 1", "logoDesc1": "Logo with blue gradient background", "logoTemplate2": "Simple Logo 2", "logoDesc2": "Logo with white background", "logoTemplate3": "Modern Logo", "logoDesc3": "Logo with modern design"}, "personalCampaigns": {"title": "Zalo Personal Campaigns", "description": "Man<PERSON> personal campaigns", "table": {"name": "Campaign Name", "type": "Campaign Type", "messageType": "Message Type", "status": "Status", "progress": "Progress", "createdAt": "Created At"}, "campaignTypes": {"sendAll": "Send Bulk Messages", "crawlFriends": "Crawl Friends List", "crawlGroups": "Crawl Groups List", "sendFriendRequest": "Send Friend Requests"}, "messageTypes": {"text": "Text", "image": "Image", "qr_code": "QR Code"}, "status": {"draft": "Draft", "scheduled": "Scheduled", "active": "Active", "paused": "Paused", "completed": "Completed", "failed": "Failed"}, "actions": {"start": "Start", "pause": "Pause"}, "messages": {"createSuccess": "Campaign created successfully"}, "create": {"title": "Create new <PERSON><PERSON> personal campaign", "subtitle": "Choose the type of campaign you want to create", "configureSubtitle": "Configure campaign details", "formNotImplemented": "Form for this campaign type is under development."}, "forms": {"crawFriendsList": {"title": "Crawl Friends List", "description": "Create campaign to collect friends list from Zalo account", "campaignName": "Campaign Name", "campaignNamePlaceholder": "Enter crawl friends list campaign name", "accounts": "Account List", "accountsPlaceholder": "Select Zalo accounts to crawl friends list", "submit": "Create Campaign"}}}, "oaCampaigns": {"title": "OA Campaigns", "description": "Manage Zalo OA campaigns", "table": {"name": "Campaign Name", "type": "Campaign Type", "status": "Status", "progress": "Progress", "createdAt": "Created At"}, "actions": {"start": "Start", "pause": "Pause"}, "messages": {"createSuccess": "Campaign created successfully"}, "create": {"title": "Create new OA campaign", "subtitle": "Choose the type of campaign you want to create", "configureSubtitle": "Configure campaign details", "createButton": "Create Campaign"}, "addMemberToGroup": {"title": "Add members to group", "description": "Form for adding members to group is under development."}, "sendOAMessage": {"title": "Send OA message", "description": "Form for sending OA messages is under development."}, "sendZNSMessage": {"title": "Send ZNS message", "description": "Form for sending ZNS messages is under development."}}, "chat": {"title": "Cha<PERSON>", "description": "Manage Zalo conversations", "selectAccount": "Select Zalo account", "chooseAccount": "Choose account", "noActiveAccounts": "No active accounts", "selectAccountFirst": "Please select a Zalo account first", "selectAccountDescription": "Select a Zalo account to start chatting with customers", "searchContacts": "Search contacts", "newChat": "New chat", "selectContactToStart": "Select a contact to start conversation", "selectContactDescription": "Choose a contact from the left list to start conversation", "newConversation": "New conversation", "noContacts": "No contacts yet", "noContactsFound": "No contacts found", "noConversations": "No conversations yet", "noConversationsFound": "No conversations found", "tryDifferentSearch": "Try different search terms", "searchResults": "{{count}} search results", "totalContacts": "{{count}} contacts", "totalConversations": "{{count}} conversations", "online": "Online", "offline": "Offline", "lastSeen": "Last seen {{time}}", "justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "you": "You", "image": "Image", "file": "File", "message": "Message", "noMessages": "No messages yet", "startConversation": "Start conversation with {{name}}", "selectContact": "Select contact to view messages", "typeMessage": "Type a message...", "typeMessageTo": "Type a message to {{name}}...", "sendMessage": "Send message", "newLine": "New line", "attachFile": "Attach file", "emoji": "<PERSON><PERSON><PERSON>", "addTag": "Add tag", "contactInfo": "Contact info", "tags": "Tags", "notes": "Notes", "attachedFiles": "Attached files", "replyTo": "Reply to", "scrollToBottom": "Scroll to bottom", "connected": "Connected", "showContacts": "Show contacts list", "backToList": "Back to list", "expandSidebar": "Expand sidebar", "collapseSidebar": "Collapse sidebar", "keyboardShortcuts": "Keyboard shortcuts", "unknownSender": "Unknown sender", "errors": {"loadAccountsFailed": "Failed to load accounts", "loadContactsFailed": "Failed to load contacts", "loadConversationsFailed": "Failed to load conversations", "loadMessagesFailed": "Failed to load messages", "sendMessageFailed": "Failed to send message", "createContactFailed": "Failed to create contact", "uploadFailed": "Failed to upload file", "updateSettingsFailed": "Failed to update settings", "tooManyFiles": "Maximum {{max}} files allowed", "fileTooLarge": "File {{name}} is too large. Maximum size is {{max}}MB", "invalidFileType": "File type {{name}} is not supported"}, "success": {"messageSent": "Message sent", "fileUploaded": "File uploaded", "contactCreated": "Contact created", "settingsUpdated": "Settings updated"}}}, "tag": {"name": "Tag Name", "color": "Color", "objectCount": "Object Count", "validation": {"nameRequired": "Tag name is required", "colorInvalid": "Color code must be in HEX format (e.g., #FF0000)"}}, "emailMarketing": {"title": "Email", "description": "Manage email campaigns and templates", "totalTemplates": "Total Templates", "manage": "Manage Emails"}}, "segment": {"title": "Segmentation", "description": "Segment customers by different criteria", "manage": "Manage Segments", "addNew": "Add New Segment", "edit": "Edit Segment", "detail": "Segment Details", "name": "Segment Name", "audience": "Audience", "status": "Status", "totalContacts": "Total Contacts", "conditions": "Conditions", "confirmDelete": "Are you sure you want to delete this segment?", "totalSegments": "Total Segments", "types": {"demographic": "Demographic", "behavioral": "Behavioral", "geographic": "Geographic", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Not Contains", "greater_than": "Greater Than", "less_than": "Less Than", "between": "Between"}, "form": {"title": "Segment Information", "name": "Segment Name", "audience": "Audience", "status": "Status", "totalContacts": "Total Contacts", "conditions": "Conditions", "confirmDelete": "Are you sure you want to delete this segment?", "totalSegments": "Total Segments", "types": {"demographic": "Demographic", "behavioral": "Behavioral", "geographic": "Geographic", "psychographic": "Psychographic"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Not Contains", "greater_than": "Greater Than", "less_than": "Less Than", "between": "Between"}, "form": {"title": "Segment Information", "name": "Segment Name", "namePlaceholder": "Enter segment name...", "description": "Description", "descriptionPlaceholder": "Enter segment description...", "conditions": "Conditions", "conditionsDescription": "Set up conditions to segment customers", "addGroup": "Add Condition Group", "addCondition": "Add Condition", "removeGroup": "Remove Group", "removeCondition": "Remove Condition", "field": "Field", "operator": "Operator", "value": "Value", "valuePlaceholder": "Enter value...", "and": "AND", "or": "OR", "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Not Contains", "starts_with": "Starts With", "ends_with": "Ends With", "greater_than": "Greater Than", "less_than": "Less Than", "greater_than_or_equal": "Greater Than or Equal", "less_than_or_equal": "Less Than or Equal", "is_empty": "Is Empty", "is_not_empty": "Is Not Empty"}, "validation": {"nameRequired": "Segment name is required", "nameMinLength": "Segment name must be at least 2 characters", "nameMaxLength": "Segment name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters", "conditionsRequired": "At least one condition is required", "fieldRequired": "Field is required", "operatorRequired": "Operator is required", "valueRequired": "Value is required"}, "buttons": {"save": "Save", "cancel": "Close", "saveTooltip": "Save segment", "cancelTooltip": "Cancel and close form"}, "systemField": "System Field"}, "selectCustomField": "Select field..."}}, "campaign": {"title": "Campaigns", "description": "Manage marketing campaigns", "manage": "Manage Campaigns", "addNew": "Add New Campaign", "totalContacts": "Total Contacts", "startDate": "Start Date", "endDate": "End Date", "confirmDelete": "Are you sure you want to delete this campaign?", "activeCampaigns": "Active Campaigns", "types": {"email": "Email", "sms": "SMS", "push": "Push Notification", "social": "Social Media", "multi": "Multi-Channel"}, "status": {"active": "Active", "paused": "Paused", "completed": "Completed", "scheduled": "Scheduled", "draft": "Draft"}, "metrics": {"sent": "<PERSON><PERSON>", "delivered": "Delivered", "opened": "Opened", "clicked": "Clicked", "converted": "Converted", "bounced": "Bounced", "unsubscribed": "Unsubscribed"}, "form": {"nameLabel": "Campaign Name", "namePlaceholder": "Enter campaign name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter campaign description", "typeLabel": "Type", "statusLabel": "Status", "segmentLabel": "Segment", "segmentPlaceholder": "Select segment", "audienceLabel": "Audience", "audiencePlaceholder": "Select audience", "startDateLabel": "Start Date", "endDateLabel": "End Date", "endDateOptional": "(Optional)"}}, "tags": {"title": "Tag Management", "description": "Create and manage tags for customers", "addNew": "Add New Tag", "edit": "Edit Tag", "name": "Tag Name", "status": "Status", "confirmDelete": "Are you sure you want to delete this tag?", "statuses": {"active": "Active", "inactive": "Inactive"}, "form": {"name": "Tag Name", "namePlaceholder": "Enter tag name", "color": "Color Code", "colorPlaceholder": "Choose color for tag", "randomColor": "Random Color"}, "validation": {"nameRequired": "Tag name is required", "colorInvalid": "Color code must be in HEX format (e.g., #FF0000)"}, "deleteSuccess": "Tag deleted successfully", "deleteError": "Failed to delete tag", "selectToDelete": "Please select tags to delete", "bulkDeleteSuccess": "Successfully deleted {{count}} tags", "bulkDeleteError": "Failed to delete multiple tags", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected tags?", "totalTags": "Total Tags", "manage": "Manage Tags"}, "tag": {"name": "Tag Name", "color": "Color", "objectCount": "Object Count", "validation": {"nameRequired": "Tag name is required", "colorInvalid": "Color code must be in HEX format (e.g., #FF0000)"}}, "customField": {"configId": "Field Identifier Name", "title": "Custom Fields", "description": "Manage custom fields", "adminDescription": "Manage system custom fields", "add": "Add Custom Field", "edit": "Edit Custom Field", "addForm": "Add New Custom Field", "editForm": "Edit Custom Field", "component": "Component Type", "components": {"input": "Input Field", "textarea": "Text Area", "select": "Select List", "checkbox": "Checkbox", "radio": "Radio Button", "date": "Date", "number": "Number", "file": "File", "multiSelect": "Multi Select"}, "type": "Data Type", "type.string": "Text", "type.number": "Number", "type.boolean": "Yes/No", "type.date": "Date", "type.object": "Object", "type.array": "Array", "types": {"text": "Text", "number": "Number", "boolean": "Yes/No", "date": "Date", "select": "Select Box", "object": "Object", "array": "Array", "string": "Text"}, "name": "Field Name", "label": "Label", "placeholder": "Placeholder", "defaultValue": "Default Value", "options": "Options", "required": "Required", "validation": {"minLength": "Minimum Length", "maxLength": "Maximum Length", "pattern": "Pattern", "min": "Minimum Value", "max": "Maximum Value"}, "form": {"componentRequired": "Please select component type", "labelRequired": "Please enter label", "typeRequired": "Please select data type", "idRequired": "Please enter field identifier name", "labelPlaceholder": "Enter display label", "descriptionPlaceholder": "Enter description for this field", "placeholderPlaceholder": "Enter placeholder", "defaultValuePlaceholder": "Enter default value", "optionsPlaceholder": "Enter options, separated by commas or JSON format", "selectOptionsPlaceholder": "Enter values in format: Name|Value, Each pair on a new line. Example:\na|1\nb|2", "booleanDefaultPlaceholder": "Select default value", "dateDefaultPlaceholder": "Select default date", "description": "Description", "labelTagRequired": "Please add at least one label", "fieldIdLabel": "Field Identifier Name", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "Field Display Name", "displayNamePlaceholder": "Enter display name for this field", "displayNameRequired": "Please enter field display name", "labelInputPlaceholder": "Enter label and press Enter", "tagsCount": "labels added", "patternSuggestions": "Common pattern suggestions:", "defaultValue": "Default Value", "minLength": "Minimum Length", "maxLength": "Maximum Length", "pattern": "Pattern", "options": "Options", "min": "Minimum Value", "max": "Maximum Value", "minValue": "Minimum Value", "maxValue": "Maximum Value", "placeholder": "Placeholder", "required": "Required", "labels": "Labels", "showAdvancedSettings": "Show Advanced Settings"}, "createSuccess": "Custom field created successfully", "createError": "Error creating custom field", "updateSuccess": "Custom field updated successfully", "updateError": "Error updating custom field", "deleteSuccess": "Custom field deleted successfully", "deleteError": "Error deleting custom field", "loadError": "Error loading custom field", "booleanValues": {"true": "Yes", "false": "No"}, "patterns": {"email": "Email", "phoneVN": "Vietnam Phone", "phoneIntl": "International Phone", "postalCodeVN": "Vietnam Postal Code", "lettersOnly": "Letters Only", "numbersOnly": "Numbers Only", "alphanumeric": "Letters and Numbers", "noSpecialChars": "No Special Characters", "url": "URL", "ipv4": "IPv4", "strongPassword": "Strong Password", "vietnameseName": "Vietnamese Name", "studentId": "Student ID", "nationalId": "National ID", "taxCode": "Tax Code", "dateFormat": "Date (dd/mm/yyyy)", "timeFormat": "Time (hh:mm)", "hexColor": "Hex Color", "base64": "Base64", "uuid": "UUID", "filename": "Filename", "urlSlug": "URL Slug", "variableName": "Variable Name", "creditCard": "Credit Card Number", "qrCode": "QR Code", "gpsCoordinate": "GPS Coordinate", "rgbColor": "RGB Color", "domain": "Domain", "decimal": "Decimal", "barcode": "Barcode"}, "confirmDeleteMessage": "Are you sure you want to delete this custom field?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected custom fields?", "bulkDeleteSuccess": "Successfully deleted {{count}} custom fields", "bulkDeleteError": "Error occurred while deleting custom fields", "selectedItems": "Selected {{count}} items", "totalFields": "Total Custom Fields", "manage": "Manage Custom Fields", "noDescription": "No description"}, "reports": {"title": "Reports", "description": "View marketing activity reports", "overview": "Overview", "campaigns": "Campaigns", "audiences": "Audiences", "segments": "Segments", "performance": "Performance", "period": "Time Period", "exportData": "Export Data", "metrics": {"totalCampaigns": "Total Campaigns", "activeCampaigns": "Active Campaigns", "totalAudiences": "Total Audiences", "totalSegments": "Total Segments", "conversionRate": "Conversion Rate", "engagementRate": "Engagement Rate"}, "periods": {"today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "custom": "Custom"}, "totalReports": "Total Reports", "view": "View Reports"}, "templateEmail": {"title": "Email Templates", "description": "Create and manage email templates", "manage": "Manage Email Templates", "addNew": "Add New Email Template", "edit": "Edit <PERSON><PERSON>late", "detail": "Email Template Details", "name": "Template Name", "subject": "Email Subject", "content": "Email Content", "type": "Template Type", "status": "Status", "confirmDelete": "Are you sure you want to delete this email template?", "types": {"welcome": "Welcome", "newsletter": "Newsletter", "promotion": "Promotion", "notification": "Notification", "transactional": "Transactional", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "form": {"nameLabel": "Template Name", "namePlaceholder": "Enter template name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter template description", "subjectLabel": "Email Subject", "subjectPlaceholder": "Enter email subject", "contentLabel": "Email Content", "contentPlaceholder": "Enter email content", "typeLabel": "Template Type", "statusLabel": "Status"}}, "emailMarketing": {"title": "Email", "description": "Manage email campaigns and templates", "totalTemplates": "Total Templates", "manage": "Manage Emails"}, "smsMarketing": {"title": "SMS", "description": "Send and manage SMS campaigns", "totalCampaigns": "Total Campaigns", "manage": "Manage SMS", "comingSoon": "SMS Marketing feature is under development. Please check back later!"}, "googleAds": {"title": "Google Ads", "description": "Integrate and manage Google Ads campaigns", "totalAccounts": "Accounts", "manage": "Manage Google Ads", "comingSoon": "This feature is under development. Please check back later!", "connectAccount": "Connect Account", "connectAccountDescription": "Account connection form will be developed in the next phase.", "name": "Account Name", "customerId": "Customer ID", "viewCampaigns": "View Campaigns", "accountCreated": "Google Ads account has been created successfully", "accountCreateError": "Error creating Google Ads account", "accountUpdated": "Google Ads account has been updated", "accountUpdateError": "Error updating Google Ads account", "accountDeleted": "Google Ads account has been deleted", "accountDeleteError": "Error deleting Google Ads account", "accounts": "Google Ads Accounts", "campaigns": "Google Ads Campaigns", "campaignName": "Campaign Name", "campaignId": "Campaign ID", "campaignType": "Campaign Type", "budget": "Budget", "startDate": "Start Date", "endDate": "End Date", "createCampaign": "Create New Campaign", "editCampaign": "Edit Campaign", "campaignCreated": "Google Ads campaign has been created successfully", "campaignCreateError": "Error creating Google Ads campaign", "campaignUpdated": "Google Ads campaign has been updated", "campaignUpdateError": "Error updating Google Ads campaign", "campaignDeleted": "Google Ads campaign has been deleted", "campaignDeleteError": "Error deleting Google Ads campaign", "campaignEnabled": "Google Ads campaign has been enabled", "campaignPaused": "Google Ads campaign has been paused", "campaignStatusUpdateError": "Error updating Google Ads campaign status", "campaignTypes": {"SEARCH": "Search", "DISPLAY": "Display", "VIDEO": "Video", "SHOPPING": "Shopping", "APP": "App", "PERFORMANCE_MAX": "Performance Max"}, "campaignStatuses": {"ENABLED": "Enabled", "PAUSED": "Paused", "REMOVED": "Removed", "DRAFT": "Draft"}, "tabs": {"accounts": "Accounts", "campaigns": "Campaigns", "performance": "Performance"}}, "articles": {"title": "Article Management", "description": "Create and manage marketing articles", "list": "Article List", "listDescription": "Manage all your marketing articles", "create": "Create Article", "edit": "Edit Article", "update": "Update Article", "publish": "Publish", "archive": "Archive", "articleTitle": "Title", "titlePlaceholder": "Enter article title", "excerpt": "Excerpt", "excerptPlaceholder": "Enter brief excerpt", "author": "Author", "authorPlaceholder": "Enter author name", "content": "Content", "contentPlaceholder": "Enter article content", "imageUrl": "Featured Image", "imageUrlPlaceholder": "Enter image URL or upload", "tags": "Tags", "tagsPlaceholder": "Enter tags and press Enter", "relatedArticles": "Related Articles", "selectRelatedArticles": "Select up to 6 related articles", "basicInfo": "Basic Information", "metadata": "Additional Information", "stats": "Statistics", "views": "views", "likes": "likes", "status": {"draft": "Draft", "published": "Published", "archived": "Archived"}, "selectStatus": "Select status", "deleteConfirm": "Delete Article", "deleteDescription": "Are you sure you want to delete this article?", "bulkDeleteConfirm": "Delete {{count}} articles", "bulkDeleteDescription": "Are you sure you want to delete the selected articles?", "validation": {"titleRequired": "Title is required", "authorRequired": "Author is required", "contentRequired": "Content is required", "maxRelatedArticles": "Maximum 6 related articles"}}, "videos": {"title": "Video Management", "description": "Create and manage marketing videos", "list": "Video List", "listDescription": "Manage all your marketing videos", "create": "Create Video", "edit": "Edit Video", "update": "Update Video", "publish": "Publish", "archive": "Archive", "videoTitle": "Title", "titlePlaceholder": "Enter video title", "videoDescription": "Description", "descriptionPlaceholder": "Enter video description", "url": "Video URL", "urlPlaceholder": "Enter video URL", "thumbnailUrl": "<PERSON><PERSON><PERSON><PERSON>", "thumbnailUrlPlaceholder": "Enter thumbnail URL", "duration": "Duration", "durationDescription": "Format: 5:30 or 330 (seconds)", "selectCategory": "Select category", "tags": "Tags", "tagsPlaceholder": "Enter tags and press Enter", "basicInfo": "Basic Information", "videoDetails": "Video Details", "metadata": "Additional Information", "stats": "Statistics", "views": "views", "likes": "likes", "status": {"draft": "Draft", "published": "Published", "archived": "Archived"}, "category": {"tutorial": "Tutorial", "productDemo": "Product Demo", "testimonial": "Testimonial", "promotional": "Promotional", "educational": "Educational", "entertainment": "Entertainment"}, "selectStatus": "Select status", "deleteConfirm": "Delete Video", "deleteDescription": "Are you sure you want to delete this video?", "bulkDeleteConfirm": "Delete {{count}} videos", "bulkDeleteDescription": "Are you sure you want to delete the selected videos?", "validation": {"titleRequired": "Title is required", "urlRequired": "Video URL is required", "invalidUrl": "Invalid URL", "invalidDuration": "Invalid duration"}}, "email": {"title": "Email", "description": "Manage email marketing campaigns and templates", "overview": {"title": "Email", "description": "Manage email marketing campaigns and templates", "createCampaign": "Create Campaign", "manageTemplates": "Manage Templates", "viewReports": "View Reports", "stats": {"totalCampaigns": "Total Campaigns", "activeCampaigns": "Active", "totalTemplates": "Total Templates", "readyToUse": "Ready to Use", "emailsSent": "Emails Sent", "thisMonth": "This Month", "openRate": "Open Rate", "averageRate": "Average Rate"}, "quickActions": {"createCampaign": "Create Email Campaign", "createCampaignDesc": "Start a new email marketing campaign", "manageTemplates": "Manage Templates", "manageTemplatesDesc": "Create and edit email templates", "viewAnalytics": "View Analytics", "viewAnalyticsDesc": "Track email campaign performance"}, "recentActivity": {"title": "Recent Activity", "viewAll": "View All", "campaignSent": "Campaign Sent", "templateCreated": "Template Created", "reportGenerated": "Report Generated"}, "gettingStarted": {"title": "Get Started with Email Marketing", "description": "Create your first email template to start marketing campaigns", "createTemplate": "Create First Template"}}, "templates": {"title": "Email Templates", "description": "Create and manage email marketing templates", "createTemplate": "Create Template", "stats": {"totalTemplates": "Total Templates", "newTemplates": "+3 new templates", "active": "Active", "readyToUse": "Ready to Use", "draft": "Draft", "incomplete": "Incomplete", "testSent": "Test Sent", "thisWeek": "This Week"}, "table": {"template": "Template", "type": "Type", "status": "Status", "tags": "Tags", "variables": "Variables", "updated": "Updated", "actions": "Actions"}, "status": {"active": "Active", "draft": "Draft", "archived": "Archived"}, "preview": {"title": "Template Preview", "variables": "Template Variables:", "required": "Required"}, "create": {"title": "Create <PERSON><PERSON>", "description": "Create new email marketing template"}, "edit": {"title": "Edit <PERSON><PERSON>late", "description": "Edit an existing email marketing template"}, "form": {"editors": {"title": "Choose Editor Type", "emailBuilder": {"name": "Email Builder", "description": "Visual drag & drop editor"}, "richTextEditor": {"name": "Rich Text Editor", "description": "WYSIWYG text editor"}, "code": {"name": "HTML Code", "description": "Raw HTML editor"}}, "richTextEditor": {"label": "Rich Text Content", "placeholder": "Enter email content..."}, "basicInfo": {"title": "Basic Information"}, "name": {"label": "Template Name", "placeholder": "Example: January Newsletter, <PERSON> Friday Promotion..."}, "type": {"label": "Template Type", "placeholder": "Select template type"}, "subject": {"label": "Email Subject", "placeholder": "Example: 🎉 Special offer just for you!"}, "content": {"title": "Email Content", "designMode": "Design", "codeMode": "HTML"}, "htmlContent": {"label": "HTML Content", "placeholder": "<!DOCTYPE html>\\n<html>\\n<head>\\n  <title>Email Template</title>\\n</head>\\n<body>\\n  <h1>Hello {customer_name}!</h1>\\n  <p>Thank you for subscribing to our newsletter.</p>\\n</body>\\n</html>"}, "textContent": {"label": "Text Content (Optional)", "placeholder": "Hello {customer_name}!\\n\\nThank you for subscribing to our newsletter..."}, "variables": {"title": "Variables", "addButton": "Add Variable", "name": "Variable Name", "type": "Type", "typePlaceholder": "Select type", "defaultValue": "Default Value", "defaultValuePlaceholder": "Customer", "description": "Description", "descriptionPlaceholder": "Customer name"}, "tags": {"title": "Tags", "label": "Tags", "placeholder": "Enter tag and press Enter"}, "submitButton": "Create Template", "instructions": {"title": "Email Template Creation Guide", "description": "Use Email Builder to create beautiful email content. Add dynamic variables using {variable_name} syntax. Preview text will be displayed in inbox preview."}, "validation": {"nameRequired": "Template name is required", "subjectRequired": "Email subject is required", "contentRequired": "HTML content is required"}}}, "campaigns": {"syncStatus": {"tooltip": "Sync Status"}, "form": {"name": {"label": "Campaign Name", "placeholder": "e.g., Black Friday 2024 Promotion"}, "description": {"label": "Campaign Description", "placeholder": "Brief description of this campaign..."}, "audience": {"totalCount": "Total recipients:", "calculating": "Calculating...", "table": {"title": "Recipients List", "subtitle": "List of audiences from selected segments", "selectedSubtitle": "List of selected audiences", "name": "Name", "email": "Email", "phone": "Phone Number", "createdAt": "Created At", "noName": "No name", "noEmail": "No email", "noPhone": "No phone"}}}, "customer": {"table": {"title": "Customer List", "subtitle": "List of selected customers", "name": "Name", "email": "Email", "phone": "Phone Number", "noName": "No name", "noEmail": "No email", "noPhone": "No phone"}}}, "facebook": {"title": "Facebook Marketing", "description": "Manage all Facebook services", "pages": {"title": "Facebook Pages Management", "description": "Manage and monitor your business Facebook pages", "name": "Page Name", "followers": "Followers", "likes": "<PERSON>s", "engagement": "Engagement", "insights": "Insights", "managePosts": "Manage Posts", "connect": "Connect Facebook Page"}, "ads": {"title": "Facebook Ads", "description": "Manage and optimize Facebook advertising campaigns"}, "analytics": {"title": "Facebook Analytics", "description": "Analyze Facebook ads and page performance"}, "instagram": {"title": "Instagram Business", "description": "Manage Instagram business account"}, "pixel": {"title": "Facebook Pixel", "description": "Track and analyze website"}, "messenger": {"title": "Facebook Messenger", "description": "Messaging and chatbot"}, "pageMetadata": {"title": "Page Public Metadata", "description": "Analyze public data of Facebook pages", "searchPlaceholder": "Search Facebook pages...", "searchButton": "Search", "noResults": "No pages found", "loading": "Searching...", "pageInfo": {"title": "Page Information", "name": "Page Name", "category": "Category", "description": "Description", "website": "Website", "phone": "Phone", "email": "Email", "address": "Address", "founded": "Founded", "verified": "Verified", "checkmark": "Blue checkmark"}, "engagement": {"title": "Engagement Metrics", "likes": "<PERSON>s", "followers": "Followers", "checkins": "Check-ins", "talkingAbout": "Talking About", "posts": "Posts", "photos": "Photos", "videos": "Videos", "events": "Events"}, "analytics": {"title": "Detailed Analytics", "overview": "Overview", "comparison": "Comparison", "insights": "Insights", "trends": "Trends", "demographics": "Demographics", "performance": "Performance", "competitorAnalysis": "Competitor Analysis"}, "comparison": {"title": "<PERSON> Comparison", "addPage": "Add page to compare", "removePage": "Remove page", "compareMetrics": "Compare metrics", "exportReport": "Export report", "noPages": "No pages to compare"}, "insights": {"title": "Insights Report", "generateReport": "Generate report", "downloadReport": "Download report", "shareReport": "Share report", "reportType": "Report type", "dateRange": "Date range", "metrics": "Metrics", "summary": "Summary", "recommendations": "Recommendations"}, "permissions": {"title": "Access Permissions", "description": "This feature requires Page Public Metadata Access permission from Facebook", "requestPermission": "Request permission", "permissionGranted": "Permission granted", "permissionPending": "Pending approval", "permissionDenied": "Permission denied"}, "demo": {"title": "Feature Demo", "description": "Explore Facebook page analysis features", "samplePages": "Sample pages", "tryFeature": "Try feature"}}, "connection": {"status": "Connection Status"}}, "verified": "Verified", "status": {"active": "Active", "inactive": "Inactive", "connected": "Connected"}, "analytics": {"name": "Name", "impressions": "Impressions", "reach": "Reach", "clicks": "<PERSON>licks", "spend": "Spend", "conversions": "Conversions", "performance": "Performance", "efficiency": "Efficiency", "detailedReport": "Detailed Report", "export": "Export", "breakdown": "Breakdown", "exportAll": "Export All", "refresh": "Refresh", "filters": "Filters", "detailedAnalytics": "Detailed Analytics", "performanceTrends": "Performance Trends", "totalImpressions": "Total Impressions", "totalReach": "Total Reach", "totalClicks": "Total Clicks", "totalSpend": "Total Spend", "totalConversions": "Total Conversions", "avgCTR": "Average CTR", "avgCPC": "Average CPC", "avgROAS": "Average ROAS", "dateRange": {"title": "Date Range", "today": "Today", "yesterday": "Yesterday", "last7days": "Last 7 days", "last30days": "Last 30 days", "thisMonth": "This month", "lastMonth": "Last month", "custom": "Custom"}, "metrics": {"title": "Metrics", "all": "All", "performance": "Performance", "engagement": "Engagement", "conversion": "Conversion", "cost": "Cost"}}, "performanceOverview": "Performance Overview", "quickActions": "Quick Actions", "recentCampaigns": "Recent Campaigns", "conversions": "conversions", "actions": {"createCampaign": "Create Campaign", "createCampaignDesc": "Create new advertising campaign", "manageAudiences": "Manage Audiences", "manageAudiencesDesc": "Create and manage customer audiences", "viewAnalytics": "View Analytics", "viewAnalyticsDesc": "Track campaign performance", "adAccountSettings": "Account <PERSON><PERSON>", "adAccountSettingsDesc": "Manage ad account settings"}, "metrics": {"totalSpend": "Total Spend", "impressions": "Impressions", "clicks": "<PERSON>licks", "conversions": "Conversions", "avgCTR": "Average CTR", "avgCPC": "Average CPC", "avgROAS": "Average ROAS"}, "facebookAds": {"analytics": {"title": "Facebook Ads Analytics", "description": "Track performance and optimize advertising campaigns", "overview": "Performance Overview", "topCampaigns": "Top Performing Campaigns", "export": "Export Report", "chartPlaceholder": "Performance Chart", "chartDescription": "Detailed charts will be displayed here when integrated with chart library", "totalSpend": "Total Spend", "totalSpendDesc": "Compared to previous period", "impressions": "Impressions", "impressionsDesc": "Total ad impressions", "clicks": "<PERSON>licks", "clicksDesc": "Total clicks on ads", "ctr": "Click-through Rate (CTR)", "ctrDesc": "Clicks / Impressions", "cpc": "Cost per Click (CPC)", "cpcDesc": "Spend / Clicks", "conversions": "Conversions", "conversionsDesc": "Total conversions", "roas": "ROAS", "roasDesc": "Return on Ad Spend", "reach": "Reach", "reachDesc": "Number of people reached", "periods": {"1d": "Today", "7d": "Last 7 days", "30d": "Last 30 days", "90d": "Last 90 days", "custom": "Custom"}}, "metrics": {"spend": "Spend", "clicks": "<PERSON>licks"}}}, "seedingGroups": {"form": {"create": {"title": "Configure Seeding Group"}}}}