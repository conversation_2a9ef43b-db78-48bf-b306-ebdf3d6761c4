/**
 * Generic WebSocket Hook
 * React hook để quản lý WebSocket connection và events
 */

import { useEffect, useCallback, useRef } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { genericWebSocketService } from '../services/genericWebSocketService';
import { getGenericPageState } from '../services/genericApiService';
import { 
  GenericWidget, 
  GenericLayout, 
  AddWidgetEvent, 
  RemoveWidgetEvent, 
  UpdateLayoutEvent,
  SyncStateEvent 
} from '../types';

interface UseGenericWebSocketOptions {
  sessionId?: string;
  autoConnect?: boolean;
  onWidgetAdded?: (widget: GenericWidget) => void;
  onWidgetRemoved?: (widgetId: string) => void;
  onLayoutUpdated?: (layout: GenericLayout[]) => void;
  onStateSync?: (widgets: GenericWidget[], layout: GenericLayout[]) => void;
}

export const useGenericWebSocket = (options: UseGenericWebSocketOptions = {}) => {
  const {
    sessionId,
    autoConnect = true,
    onWidgetAdded,
    onWidgetRemoved,
    onLayoutUpdated,
    onStateSync,
  } = options;

  const queryClient = useQueryClient();
  const unsubscribeRefs = useRef<(() => void)[]>([]);

  // Query để lấy initial state
  const { data: initialState, isLoading } = useQuery({
    queryKey: ['generic-page-state', sessionId],
    queryFn: () => getGenericPageState(sessionId),
    enabled: !!sessionId,
    staleTime: 0, // Always fetch fresh data
  });

  // Connect to WebSocket
  const connect = useCallback(async () => {
    try {
      await genericWebSocketService.connect(sessionId);
      console.log('✅ Connected to Generic WebSocket');
    } catch (error) {
      console.error('❌ Failed to connect to Generic WebSocket:', error);
    }
  }, [sessionId]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    // Cleanup subscriptions
    unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
    unsubscribeRefs.current = [];
    
    genericWebSocketService.disconnect();
    console.log('🔌 Disconnected from Generic WebSocket');
  }, []);

  // WebSocket event handlers
  const handleAddWidget = useCallback((event: AddWidgetEvent) => {
    console.log('📥 Widget added:', event.payload.widget);
    
    // Update query cache
    queryClient.setQueryData(['generic-page-state', sessionId], (oldData: any) => {
      if (!oldData) return oldData;
      
      return {
        ...oldData,
        widgets: [...oldData.widgets, event.payload.widget],
      };
    });

    // Call callback
    onWidgetAdded?.(event.payload.widget);
  }, [queryClient, sessionId, onWidgetAdded]);

  const handleRemoveWidget = useCallback((event: RemoveWidgetEvent) => {
    console.log('📥 Widget removed:', event.payload.widgetId);
    
    // Update query cache
    queryClient.setQueryData(['generic-page-state', sessionId], (oldData: any) => {
      if (!oldData) return oldData;
      
      return {
        ...oldData,
        widgets: oldData.widgets.filter((w: GenericWidget) => w.id !== event.payload.widgetId),
      };
    });

    // Call callback
    onWidgetRemoved?.(event.payload.widgetId);
  }, [queryClient, sessionId, onWidgetRemoved]);

  const handleUpdateLayout = useCallback((event: UpdateLayoutEvent) => {
    console.log('📥 Layout updated:', event.payload.layout);
    
    // Update query cache
    queryClient.setQueryData(['generic-page-state', sessionId], (oldData: any) => {
      if (!oldData) return oldData;
      
      return {
        ...oldData,
        layout: event.payload.layout,
      };
    });

    // Call callback
    onLayoutUpdated?.(event.payload.layout);
  }, [queryClient, sessionId, onLayoutUpdated]);

  const handleSyncState = useCallback((event: SyncStateEvent) => {
    console.log('📥 State synced:', event.payload);
    
    // Update query cache
    queryClient.setQueryData(['generic-page-state', sessionId], (oldData: any) => ({
      ...oldData,
      widgets: event.payload.widgets,
      layout: event.payload.layout,
    }));

    // Call callback
    onStateSync?.(event.payload.widgets, event.payload.layout);
  }, [queryClient, sessionId, onStateSync]);

  // Setup WebSocket subscriptions
  useEffect(() => {
    if (!autoConnect || !sessionId) return;

    const setupSubscriptions = async () => {
      try {
        // Connect if not already connected
        if (!genericWebSocketService.getConnectionStatus()) {
          await connect();
        }

        // Subscribe to events
        const unsubscribeAddWidget = genericWebSocketService.onAddWidget(handleAddWidget);
        const unsubscribeRemoveWidget = genericWebSocketService.onRemoveWidget(handleRemoveWidget);
        const unsubscribeUpdateLayout = genericWebSocketService.onUpdateLayout(handleUpdateLayout);
        const unsubscribeSyncState = genericWebSocketService.onSyncState(handleSyncState);

        // Store unsubscribe functions
        unsubscribeRefs.current = [
          unsubscribeAddWidget,
          unsubscribeRemoveWidget,
          unsubscribeUpdateLayout,
          unsubscribeSyncState,
        ];

        console.log('✅ Generic WebSocket subscriptions setup');
      } catch (error) {
        console.error('❌ Failed to setup Generic WebSocket subscriptions:', error);
      }
    };

    setupSubscriptions();

    // Cleanup on unmount
    return () => {
      unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
      unsubscribeRefs.current = [];
    };
  }, [
    autoConnect,
    sessionId,
    connect,
    handleAddWidget,
    handleRemoveWidget,
    handleUpdateLayout,
    handleSyncState,
  ]);

  // WebSocket actions
  const addWidget = useCallback((widget: GenericWidget, position?: { x: number; y: number }) => {
    genericWebSocketService.addWidget(widget, position);
  }, []);

  const removeWidget = useCallback((widgetId: string) => {
    genericWebSocketService.removeWidget(widgetId);
  }, []);

  const updateLayout = useCallback((layout: GenericLayout[]) => {
    genericWebSocketService.updateLayout(layout);
  }, []);

  const requestSync = useCallback(() => {
    genericWebSocketService.requestSync();
  }, []);

  return {
    // Connection state
    isConnected: genericWebSocketService.getConnectionStatus(),
    sessionId: genericWebSocketService.getSessionId(),
    
    // Data
    widgets: initialState?.widgets || [],
    layout: initialState?.layout || [],
    isLoading,
    
    // Actions
    connect,
    disconnect,
    addWidget,
    removeWidget,
    updateLayout,
    requestSync,
  };
};
