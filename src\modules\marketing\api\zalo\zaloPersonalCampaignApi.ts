import { apiClient } from '@/shared/api';
import type {
  ZaloPersonalCampaignDto,
  ZaloPersonalCampaignQueryDto,
  ZaloPersonalCampaignResponseDto,
  CreateZaloPersonalCampaignDto,
  UpdateZaloPersonalCampaignDto,
  CreateCrawlFriendsListCampaignDto,
  CreateCrawlGroupsCampaignDto,
} from '../../types/zaloPersonalCampaign';

/**
 * API endpoints cho Zalo Personal Campaigns
 */
const ENDPOINTS = {
  LIST: '/marketing/user/zalo-personal/campaigns',
  DETAIL: (id: string) => `/marketing/user/zalo-personal/campaigns/${id}`,
  CREATE: '/marketing/user/zalo-personal/campaigns',
  UPDATE: (id: string) => `/marketing/user/zalo-personal/campaigns/${id}`,
  DELETE: '/marketing/user/zalo-personal/campaigns',
  START: (id: string) => `/marketing/user/zalo-personal/campaigns/${id}/start`,
  PAUSE: (id: string) => `/marketing/user/zalo-personal/campaigns/${id}/pause`,
  STOP: (id: string) => `/marketing/user/zalo-personal/campaigns/${id}/stop`,
  STATS: (id: string) => `/marketing/user/zalo-personal/campaigns/${id}/stats`,
} as const;

/**
 * Lấy danh sách chiến dịch Zalo Personal
 */
export const getZaloPersonalCampaigns = async (
  params?: ZaloPersonalCampaignQueryDto
): Promise<ZaloPersonalCampaignResponseDto> => {
  const response = await apiClient.get(ENDPOINTS.LIST, { params });
  return response as ZaloPersonalCampaignResponseDto;
};

/**
 * Lấy chi tiết chiến dịch Zalo Personal
 */
export const getZaloPersonalCampaignDetail = async (
  id: string
): Promise<{ result: ZaloPersonalCampaignDto }> => {
  const response = await apiClient.get(ENDPOINTS.DETAIL(id));
  return response as { result: ZaloPersonalCampaignDto };
};

/**
 * Tạo chiến dịch Zalo Personal mới
 */
export const createZaloPersonalCampaign = async (
  data: CreateZaloPersonalCampaignDto
): Promise<{ result: ZaloPersonalCampaignDto }> => {
  const response = await apiClient.post(ENDPOINTS.CREATE, data);
  return response as { result: ZaloPersonalCampaignDto };
};

/**
 * Tạo chiến dịch Crawl Friends List - API specification
 */
export const createCrawlFriendsCampaign = async (
  data: CreateCrawlFriendsListCampaignDto
): Promise<{ result: ZaloPersonalCampaignDto }> => {
  const response = await apiClient.post(ENDPOINTS.CREATE, data);
  return response as { result: ZaloPersonalCampaignDto };
};

/**
 * Tạo chiến dịch Crawl Groups - API specification
 */
export const createCrawlGroupsCampaign = async (
  data: CreateCrawlGroupsCampaignDto
): Promise<{ result: ZaloPersonalCampaignDto }> => {
  const response = await apiClient.post(ENDPOINTS.CREATE, data);
  return response as { result: ZaloPersonalCampaignDto };
};

/**
 * Cập nhật chiến dịch Zalo Personal
 */
export const updateZaloPersonalCampaign = async (
  id: string,
  data: UpdateZaloPersonalCampaignDto
): Promise<{ result: ZaloPersonalCampaignDto }> => {
  const response = await apiClient.put(ENDPOINTS.UPDATE(id), data);
  return response as { result: ZaloPersonalCampaignDto };
};

/**
 * Xóa chiến dịch Zalo Personal
 */
export const deleteZaloPersonalCampaigns = async (
  ids: string[]
): Promise<{ result: { deletedCount: number } }> => {
  const response = await apiClient.delete(ENDPOINTS.DELETE, {
    data: { ids }
  });
  return (response as any).result;
};

/**
 * Bắt đầu chạy chiến dịch
 */
export const startZaloPersonalCampaign = async (
  id: string
): Promise<{ success: boolean }> => {
  const response = await apiClient.post(ENDPOINTS.START(id));
  return (response as any).result;
};

/**
 * Tạm dừng chiến dịch
 */
export const pauseZaloPersonalCampaign = async (
  id: string
): Promise<{ success: boolean }> => {
  const response = await apiClient.post(ENDPOINTS.PAUSE(id));
  return (response as any).result;
};

/**
 * Dừng chiến dịch
 */
export const stopZaloPersonalCampaign = async (
  id: string
): Promise<{ success: boolean }> => {
  const response = await apiClient.post(ENDPOINTS.STOP(id));
  return (response as any).result;
};

// Note: Stats are now included in the main campaign object, no separate endpoint needed
