import React, { useRef, useEffect, useState } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
} from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  onClickOutside?: () => void;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Nhập nội dung...',
  className = '',
  onClickOutside,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);

  console.log('RichTextEditor rendered with:', {
    value,
    placeholder,
    hasOnClickOutside: !!onClickOutside,
  });

  const colors = [
    '#000000',
    '#333333',
    '#666666',
    '#999999',
    '#cccccc',
    '#ffffff',
    '#ff0000',
    '#ff6600',
    '#ffcc00',
    '#33cc00',
    '#0066cc',
    '#6600cc',
    '#ff3366',
    '#ff9933',
    '#ffff33',
    '#66ff33',
    '#3366ff',
    '#9933ff',
  ];

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value || '';

      // Ensure contentEditable is set
      editorRef.current.contentEditable = 'true';

      // Focus and position cursor for inline editing
      if (onClickOutside) {
        setTimeout(() => {
          if (editorRef.current) {
            // Ensure editor is properly configured
            editorRef.current.contentEditable = 'true';
            editorRef.current.focus();

            // Position cursor at the end
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(editorRef.current);
            range.collapse(false);
            selection?.removeAllRanges();
            selection?.addRange(range);

            console.log('Editor setup complete, selection:', {
              rangeCount: selection?.rangeCount,
              isCollapsed: selection?.isCollapsed,
            });
          }
        }, 150);
      }
    }
  }, [value, onClickOutside]);

  useEffect(() => {
    if (!onClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (editorRef.current && !editorRef.current.contains(target)) {
        // Don't close if clicking on toolbar or related elements
        const element = target instanceof Element ? target : null;
        const isToolbarClick =
          element &&
          (element.closest('.rich-text-editor') ||
            element.closest('button') ||
            element.closest('select') ||
            element.closest('option') ||
            element.tagName === 'BUTTON' ||
            element.tagName === 'SELECT');

        if (!isToolbarClick) {
          onClickOutside();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClickOutside]);

  const handleCommand = (command: string, value?: string) => {
    if (!editorRef.current) return;

    console.log('RichTextEditor - Executing command:', command, 'with value:', value);

    // Focus editor and ensure it's ready
    editorRef.current.focus();

    try {
      let success = false;
      const selection = window.getSelection();

      console.log('Current selection:', {
        rangeCount: selection?.rangeCount,
        isCollapsed: selection?.isCollapsed,
        selectedText: selection?.toString(),
      });

      // Different approach based on command type
      switch (command) {
        case 'bold':
        case 'italic':
        case 'underline': {
          // For formatting commands, ensure we have text selected or create a selection
          if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
            // If no selection, select all content
            const range = document.createRange();
            range.selectNodeContents(editorRef.current);
            selection?.removeAllRanges();
            selection?.addRange(range);
          }

          // Use direct execCommand
          success = document.execCommand(command, false);
          break;
        }

        case 'justifyLeft':
        case 'justifyCenter':
        case 'justifyRight': {
          // Alignment commands work on the entire element
          success = document.execCommand(command, false);
          break;
        }

        case 'insertUnorderedList': {
          // List command
          success = document.execCommand('insertUnorderedList', false);
          break;
        }

        case 'fontSize': {
          // Font size with better handling
          if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
            // Select all if no selection
            const range = document.createRange();
            range.selectNodeContents(editorRef.current);
            selection?.removeAllRanges();
            selection?.addRange(range);
          }

          // Try different approaches for font size
          success = document.execCommand('fontSize', false, value || '3');

          if (!success) {
            // Fallback: wrap in span with style
            try {
              const range = selection?.getRangeAt(0);
              if (range) {
                const span = document.createElement('span');
                const sizeMap: { [key: string]: string } = {
                  '1': '10px',
                  '2': '13px',
                  '3': '16px',
                  '4': '18px',
                  '5': '24px',
                  '6': '32px',
                  '7': '48px',
                };
                span.style.fontSize = sizeMap[value || '3'] || '16px';

                try {
                  range.surroundContents(span);
                  success = true;
                } catch (e) {
                  console.log('Range:', e);
                  // If surroundContents fails, try different approach
                  const contents = range.extractContents();
                  span.appendChild(contents);
                  range.insertNode(span);
                  success = true;
                }
              }
            } catch (e) {
              console.warn('Font size fallback failed:', e);
            }
          }
          break;
        }

        case 'foreColor': {
          // Color with better handling
          if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
            // Select all if no selection
            const range = document.createRange();
            range.selectNodeContents(editorRef.current);
            selection?.removeAllRanges();
            selection?.addRange(range);
          }

          success = document.execCommand('foreColor', false, value);
          break;
        }

        default: {
          success = document.execCommand(command, false, value);
          break;
        }
      }

      console.log(`RichTextEditor - Command ${command} executed:`, success);

      if (!success) {
        console.warn(`RichTextEditor - Command ${command} failed`);
      }

      // Trigger onChange to update content
      if (success) {
        setTimeout(() => {
          if (editorRef.current) {
            onChange(editorRef.current.innerHTML);
          }
        }, 10);
      }

      // Keep focus on editor
      editorRef.current.focus();
    } catch (error) {
      console.error('RichTextEditor - execCommand error:', error);
    }
  };

  const handleInput = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      console.log('Content changed:', content);
      onChange(content);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    console.log('Key pressed:', e.key, 'ctrlKey:', e.ctrlKey);

    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'b':
          e.preventDefault();
          handleCommand('bold');
          break;
        case 'i':
          e.preventDefault();
          handleCommand('italic');
          break;
        case 'u':
          e.preventDefault();
          handleCommand('underline');
          break;
        case 'v':
          // Allow paste
          console.log('Paste operation');
          return;
        case 'c':
          // Allow copy
          console.log('Copy operation');
          return;
        case 'x':
          // Allow cut
          console.log('Cut operation');
          return;
        case 'a':
          // Allow select all
          console.log('Select all operation');
          return;
      }
      return;
    }

    // Allow all normal editing keys
    const editingKeys = [
      'Delete',
      'Backspace',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Home',
      'End',
      'Enter',
      'Tab',
      'Space',
    ];

    // Allow normal character input and editing keys
    if (editingKeys.includes(e.key) || (e.key.length === 1 && !e.ctrlKey && !e.metaKey)) {
      // Let browser handle normal text editing
      console.log('Allowing key:', e.key);
      return;
    }
  };

  return (
    <div
      className={`rich-text-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('bold');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Bold (Ctrl+B)"
        >
          <Bold size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('italic');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Italic (Ctrl+I)"
        >
          <Italic size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('underline');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Underline (Ctrl+U)"
        >
          <Underline size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyLeft');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Left"
        >
          <AlignLeft size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyCenter');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Center"
        >
          <AlignCenter size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('justifyRight');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Align Right"
        >
          <AlignRight size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
          onMouseDown={e => {
            e.preventDefault();
            e.stopPropagation();
            handleCommand('insertUnorderedList');
          }}
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
          title="Bullet List"
        >
          <List size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <select
          className="text-xs border-0 bg-transparent text-gray-700 dark:text-gray-300"
          onChange={e => {
            e.stopPropagation();
            handleCommand('fontSize', e.target.value);
          }}
          onMouseDown={e => e.stopPropagation()}
          onClick={e => e.stopPropagation()}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>

        <div className="relative">
          <button
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 active:bg-blue-200 dark:active:bg-blue-700"
            onMouseDown={e => {
              e.preventDefault();
              e.stopPropagation();
              setShowColorPicker(!showColorPicker);
            }}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
            }}
            title="Text Color"
          >
            <Palette size={14} />
          </button>

          {showColorPicker && (
            <div className="absolute top-8 left-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-2 z-10">
              <div className="grid grid-cols-6 gap-1">
                {colors.map(color => (
                  <button
                    key={color}
                    className="w-5 h-5 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onMouseDown={e => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleCommand('foreColor', color);
                      setShowColorPicker(false);
                    }}
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[80px] outline-none text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
        contentEditable="true"
        onInput={handleInput}
        suppressContentEditableWarning={true}
        spellCheck={false}
        role="textbox"
        tabIndex={0}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
        }}
        data-placeholder={placeholder}
        onKeyDown={handleKeyDown}
        onMouseUp={() => {
          // Log selection when user selects text
          const selection = window.getSelection();
          console.log('Text selected:', {
            rangeCount: selection?.rangeCount,
            isCollapsed: selection?.isCollapsed,
            selectedText: selection?.toString(),
          });
        }}
        onFocus={() => {
          console.log('Editor focused');
        }}
      />

      <style
        dangerouslySetInnerHTML={{
          __html: `
          .rich-text-editor [contenteditable]:empty:before {
            content: attr(data-placeholder);
            color: #9ca3af;
            pointer-events: none;
          }
          .dark .rich-text-editor [contenteditable]:empty:before {
            color: #6b7280;
          }
        `,
        }}
      />
    </div>
  );
};

export default RichTextEditor;
