import React, { useRef, useEffect, useState } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Palette,
} from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Nhập nội dung...',
  className = '',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);

  const colors = [
    '#000000',
    '#333333',
    '#666666',
    '#999999',
    '#cccccc',
    '#ffffff',
    '#ff0000',
    '#ff6600',
    '#ffcc00',
    '#33cc00',
    '#0066cc',
    '#6600cc',
    '#ff3366',
    '#ff9933',
    '#ffff33',
    '#66ff33',
    '#3366ff',
    '#9933ff',
  ];

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value || '';
    }
  }, [value]);

  const handleCommand = (command: string, value?: string) => {
    if (!editorRef.current) return;

    editorRef.current.focus();

    // Ensure we have a selection
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      const range = document.createRange();
      range.selectNodeContents(editorRef.current);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    try {
      document.execCommand(command, false, value);
      editorRef.current.focus();
    } catch (error) {
      console.warn('Command failed:', command, error);
    }
  };

  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  return (
    <div
      className={`rich-text-editor border border-gray-300 dark:border-gray-600 rounded-md ${className}`}
    >
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('bold');
          }}
          title="Bold"
        >
          <Bold size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('italic');
          }}
          title="Italic"
        >
          <Italic size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('underline');
          }}
          title="Underline"
        >
          <Underline size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('justifyLeft');
          }}
          title="Align Left"
        >
          <AlignLeft size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('justifyCenter');
          }}
          title="Align Center"
        >
          <AlignCenter size={14} />
        </button>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('justifyRight');
          }}
          title="Align Right"
        >
          <AlignRight size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <button
          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
          onMouseDown={e => {
            e.preventDefault();
            handleCommand('insertUnorderedList');
          }}
          title="Bullet List"
        >
          <List size={14} />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>

        <select
          className="text-xs border-0 bg-transparent text-gray-700 dark:text-gray-300"
          onChange={e => handleCommand('fontSize', e.target.value)}
          onMouseDown={e => e.stopPropagation()}
          defaultValue="3"
        >
          <option value="1">8px</option>
          <option value="2">10px</option>
          <option value="3">12px</option>
          <option value="4">14px</option>
          <option value="5">18px</option>
          <option value="6">24px</option>
          <option value="7">36px</option>
        </select>

        <div className="relative">
          <button
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300"
            onMouseDown={e => {
              e.preventDefault();
              setShowColorPicker(!showColorPicker);
            }}
            title="Text Color"
          >
            <Palette size={14} />
          </button>

          {showColorPicker && (
            <div className="absolute top-8 left-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-2 z-10">
              <div className="grid grid-cols-6 gap-1">
                {colors.map(color => (
                  <button
                    key={color}
                    className="w-5 h-5 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onMouseDown={e => {
                      e.preventDefault();
                      handleCommand('foreColor', color);
                      setShowColorPicker(false);
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="p-3 min-h-[80px] outline-none text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
        contentEditable
        onInput={handleInput}
        suppressContentEditableWarning={true}
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
        }}
        data-placeholder={placeholder}
      />

      <style
        dangerouslySetInnerHTML={{
          __html: `
          .rich-text-editor [contenteditable]:empty:before {
            content: attr(data-placeholder);
            color: #9ca3af;
            pointer-events: none;
          }
          .dark .rich-text-editor [contenteditable]:empty:before {
            color: #6b7280;
          }
        `,
        }}
      />
    </div>
  );
};

export default RichTextEditor;
