import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Chip,
  Typography,
  ActionMenu,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useSmartNotification } from '@/shared';
import { useZaloPersonalCampaigns, useDeleteZaloPersonalCampaigns } from '../../hooks/zalo/useZaloPersonalCampaigns';
import CreateZaloPersonalCampaignForm from '../../components/zalo/CreateZaloPersonalCampaignForm';
import {
  ZaloPersonalCampaignDto,
  ZaloPersonalCampaignStatus,
  ZaloPersonalCampaignQueryDto,
  getStatusLabel,
  getCampaignTypeLabel,
} from '../../types/zaloPersonalCampaign';
import type { TableColumn } from '@/shared/components/common/Table/types';
import { formatTimestamp } from '@/shared/utils/date';

const PersonalCampaignsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { showNotification } = useSmartNotification();
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Mutations
  const deleteMutation = useDeleteZaloPersonalCampaigns();

  // Table columns
  const columns = useMemo((): TableColumn<ZaloPersonalCampaignDto>[] => [
    {
      key: 'name',
      title: t('marketing:zalo.personalCampaigns.table.name', 'Tên chiến dịch'),
      sortable: true,
      render: (_, record) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {record.name}
          </Typography>
          {record.description && (
            <Typography variant="caption" className="text-muted">
              {record.description}
            </Typography>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      title: t('marketing:zalo.personalCampaigns.table.type', 'Loại chiến dịch'),
      render: (_, record) => (
        <Typography variant="body2" className="capitalize">
          {getCampaignTypeLabel(record.type, t)}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('marketing:zalo.personalCampaigns.table.status', 'Trạng thái'),
      render: (_, record) => {
        const getVariantByStatus = (status: ZaloPersonalCampaignStatus) => {
          switch (status) {
            case ZaloPersonalCampaignStatus.DRAFT:
              return 'default';
            case ZaloPersonalCampaignStatus.SCHEDULED:
              return 'warning';
            case ZaloPersonalCampaignStatus.ACTIVE:
              return 'success';
            case ZaloPersonalCampaignStatus.PAUSED:
              return 'warning';
            case ZaloPersonalCampaignStatus.COMPLETED:
              return 'info';
            case ZaloPersonalCampaignStatus.FAILED:
              return 'danger';
            default:
              return 'default';
          }
        };

        return (
          <Chip
            variant={getVariantByStatus(record.status)}
            size="sm"
          >
            {getStatusLabel(record.status, t)}
          </Chip>
        );
      },
    },
    {
      key: 'progress',
      title: t('marketing:zalo.personalCampaigns.table.progress', 'Tiến độ'),
      render: (_, record) => (
        <div className="text-sm">
          <div className="flex justify-between">
            <span>{t('marketing:zalo.personalCampaigns.table.processed', 'Đã xử lý')}:</span>
            <span className="font-medium">{record.successCount + record.failureCount}/{record.totalRecipients}</span>
          </div>
          <div className="flex justify-between text-green-600">
            <span>{t('marketing:zalo.personalCampaigns.table.success', 'Thành công')}:</span>
            <span className="font-medium">{record.successCount}</span>
          </div>
          <div className="flex justify-between text-red-600">
            <span>{t('marketing:zalo.personalCampaigns.table.failed', 'Thất bại')}:</span>
            <span className="font-medium">{record.failureCount}</span>
          </div>
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: t('marketing:zalo.personalCampaigns.table.createdAt', 'Ngày tạo'),
      sortable: true,
      render: (_, record) => {
        return formatTimestamp(record.createdAt);
      },
    },
    {
      key: 'actions',
      title: t('common:actions', 'Thao tác'),
      width: '80px',
      render: (_, record) => {
        const actionItems = [
          {
            id: 'view',
            label: t('common:view', 'Xem chi tiết'),
            icon: 'eye',
            onClick: () => handleView(record),
          },
          {
            id: 'edit',
            label: t('common:edit', 'Chỉnh sửa'),
            icon: 'edit',
            onClick: () => handleEdit(record),
            disabled: record.status === ZaloPersonalCampaignStatus.ACTIVE,
          },
          {
            id: 'start',
            label: t('marketing:zalo.personalCampaigns.actions.start', 'Bắt đầu'),
            icon: 'play',
            onClick: () => handleStart(record),
            disabled: record.status === ZaloPersonalCampaignStatus.ACTIVE,
          },
          {
            id: 'pause',
            label: t('marketing:zalo.personalCampaigns.actions.pause', 'Tạm dừng'),
            icon: 'pause',
            onClick: () => handlePause(record),
            disabled: record.status !== ZaloPersonalCampaignStatus.ACTIVE,
          },
          {
            id: 'duplicate',
            label: t('common:duplicate', 'Nhân bản'),
            icon: 'copy',
            onClick: () => handleDuplicate(record),
          },
          {
            id: 'delete',
            label: t('common:delete', 'Xóa'),
            icon: 'trash',
            onClick: () => handleDelete(record),
            variant: 'danger',
            disabled: record.status === ZaloPersonalCampaignStatus.ACTIVE,
          },
        ];

        return <ActionMenu items={actionItems} />;
      },
    },
  ], [t]);

  // Data table configuration
  const dataTable = useDataTable(
    useDataTableConfig({
      columns: columns as any,
      createQueryParams: (params: any) => {
        const queryParams: ZaloPersonalCampaignQueryDto = {
          page: params.page,
          limit: params.pageSize,
        };

        if (params.searchTerm) {
          queryParams.search = params.searchTerm;
        }

        if (params.sortBy) {
          queryParams.sortBy = params.sortBy;
        }

        if (params.sortDirection) {
          queryParams.sortDirection = params.sortDirection === 'ASC' ? 'asc' : 'desc';
        }

        return queryParams;
      },
    })
  );

  // Data fetching
  const { data, isLoading, refetch } = useZaloPersonalCampaigns(dataTable.queryParams as any);

  // Event handlers
  const handleAdd = () => {
    setShowCreateForm(true);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    refetch();
    showNotification('success', t('marketing:zalo.personalCampaigns.messages.createSuccess', 'Tạo chiến dịch thành công'));
  };

  const handleView = (campaign: ZaloPersonalCampaignDto) => {
    // TODO: Navigate to campaign detail page
    showNotification('info', `Xem chi tiết chiến dịch: ${campaign.name}`);
  };

  const handleEdit = (campaign: ZaloPersonalCampaignDto) => {
    // TODO: Navigate to campaign edit page
    showNotification('info', `Chỉnh sửa chiến dịch: ${campaign.name}`);
  };

  const handleStart = (campaign: ZaloPersonalCampaignDto) => {
    // TODO: Implement start campaign
    showNotification('info', `Bắt đầu chiến dịch: ${campaign.name}`);
  };

  const handlePause = (campaign: ZaloPersonalCampaignDto) => {
    // TODO: Implement pause campaign
    showNotification('info', `Tạm dừng chiến dịch: ${campaign.name}`);
  };

  const handleDuplicate = (campaign: ZaloPersonalCampaignDto) => {
    // TODO: Implement duplicate campaign
    showNotification('info', `Nhân bản chiến dịch: ${campaign.name}`);
  };

  const handleDelete = async (campaign: ZaloPersonalCampaignDto) => {
    if (window.confirm(`Bạn có chắc chắn muốn xóa chiến dịch "${campaign.name}"?`)) {
      try {
        await deleteMutation.mutateAsync([campaign.id.toString()]);
        showNotification('success', `Đã xóa chiến dịch: ${campaign.name}`);
        refetch();
      } catch (error) {
        showNotification('error', 'Có lỗi xảy ra khi xóa chiến dịch');
      }
    }
  };

  const handleReload = () => {
    refetch();
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Create Form SlideIn */}
      <SlideInForm isVisible={showCreateForm}>
        <CreateZaloPersonalCampaignForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateForm(false)}
        />
      </SlideInForm>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'refresh-cw',
            tooltip: t('common:reload', 'Tải lại dữ liệu'),
            variant: 'secondary',
            onClick: handleReload,
            className: 'text-blue-500',
          },
        ]}
      />

      {/* Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.result?.items || []}
          loading={isLoading}
          pagination={{
            current: data?.result?.meta?.currentPage || 1,
            pageSize: data?.result?.meta?.itemsPerPage || 10,
            total: data?.result?.meta?.totalItems || 0,
            showSizeChanger: true,
            onChange: dataTable.tableData.handlePageChange,
          }}
        />
      </Card>
    </div>
  );
};

export default PersonalCampaignsPage;
